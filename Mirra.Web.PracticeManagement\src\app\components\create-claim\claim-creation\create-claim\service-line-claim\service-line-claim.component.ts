
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

import { _MatTabGroupBase } from '@angular/material/tabs';
import { QualifierDataByType } from 'src/app/classmodels/ResponseModel/ClaimForm/QualifierDataByType';
import { MemberResult } from 'src/app/classmodels/ResponseModel/Member/MemberResult';
import { submitValidateAllFields } from 'src/app/common/form.validators';
import { ServiceLine, ServiceLineSelectionModel } from 'src/app/models/ClaimForm/ServiceLineSelectionModel';
import { ServiceLineProfessional837sModel } from 'src/app/models/ClaimForm/claim.create.model';
import { ClaimInfoModel } from 'src/app/models/Providers/ClaimInfoModel';
import { AllCPTCode, ValidateCPTRequest } from 'src/app/classmodels/ResponseModel/ClaimForm/AllCPTCode';
import { GetAllCPTCodeService } from 'src/app/services/ClaimForm/get-all-cptcode.service';
import { AllPlaceOfServicesService } from 'src/app/services/ClaimForm/all-place-of-services.service';
import { AllPlaceOfServices } from 'src/app/classmodels/ResponseModel/ClaimForm/AllPlaceOfServices';
import { SubjectService } from 'src/app/shared/services/subject.service';
import { distinctUntilChanged, first } from 'rxjs';
import * as moment from 'moment';
import Swal from 'sweetalert2';
import * as JSLZString from 'lz-string';
import { AllICDCode } from 'src/app/classmodels/ResponseModel/ClaimForm/AllICDCode';
import { MatDialog } from '@angular/material/dialog';
import { CptViewHistoryComponent } from '../../../../../modals/cpt-view-history/cpt-view-history.component';
import { numberWith3DecimalValidator,modifierValidator } from 'src/app/common/form.validators';
import { CacheService } from 'src/app/services/cache-service/cache.service';
import { LocalStorageKey, PriceCost } from 'src/app/shared/constant/constatnt';
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: 'app-service-line-claim',
  templateUrl: './service-line-claim.component.html',
  styleUrls: ['./service-line-claim.component.scss']
})
export class ServiceLineClaimComponent implements OnInit {
  serviceLineInfo: FormGroup;
  @Input() serviceLineInfos: ServiceLine;
  todayDate: Date = new Date();
  minDate = new Date('2015-10-01');
  @Input() memberProfile: MemberResult;
  @Input() claimFormData: ClaimInfoModel;
  @Output() diagnosisPointerBlur: EventEmitter<any> = new EventEmitter();
  @Output() dosFromChanged: EventEmitter<any> = new EventEmitter();
  @Output() calculateTotal: EventEmitter<any> = new EventEmitter();
  @Input() allPlaceOfServices: AllPlaceOfServices[] = [];

  allICDCode: AllICDCode[] = [{ text: "abcdef", value: "abcdef", addDate: '', termDate: '' }];
  isControl: boolean = true;
  nDcQualifierQty: QualifierDataByType[] = [];
  nDcQualifier: QualifierDataByType[] = [];
  allCPTCode: AllCPTCode[] = [];
  allCPTCharges: any = [];
  viewHistoryCPTList:any[]=[];
  cmg: any[] = ["Yes", "No"]
  epsdt: any[] = ["Yes", "No"];
  isSubmited: boolean = false;
  constructor(private serviceLineform: FormBuilder,
    private placeOfService: AllPlaceOfServicesService,
    private subjectService: SubjectService,
    private cptservice: GetAllCPTCodeService,
    private dialog: MatDialog,
    private cacheService:CacheService,
    private cdRef: ChangeDetectorRef, 
    private readonly spinner:NgxSpinnerService) {
    this.subjectService.getBillingProviderNPIChange().subscribe(res => {
      if (res || res == '') {
        this.changeRenderProviderNo(res);
        this.subjectService.resetServicelineProviderNPIChange();
      }
    })
  }

  ngOnInit(): void {
    this.spinner.show('child');
    this.cdRef.detectChanges();
   
    setTimeout(() => {
      this.patchValue(); // Heavy synchronous form patch
    this.nDcQualifier = this.claimFormData.ndcQualifierDataByType;
    this.nDcQualifierQty = this.claimFormData.ndcQtyQualifierDataByType;

    if (this.claimFormData.isAddClaim) {
      this.serviceLine.controls.forEach(control => {
        control.get('proceduceC')?.setValue('HC', { emitEvent: false });
      });
    }
    this.spinner.hide('child'); 
    }, 1000); 
    
  }

  createForm() {
    this.serviceLineInfo = this.serviceLineform.group({
      showNDC: new FormControl('No'),

      serviceLines: this.serviceLineform.array([]),
    })
    return this.serviceLineInfo;
  }
  get serviceLine() {
    return this.serviceLineInfo.get('serviceLines') as FormArray;
  }
  get f() {
    return this.serviceLineInfo.controls;
  }
  // allPlaceOfServices: AllPlaceOfServices[] = [];
  placeOfServiceData() {
    if (!!this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices)) {
          this.allPlaceOfServices = this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices);
        }
        else{
          this.placeOfService.fetchchAllPlaceOfServices().subscribe((res: AllPlaceOfServices[]) => {
            this.allPlaceOfServices = res;
          })
        }
  }
  patchValue() {
    if (this.claimFormData.isAddClaim) {
      const renderingNPI = this.claimFormData?.provider?.renderingProvider?.providerNPI;
      const posCode = this.claimFormData?.placeOfServiceCode;
      const dOSFrom = new Date(this.memberProfile?.dOSFrom);
      const dOSTo = this.memberProfile?.dOSTo;
  
      const groups = this.serviceLineInfos?.serviceLines.map((ele: ServiceLineSelectionModel) => {
        const charges = Number(ele.charges) || 0;
        const units = Number(ele.daysunit) || 0;
        const total = (units * charges).toFixed(2);
  
        return this.serviceLineform.group({
          dateServiceFrom: [{ value: dOSFrom, disabled: true }] ,
          dateServiceTo:[{ value: dOSTo, disabled: true }]   ,
          locationOfService: [{ value: posCode, disabled: false }],
          emg: [{ value: ele.eMG, disabled: false }],
          cpt: [{ value: ele.cptCode, disabled: false }, Validators.required],
          desc: [{ value: ele.desc }],
          m1: [{ value: ele.m1, disabled: false }],
          m2: [{ value: ele.m2, disabled: false }],
          m3: [{ value: ele.m3, disabled: false }],
          m4: [{ value: ele.m4, disabled: false }],
          diagnosispointer1: [{ value: Number(ele.d1).toString(), disabled: false }],
          diagnosispointer2: [{ value: ele.d2 ? Number(ele.d2).toString() : '', disabled: false }],
          diagnosispointer3: [{ value: ele.d3 ? Number(ele.d3).toString() : '', disabled: false }],
          diagnosispointer4: [{ value: ele.d4 ? Number(ele.d4).toString() : '', disabled: false }],
          unitCharges: [{ value: ele.charges, disabled: false }, [Validators.required]],
          dayUnitChanges: [{ value: ele.daysunit, disabled: false },[ Validators.required, Validators.max(99999999), Validators.min(1)]],
          total: [{ value: total, disabled: true }],
          ePSDT: [{ value: ele.ePSDT, disabled: false }],
          jRenderingProviderId: [{ value: renderingNPI, disabled: true }],
          proceduceC: '',
          proceduceCount: '',
          ndcUnitPrice: '',
          lineNote: '',
          ndcQtyQual: null,
          anesStart: '',
          ndcQty: '',
          anesStop1: '',
          anesStop2: '',
          anesStop3: '',
          ndcQual: null,
          ndcCode: ''
        })
      });
  
      groups.forEach(group => this.serviceLine.push(group))
  
    }
    
    if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim) {
      
        this.fetchCPTData();
        this.fetchchAllCPTCharges();
        this.placeOfServiceData();
    }

    // View Claim Show NDC radio button enabled 
    if (this.claimFormData.isViewClaim) {
      this.patchServiceLineData();
      this.serviceLineInfo.disable();
      this.serviceLineInfo.controls["showNDC"].enable();
    }
    this.dateValidators();
  }
  

  showNDC() {
    if (this.claimFormData.isAddClaim) {
      for (let index = 0; index < this.serviceLineInfos?.serviceLines.length; index++) {
        this.serviceLine.at(index).get('proceduceC').patchValue('HC');
        this.serviceLine.at(index).get('proceduceCount').patchValue(index + 1);
      }
    }
  }
  hideNDC() {

  }

  setValidator() {
    this.isSubmited
    if (this.serviceLineInfo.invalid) {
      submitValidateAllFields.validateAllFields(this.serviceLineInfo);
      return false;
    }
    return true;
  }
  invalidCptCode: string[] = [];
  
  OnCPTCodeChange(codeValue: any, index: any) {
  let dayUnitChanges = this.serviceLine.at(index).get('dayUnitChanges').value ? this.serviceLine.at(index).get('dayUnitChanges').value:'1';
    let unitCharges = this.serviceLine.at(index).get('unitCharges').value;
    if (codeValue) {
      let cptCharge = this.allCPTCharges.filter(item => item.code == codeValue)[0]?.charge;
       if(!!cptCharge){
        unitCharges =cptCharge;
       }
       else{
        unitCharges = (this.claimFormData.claimViewModel?.payerId === "41212" || this.claimFormData.claimViewModel?.payerId === "20133" || this.claimFormData.claimViewModel?.payerId === "59354") ? 
        PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;
       }
     
      let cptDescription = this.cptCodes.find(item => item.cpt == codeValue).description;
      this.serviceLine.at(index).patchValue({
        dayUnitChanges: (dayUnitChanges),
        total: (dayUnitChanges * unitCharges).toFixed(2),
        desc: {value : cptDescription},
        proceduceC:'HC' ,
        unitCharges: Number.parseFloat(unitCharges).toFixed(2)
      })

      this.invalidCptCode.forEach(element => {
        if (element === codeValue) {
          this.serviceLine.at(index).get('cpt').setErrors({ invalidCpt: true })
        }
      });

    }
    else {
      this.serviceLine.at(index).patchValue({
        dayUnitChanges: null,
        total: (0 * unitCharges).toFixed(2)
      })
    }

    this.calculateTotal.emit();
    this.validateCPTCode();
  }

  fetchchAllCPTCharges() {
    if (this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges)) {
      this.allCPTCharges = this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges);
      return;
    } 
    this.cptservice.fetchchAllCPTCharges().pipe(first()).subscribe(response => {                           //next() callback
      this.allCPTCharges = response.content;
    });
  }

  validateForm() {

    for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {
      if (this.serviceLine.at(index).get('jRenderingProviderId').value.length != 10) {
        this.serviceLine.at(this.serviceLineInfo.controls['serviceLines'].value.length - 1).get('jRenderingProviderId').setErrors({ maxlength: true });
      }
      this.checkDateValidations(index, 'dateServiceFrom');
      this.checkDateFromValidation(index, 'dateServiceTo');
      this.ndcQualChange(index);
      this.changeValuesToMarkValidation(index);
      this.validateCPTCode();
    }

    return this.setValidator();

  }
  
  changeQual(e, index) {

    if (e) {
      if (!this.serviceLine.at(index).get('ndcCode').value)
        this.serviceLine.at(index).get('ndcCode').setErrors({ required: true });
      if (!this.serviceLine.at(index).get('ndcQty').value)
        this.serviceLine.at(index).get('ndcQty').setErrors({ required: true });
      if (!this.serviceLine.at(index).get('ndcQual').value)
        this.serviceLine.at(index).get('ndcQual').setErrors({ required: true });
    } else {
      this.serviceLine.at(index).get('ndcCode').setErrors(null);
      this.serviceLine.at(index).get('ndcQty').setErrors(null);
      this.serviceLine.at(index).get('ndcQual').setErrors(null);
    }
    this.changeValuesToMarkValidation(index);
  }
  changeValuesToMarkValidation(index) {
    let fields = ['ndcCode', 'ndcQty', 'ndcQual', 'ndcQtyQual'];
    let validatorToBeApplied = false;
    for (const field of fields) {
      validatorToBeApplied = validatorToBeApplied || !!this.serviceLine.at(index).get(field).value;
    }
    if (validatorToBeApplied) {
      for (const field of fields) {
        if (!(!!this.serviceLine.at(index).get(field).value)) {
          this.serviceLine.at(index).get(field).setErrors({ required: true });
          this.serviceLine.at(index).get(field).setValidators(Validators.required);
        }
      }
    } else {
      for (const field of fields) {
        this.serviceLine.at(index).get(field).removeValidators(Validators.required);
        if (this.serviceLine.at(index).get(field).hasError('required')) {
          delete this.serviceLine.at(index).get(field).errors['required'];
          this.serviceLine.at(index).get(field).updateValueAndValidity();
        }
      }

    }
    // fields = fields.filter(field => field != fieldName);
    // if (!!this.serviceLine.at(index).get(fieldName).value) {
    //   for (const field of fields) {
    //     if (!(!!this.serviceLine.at(index).get(field).value)) {
    //       this.serviceLine.at(index).get(field).setErrors({required: true})
    //     } else {
    //       if (this.serviceLine.at(index).get(field).hasError('required')) {
    //         delete this.serviceLine.at(index).get(field).errors['required'];
    //         this.serviceLine.at(index).get(field).updateValueAndValidity();
    //       }
    //     }
    //   }
    // }
  }
  clear(index) {
    this.serviceLine.at(index).get('ndcCode').setErrors(null);
    this.serviceLine.at(index).get('ndcQty').setErrors(null);
    this.serviceLine.at(index).get('ndcQual').setErrors(null);
  }

  ndcValidation(index) {
    let formArr = this.serviceLineInfo.controls['serviceLines'] as FormArray;
    const formGroup = formArr.controls[index] as FormGroup;
    return formGroup;
  }



  calculateTotalAmount(e, i) {
    let unitCharges = this.serviceLine.at(i).get('unitCharges').value;  
    if (unitCharges == null || unitCharges == undefined || unitCharges.length == 0) {
      unitCharges = (this.claimFormData.claimViewModel?.payerId === "41212" || this.claimFormData.claimViewModel?.payerId === "20133" || this.claimFormData.claimViewModel?.payerId === "59354") ? 
      PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;
      this.serviceLine.at(i).get('unitCharges').setValue(unitCharges);
    }
    let dayUnitChanges: number = this.serviceLine.at(i).get('dayUnitChanges').value;
    this.serviceLine.at(i).patchValue({
      total: Number.parseFloat((dayUnitChanges * unitCharges).toString()).toFixed(2)
    })

    this.calculateTotal.emit(e);
    return false
  }
  minimumcharacterTohitAPI: number = 3;
  cPTCodes: AllCPTCode[] = [];



  addnewService() {
    const dOSFrom =    this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);
    const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);
    let locationOfService = '11';
    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {
      locationOfService = this.serviceLine.controls[0].value.locationOfService;
    }
    const empGroup = this.serviceLineform.group({
      dateServiceFrom: new FormControl({value: dOSFrom, disabled: true}, [Validators.required]),
      dateServiceTo: new FormControl( {value: dOSTo, disabled: true}, [Validators.required  ]),
      locationOfService: new FormControl(locationOfService, Validators.required),
      emg: new FormControl(null),
      desc: [{ value: null }],
      cpt: new FormControl(null, [Validators.required]),
      m1: [{ value: '', disabled: this.claimFormData.isViewClaim }, modifierValidator],
      m2: [{ value: '', disabled: this.claimFormData.isViewClaim }, modifierValidator],
      m3: [{ value: "", disabled: this.claimFormData.isViewClaim }, modifierValidator],
      m4: [{ value: '', disabled: this.claimFormData.isViewClaim }, modifierValidator],
      diagnosispointer1: new FormControl('1', [Validators.required]),
      diagnosispointer2: [{ value: '', disabled: this.claimFormData.isViewClaim }],
      diagnosispointer3: [{ value: '', disabled: this.claimFormData.isViewClaim }],
      diagnosispointer4: [{ value: '', disabled: this.claimFormData.isViewClaim }],
      unitCharges: new FormControl((this.claimFormData.claimViewModel?.payerId === "41212" || this.claimFormData.claimViewModel?.payerId === "20133" || this.claimFormData.claimViewModel?.payerId === "59354") ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice, Validators.required),
      dayUnitChanges : new FormControl({ value: '1', disabled: this.claimFormData.isViewClaim }, [Validators.required, Validators.max(99999999), Validators.min(1)]),
      total: [{ value: (this.claimFormData.claimViewModel?.payerId === "41212" || this.claimFormData.claimViewModel?.payerId === "20133" || this.claimFormData.claimViewModel?.payerId === "59354") ? PriceCost.zeroPrice  : PriceCost.zeroPointZeroOnePrice, disabled: true }],
      ePSDT: [{ value: null, disabled: this.claimFormData.isViewClaim }],
      jRenderingProviderId: [{ value: this.serviceLine.at(0)?.get('jRenderingProviderId')?.value, disabled: true }, [Validators.maxLength(10), Validators.minLength(10)]],
      //new FormControl(this.serviceLine.at(0)?.get('jRenderingProviderId')?.value, [Validators.maxLength(10), Validators.minLength(10)]).disable(),
      proceduceC: '',
      proceduceCount: this.serviceLine.length + 1,
      ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),
      lineNote: '',
      ndcQtyQual: '',
      anesStart: '',
      ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),
      anesStop1: '',
      anesStop2: '',
      anesStop3: '',
      ndcQual: '',
      ndcCode: ''
    });

    this.serviceLine.push(empGroup);
    // this.dateValidators();
    // if (this.serviceLine.invalid) {
    //   submitValidateAllFields.validateAllFields(this.serviceLineInfo);
    //   return;
    // }

  }
  removeService(i) {    
    
    this.serviceLine.removeAt(i);
    this.calculateTotal.emit();
    this.validateForm();
  }

  renderingProvider(e: any) {
    this.changeRenderProviderNo(e.target.value);
    this.subjectService.setServicelineProviderNPIChange(e.target.value);
  }
  changeRenderProviderNo(data) {
    if (!!this.serviceLineInfo.controls) {
      for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {
        this.serviceLine.at(index).get('jRenderingProviderId').patchValue(data);
      }
    }
  }
  dateValidators() {
    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {
      for (let item of this.serviceLine.controls) {
        item.get('dateServiceFrom').valueChanges.pipe(distinctUntilChanged()).subscribe((dateServiceFrom) => {
          if (!!dateServiceFrom) {
            if (!!item.get('dateServiceTo').value) {
              if (new Date(dateServiceFrom).getTime() > (new Date(item.get('dateServiceTo').value).getTime())) {
                item.get('dateServiceFrom').setErrors({ invalidDate: true });
              } else if(new Date(dateServiceFrom).getTime() < (new Date(item.get('dateServiceTo').value).getTime())){
                item.get('dateServiceFrom').updateValueAndValidity();
                item.get('dateServiceTo').updateValueAndValidity();
              } else {
                if (item.get('dateServiceFrom').hasError('invalidDate')) {
                  delete item.get('dateServiceFrom').errors['invalidDate'];
                  item.get('dateServiceFrom').updateValueAndValidity();
                }
                if (item.get('dateServiceFrom').hasError('required')) {
                  delete item.get('dateServiceFrom').errors['invalidDate'];
                  item.get('dateServiceFrom').updateValueAndValidity();
                }
              }
            } else {
              if (item.get('dateServiceFrom').hasError('invalidDate')) {
                delete item.get('dateServiceFrom').errors['invalidDate'];
                item.get('dateServiceFrom').updateValueAndValidity();
              }
              if (item.get('dateServiceFrom').hasError('required')) {
                delete item.get('dateServiceFrom').errors['invalidDate'];
                item.get('dateServiceFrom').updateValueAndValidity();
              }
            }
          } else {
            item.get('dateServiceFrom').setErrors({ required: true });
          }
        })
        item.get('dateServiceTo').valueChanges.pipe(distinctUntilChanged()).subscribe((dateServiceTo) => {
          if (!!dateServiceTo) {
            if (!!item.get('dateServiceFrom').value) {
              if (new Date(dateServiceTo).getTime() < (new Date(item.get('dateServiceFrom').value).getTime())) {
                item.get('dateServiceTo').setErrors({ invalidDate: true });
              } else {
                item.get('dateServiceTo').setErrors(null);
                item.get('dateServiceFrom').updateValueAndValidity();
              }
            } else {
              item.get('dateServiceTo').setErrors(null);             
            }
          } else {
            item.get('dateServiceTo').setErrors({ required: true });
          }
        })
      }
    }
  }
  placeOfServiceChanges(e:any) {

    if (!!this.serviceLineInfo.controls) {
      for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {
        this.serviceLine.at(index).get('locationOfService').patchValue(!!e?.value?e.value:null);
      }
    }
  }
  fetchCPT() {
    let result = [];
    let dosFrom;
    if(this.claimFormData.isAddClaim){
      dosFrom = new Date(this.claimFormData?.profileMember?.dOSFrom).getTime();
    }
    else if(!!this.claimFormData?.claimViewModel){
       dosFrom = new Date(this.claimFormData?.claimViewModel?.claimDosfrom).getTime();
    }
   
    for (const item of this.cptCodes) {

      let add_date = (new Date(item.add_date)).getTime();
      let term_date = (new Date(item.term_date)).getTime();
      if ((add_date < dosFrom || add_date == dosFrom) && (term_date > dosFrom || term_date == dosFrom) && !(!!result.find(e => e.mdmCode == item.mdmCode))) {
        result.push(item);
      }
    }
    this.cPTCodes = result;
    if(this.claimFormData.isEditClaim){
      this.validateInvalidCPT();
    }
  }
  cptCodes: AllCPTCode[];
  fetchCPTData() {
    let cpt = localStorage.getItem(LocalStorageKey.allCPT);
    if (!!cpt) {
      this.cptCodes = JSON.parse(JSLZString.decompress(cpt));
      this.fetchCPT();
    } else {
      this.cptservice.fetchAllCpt().subscribe((res) => {
        if (!!res) {
          this.cptCodes = res;
          this.fetchCPT();
        }
      })
    }
  }
  setYesOrNo(data: string) {
    if (data === '' || data === 'N' || data === 'No') {
      return 'No';
    }
    return 'Yes';

  }
  patchServiceLineData() {
    let count: number = 1;
    this.claimFormData?.claimViewModel?.claimsProfessional837?.serviceLineProfessional837s.forEach((ele: ServiceLineProfessional837sModel) => {
      let date = ele.dtp03ServiceDate;
      let fromDate = date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8);
      let toDate = fromDate;

      if (date.substring(9, 13) && date.substring(13, 15) && date.substring(15, 17))
        toDate = date.substring(9, 13) + "-" + date.substring(13, 15) + "-" + date.substring(15, 17);
      const empGroup = this.serviceLineform.group({
        dateServiceFrom: [{ value: fromDate, disabled: true }],
        dateServiceTo: [{ value: toDate, disabled: true }],
        locationOfService: [{ value: ele.sv105PlaceOfServiceCode, disabled: this.claimFormData.isViewClaim }, Validators.required],
        emg: [{ value: this.setYesOrNo(ele.sv109EmergencyIndicator), disabled: this.claimFormData.isViewClaim }],
        desc: [{ value: ele.sv10107ServiceDescription }],
        cpt: new FormControl(ele.sv10102ProcedureCode, [Validators.required]),
        m1: new FormControl(ele.sv10103ProcedureModifier1, modifierValidator),
        m2: new FormControl(ele.sv10104ProcedureModifier2, modifierValidator),
        m3: new FormControl(ele.sv10105ProcedureModifier3, modifierValidator),
        m4: new FormControl(ele.sv10106ProcedureModifier4, modifierValidator),
        diagnosispointer1: new FormControl(Number(ele.sv10701DiagnosisCodePointer1).toString(), [Validators.required]),
        diagnosispointer2: new FormControl(ele.sv10702DiagnosisCodePointer2 ? Number(ele.sv10702DiagnosisCodePointer2).toString() : null),
        diagnosispointer3: new FormControl(ele.sv10703DiagnosisCodePointer3 ? Number(ele.sv10703DiagnosisCodePointer3).toString() : null),
        diagnosispointer4: new FormControl(ele.sv10704DiagnosisCodePointer4 ? Number(ele.sv10704DiagnosisCodePointer4).toString() : null),
        unitCharges : [{ value: Number.parseFloat(ele.sv102LineItemChargeAmount).toFixed(2), disabled: this.claimFormData.isViewClaim }, Validators.required],
        dayUnitChanges: [{ value: ele.sv104ServiceUnitCount, disabled: this.claimFormData.isViewClaim }, [Validators.required]],
        total: [{ value: (Number.parseFloat(ele.sv104ServiceUnitCount) * Number.parseFloat(ele.sv102LineItemChargeAmount)).toFixed(2), disabled: true }],
        ePSDT: [{ value: this.setYesOrNo(ele.sv111EpsdtIndicator), disabled: this.claimFormData.isViewClaim }],
        jRenderingProviderId: 
        [{ value: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm109RenderingProviderIdentifier, disabled: true}, Validators.maxLength(10), Validators.minLength(10) ],
        proceduceC: ele.sv10101ProductServiceIdQualifier,
        proceduceCount: count,

        ndcUnitPrice: new FormControl(ele.cpt03NationalDrugUnitPrice, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),
        lineNote: ele.nte02LineNoteText,
        ndcQtyQual: ele.ctp0501UnitMeasurementCode,
        anesStart: ele.anesStart,
        ndcQty: new FormControl(ele.ctp04NationalDrugUnitCount, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),
        anesStop1: ele.anesStop,
        anesStop2: [{ value: '', disabled: this.claimFormData.isViewClaim }],
        anesStop3: [{ value: '', disabled: this.claimFormData.isViewClaim }],
        ndcQual: ele.lin02NationalDrugCodeQlfr,
        ndcCode: ele.lin03NationalDrugCode,
      });
      this.serviceLine.push(empGroup);
      count = count + 1;
    })
    if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim ) {
      for (let i = 0; i < this.serviceLine.controls.length; i++) {
        this.ndcQualChange(i);
        this.changeValuesToMarkValidation(i);

      }
      this.validateCPTCodeDOS();
    }

  }
  
  dosFromChangedEvent(index, field) {
    this.dosFromChanged.emit();
    this.checkDateValidations(index, field);
    this.twoYearValidationForDate(index);

  }
  checkDateFromValidation(index, field) {
    let fromDate = this.serviceLine.at(index).get(field).value;
    if (!!fromDate && !!new Date(fromDate)) {
      if (new Date(fromDate).getTime() < this.minDate.getTime()) {
        this.serviceLine.at(index).get(field).setErrors({ 'invalidFromDate': true });
      } else {
        if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {
          delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];
          this.serviceLine.at(index).get(field).updateValueAndValidity();
        }
      }
    } else {
      if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {
        delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];
        this.serviceLine.at(index).get(field).updateValueAndValidity();
      }
    }
  }
  twoYearValidationForDate(index) {
    if (!!this.serviceLine.at(index).get('dateServiceFrom').value) {
      let fromDate = new Date(this.serviceLine.at(index).get('dateServiceFrom').value);
      const days = moment(this.todayDate).diff(moment(fromDate), 'days');
      if (days > 730) {
        Swal.fire({
          title: 'Alert',
          text:
            "The Date of Service is beyond 730 days (2 years). Do you want to continue?",
          icon: 'warning',
          showCancelButton: false,
          confirmButtonText: 'Yes',
          showDenyButton: true,
          denyButtonText: 'No',
          reverseButtons: true,
        }).then(async (result: any) => {
          if (result.value == false) {
            this.serviceLine.at(index).get('dateServiceFrom').setValue(null);
          }
        })
      }
    }
  }
  checkDateValidations(index, field) {
    let fromDate = this.serviceLine.at(index).get('dateServiceFrom').value;
    let toDate = this.serviceLine.at(index).get('dateServiceTo').value;
    if (!!fromDate && !!toDate && !!new Date(fromDate) && !!new Date(toDate)) {
      if (new Date(fromDate).getTime() > new Date(toDate).getTime()) {
        this.serviceLine.at(index).get(field).setErrors({ 'invalidDate': true });
      } else {
        if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {
          delete this.serviceLine.at(index).get(field).errors['invalidDate'];
          this.serviceLine.at(index).get(field).updateValueAndValidity();
        }
        if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {
          delete this.serviceLine.at(index).get(field).errors['invalidDate'];
          this.serviceLine.at(index).get(field).updateValueAndValidity();
        }
        if (this.serviceLine.at(index).get(field).hasError('required')) {
          delete this.serviceLine.at(index).get(field).errors['required'];
          this.serviceLine.at(index).get(field).updateValueAndValidity();
        }
      }
    } else {
      if (this.serviceLine.at(index).get(field).hasError('currentIllnessError')) {
        if (this.serviceLine.at(index).get(field).hasError('invalidDate')) {
          delete this.serviceLine.at(index).get(field).errors['invalidDate'];
          this.serviceLine.at(index).get(field).updateValueAndValidity();
        }
      } else {
        this.serviceLine.at(index).get(field).setErrors(null);
      }
      if (!(!!fromDate)) {
        this.serviceLine.at(index).get('dateServiceFrom').setErrors({ 'required': true });
        if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {
          delete this.serviceLine.at(index).get('dateServiceTo').errors['invalidDate'];
          this.serviceLine.at(index).get('dateServiceTo').updateValueAndValidity();
        }
      }
      if (!(!!toDate)) {
        this.serviceLine.at(index).get('dateServiceTo').setErrors({ 'required': true });
        if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {
          delete this.serviceLine.at(index).get('dateServiceFrom').errors['invalidDate'];
          this.serviceLine.at(index).get('dateServiceFrom').updateValueAndValidity();
        }
      }
    }
    this.checkDateFromValidation(index, field);
    this.dateShow();
  }
  ndcCodeValidator(control: FormControl) {
    let isValid = (!!control.value && control.value.trim().length == 11) || control.value == null || control.value == '';
    if (!!control.value) {
      isValid = isValid && String(control.value).match(/[0-9]/g).length == control.value.length;
    }
    return isValid ? null : { 'isInvalid': true };
  }
  ndcQualChange(index) {
    if (!!this.ndcValidation(index).controls['ndcQual'].value && this.ndcValidation(index).controls['ndcQual'].value.toLowerCase() == 'n4') {
      this.ndcValidation(index).controls['ndcCode'].setValidators(this.ndcCodeValidator);
      if (!this.isNdcCodeValid(this.ndcValidation(index).controls['ndcCode'].value)) {
        this.ndcValidation(index).controls['ndcCode'].setErrors({ 'isInvalid': true });
      }
    } else {
      this.ndcValidation(index).controls['ndcCode'].removeValidators(this.ndcCodeValidator);
      if (this.serviceLine.at(index).get('ndcCode').hasError('isInvalid')) {
        delete this.serviceLine.at(index).get('ndcCode').errors['isInvalid'];
      }
    }
    this.serviceLine.at(index).get('ndcCode').updateValueAndValidity();
  }
  isNdcCodeValid(value) {
    let isValid = (!!value && value.trim().length == 11) || value == null || value == '';
    if (!!value) {
      isValid = isValid && String(value).match(/[0-9]/g).length == value.length;
    }
    return isValid;
  }
  addServiceShow: boolean = false;
  dateServiceFrom: any = [];
  dateServiceTo: any = [];
  oldServiceDateFrom: string = '';
  oldServiceDateTo: string = '';

  servicelineEdit(e) {

    if (e.target.checked) {
      this.addServiceShow = true;
      for (let item of this.serviceLine.controls) {
        this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());
        this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString());

       // item.get('dateServiceFrom').enable({ onlySelf: true });
       // item.get('dateServiceTo').enable({ onlySelf: true });

      }
      //submitValidateAllFields.validateDisableControl(this.serviceLineInfo, ["dateServiceFrom"]);
    } else {
      this.addServiceShow = false;
      for (let item of this.serviceLine.controls) {
       // item.get('dateServiceFrom').disable({ onlySelf: true });
       // item.get('dateServiceTo').disable({ onlySelf: true });

      }
    }
  }
  dateShow() {
    this.dateServiceFrom = []
    this.dateServiceTo = [];
    for (let item of this.serviceLine.controls) {
      this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());
      this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString());


    }
    let min = this.dateServiceFrom[0]
    let max = this.dateServiceFrom[0]
    this.dateServiceFrom.forEach(function (v) {
      max = new Date(v) > new Date(max) ? v : max;

      min = new Date(v) < new Date(min) ? v : min;
    });
    this.oldServiceDateFrom = new Date(min).toString();
    this.oldServiceDateTo = new Date(max).toString();
  }
  onSearchQualifer(term: string, item) {
    term = term.toLocaleLowerCase();
    return item['qualifier'].toLocaleLowerCase().indexOf(term) > -1 || item['description'].toLocaleLowerCase().indexOf(term) > -1;

  }

  validateCPTCode() {

    for (let i = 0; i < this.serviceLine.length; i++) {

      for (let k = 1; k <= 4; k++) {
        if (this.serviceLine.at(i).get('diagnosispointer' + k).errors?.dublicate) {
          this.serviceLine.at(i).get('diagnosispointer' + k).setErrors(null)
        }
      }
    }
    for (let i = 0; i < this.serviceLine.length; i++) {
      for (let h = 1; h <= this.serviceLine.length - 1; h++) {
        if (this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {
          for (let j = 1; j <= 4; j++) {
            for (let k = 1; k <= 4; k++) {
              if (i != h && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {
                this.serviceLine.at(i).get('diagnosispointer' + j).setErrors({ dublicate: true })
                this.serviceLine.at(h).get('diagnosispointer' + k).setErrors({ dublicate: true })
                    this.serviceLine.at(i).get('diagnosispointer' +j).markAsTouched();
               this.serviceLine.at(h).get('diagnosispointer' +k).markAsTouched();
              }
            }
          }
        }
      }
    }
    for (let i = 0; i < this.serviceLine.length; i++) {
      for (let j = 1; j <= 4; j++) {
        for (let k = 1; k <= 4; k++) {
          if (j != k && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(i).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(i).get('diagnosispointer' + k).value) {
            if (!this.serviceLine.at(i).get('diagnosispointer' + k).errors) {
              this.serviceLine.at(i).get('diagnosispointer' + k).setErrors({ dublicate: true })
              this.serviceLine.at(i).get('diagnosispointer' +i).markAsTouched();
 
            }
          }
        }
      }
    }
  }
  validateInvalidCPT() {
    let countMatched = 0;
    for (let index = 0; index < this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length; index++) {
      if (this.cptCodes.filter(t => t.cpt == this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode).length > 0) {
        countMatched = countMatched + 1;
      }
      else {
        this.cptservice.fetchchCPTCodeSeach(this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode, this.claimFormData.claimViewModel.claimDosfrom, '').subscribe((res: AllCPTCode[]) => {
          countMatched = countMatched + 1;
          res.forEach(element => {
            this.cPTCodes.push(element);
          });
          this.patchValueMatched(countMatched);
        })
      }
    }
    this.patchValueMatched(countMatched);
  }
  patchValueMatched(countMatched: number) {
    let totalCount = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length;
    if (totalCount === countMatched) {
      this.patchServiceLineData();
    }
  }

  validateCPTCodeDOS() {

    let cptData: ValidateCPTRequest[] = [];
    for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {
      let cptDosData: ValidateCPTRequest = { cpt: '', dos: '' };
      let date = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].dtp03ServiceDate;
      let fromDate = date.substring(6, 8) + "/" + date.substring(4, 6) + "/" + date.substring(0, 4);
      cptDosData.cpt = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].sv10102ProcedureCode;
      cptDosData.dos = fromDate;
      cptData.push(cptDosData);
    }


    this.cptservice.CPTValidorNot(cptData).subscribe((res: AllCPTCode[]) => {
      for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {
        if (res.filter(t => t.cpt === this.serviceLine.at(i).get('cpt').value).length === 0) {
          this.serviceLine.at(i).get('cpt').setErrors({ invalidCpt: true })
          res[i].cpt = this.serviceLine.at(i).get('cpt').value;
          this.cPTCodes.push(res[i]);
          this.invalidCptCode.push(this.serviceLine.at(i).get('cpt').value);
        }
      }
      if (this.serviceLine.invalid) {
        submitValidateAllFields.validateAllFields(this.serviceLineInfo);
        return;
      }
    })
  }


  cptViewHistory() {
    let request: any;
    if (this.claimFormData.isEditClaim) {
      if (this.serviceLine.controls.length > 0) {
        if (!!this.serviceLine.controls[0].value.dateServiceFrom?._d) {
          request = {
            subscribeID: this.claimFormData.claimViewModel.subscribeId,
            dos: this.serviceLine.controls[0].value.dateServiceFrom._d
          };
        }
        else{
          request ={
            subscribeID: this.claimFormData.claimViewModel.subscribeId,
            dos: this.serviceLine.controls[0].value.dateServiceFrom
          }
        }
      }
    }
    else if(this.claimFormData.isAddClaim){
      request = {
        subscribeID: this.claimFormData.profileMember.subscriberID,
        dos: this.claimFormData.profileMember.dOSFrom
      };
    }
      this.cptservice.getCPTViewHistory(request).subscribe((resp: any) => {
        let cptList: any = [];
        if (resp.statusCode == 200 && (resp.content || []).length > 0) {
          cptList = resp.content;
          cptList = cptList.map(cpt => ({
            ...cpt,
            isSelected: false
          }));
        }
        const existingServiceLines: any[] = this.serviceLine.value;
        existingServiceLines?.forEach((serviceLine: any) => {
          cptList.filter(cptItem => cptItem.cpt === serviceLine.cpt).forEach((cpt => {
            cpt.isSelected = true;
          }));
        });
        this.viewHistoryCPTList =cptList;
        let dialogRef = this.dialog.open(CptViewHistoryComponent, {
          height: '650px',
          width: '1100px',
          autoFocus: false,
          restoreFocus: false,
          maxHeight: '90vh',
          panelClass: 'custom-dialog-containers',
          data: {
            cptList: cptList,
          }
        });
        dialogRef.afterClosed().subscribe((data: any) => {
          if (!!data && (data.selectedCPTCodes || []).length > 0) {
            this.mapServiceLinesSelecteCPCodes(data.selectedCPTCodes)
          }
          // if selected cpts is zero then index 0 in existing service line cpt should be empty and remove remaining all.
          else if (!!data && (data.selectedCPTCodes || []).length == 0) {
            if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {

              const servicelineMatchedFromViewHistoryCPTCodes: any[] = this.serviceLine.value.filter(serviceLineItem =>
                this.viewHistoryCPTList.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt));

                if(!!servicelineMatchedFromViewHistoryCPTCodes && servicelineMatchedFromViewHistoryCPTCodes.length>0){

                  servicelineMatchedFromViewHistoryCPTCodes.forEach(item =>{
                    const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt);

                    if(index ==0){
                  let charges =  this.getUnitChargesByPayerId()
                  let cptCharge = this.allCPTCharges.filter(item => item.code == item.cpt)[0]?.charge;
                  if(!!cptCharge){
                    charges = cptCharge;
                  }
                      this.serviceLine.at(index).patchValue({
                        cpt: null,
                        unitCharges:  Number.parseFloat(charges).toFixed(2)         
                      }) 
                    }
                    else{
                      this.serviceLine.removeAt(index);
                    }
                  })
                }
              // this.serviceLine.controls[0].patchValue({
              //   cpt: null,
              //   dayUnitChanges: '0.00'
              // });
              // this.isShowDelete = false;
              // while (this.serviceLine.controls.length > 1) {
              //   this.serviceLine.removeAt(this.serviceLine.controls.length - 1);  // Remove last control
              // }
            }
          }
        });
      })     
  }

  mapServiceLinesSelecteCPCodes(selectedCPTCodes: any[]) {
    let getExistingFirstServiceLine: any;
    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {
      getExistingFirstServiceLine = this.serviceLine.controls[0];  // copy this values to auto fill to new adding service
      /// filter  Unused  servicelines based on selected CPT and remove it from servicelines controls.
      if (selectedCPTCodes.length > 0 && this.serviceLine.controls.length > 0) {
        const servicelineNoMatchedFromSelectedCPTCodes: any[] = this.serviceLine.value.filter(serviceLineItem =>
          !selectedCPTCodes.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt));

        // Remove unMatched values from serviceLines Controls.      
        servicelineNoMatchedFromSelectedCPTCodes.forEach(item => {
          if (!!item.cpt) {
            const isViewHistoryItem = this.viewHistoryCPTList.find(cpt => cpt.cpt == item.cpt);
            // Find the index of the control where the 'id' matches
            const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt);
            // Remove the control if found
            if (index !== -1 && !!isViewHistoryItem) {
              let charges =  this.getUnitChargesByPayerId()
              let cptCharge = this.allCPTCharges.filter(item => item.code == item.cptCode)[0]?.charge;
              if(!!cptCharge){
                charges = cptCharge;
              }
              if (index == 0) {
                this.serviceLine.at(index).patchValue({
                  cpt: null,
                  unitCharges: Number.parseFloat(charges).toFixed(2)  
                })
              }
              else {
                this.serviceLine.removeAt(index);
              }
            }
          }
        });
      }
      let existingServiceLines = this.serviceLine.value;
      // get selected cptCodes except from existingservice lines.
      selectedCPTCodes = selectedCPTCodes.filter(selectedItem =>
        !existingServiceLines.some(existingItem => selectedItem.cpt === existingItem.cpt));

      // if existing service line is one with dates , selected cpt is one if both are different       
       if (selectedCPTCodes.length == 1 && existingServiceLines.length == 1) {

        // if existing cpt is null update existing row of cpt.
        if (existingServiceLines.length == 1 && !existingServiceLines[0].cpt) {
          this.serviceLine.controls[0].patchValue({
            cpt: selectedCPTCodes[0].cpt,
            desc:selectedCPTCodes[0].shortDescription
          });
          this.OnCPTCodeChange(selectedCPTCodes[0].cpt, 0)
          selectedCPTCodes.shift(); // 0 index remove
        }
      }
    }

    // for new selected cpts addding to service lines.
    selectedCPTCodes.forEach((cptCodeDetails: any) => {
      this.patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine)
    });
  }

  patchSelecteCPTCodesFromHistory(cptCodeDetails: any,getExistingFirstServiceLine:any) {
    let locationOfService: any;
    let topSelectedServiceLine: any;
    if (!!getExistingFirstServiceLine) {
      locationOfService = getExistingFirstServiceLine.value.locationOfService;
      topSelectedServiceLine = getExistingFirstServiceLine.getRawValue();
    }
    if(!!cptCodeDetails.cpt){
      // existing service line with empty cptCode patch values
      const index = this.serviceLine.controls.findIndex(control => control.value.cpt === null);
      if (index !== -1) {
        this.serviceLine.at(index).patchValue({
          cpt: cptCodeDetails.cpt,
          desc:cptCodeDetails.shortDescription           
        }) 
        this.OnCPTCodeChange(cptCodeDetails.cpt, index)
    }
    else{
      let unitCharges = this.getUnitChargesByPayerId();
      let  total = unitCharges;
      let cptCharge = this.allCPTCharges.filter(item => item.code == cptCodeDetails.cpt)[0]?.charge;
      if(!!cptCharge){
           unitCharges = cptCharge;  
           total = (cptCharge * 1).toFixed(2);  
      }

      const dOSFrom =    this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);
      const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);

      const empGroup = this.serviceLineform.group({
        dateServiceFrom: new FormControl({value:dOSFrom ,disabled: true}, [Validators.required]),
        dateServiceTo: new FormControl({value:dOSTo,disabled: true}, [Validators.required]),
        locationOfService: new FormControl(!!locationOfService ? locationOfService : null, Validators.required),
        emg: new FormControl(null),
        desc: new FormControl(cptCodeDetails.shortDescription),
        cpt: new FormControl(cptCodeDetails.cpt, [Validators.required]),
        m1: [{ value: '', disabled: false }, modifierValidator],
        m2: [{ value: '', disabled: false }, modifierValidator],
        m3: [{ value: '', disabled: false }, modifierValidator],
        m4: [{ value: '', disabled: false }, modifierValidator],
        diagnosispointer1: new FormControl('1', [Validators.required]),
        diagnosispointer2: [{ value: '', disabled: false }],
        diagnosispointer3: [{ value: '', disabled: false }],
        diagnosispointer4: [{ value: '', disabled:false}],
        charges: new FormControl(Number.parseFloat(unitCharges).toFixed(2), Validators.required),
        dayUnitChanges: new FormControl('1', Validators.required),
        unitCharges: new FormControl({ value: Number.parseFloat(unitCharges).toFixed(2), disabled: false }, [Validators.required]),
        total: [{ value: total, disabled: true }],
        ePSDT: [{ value: null, disabled: false }],
        jRenderingProviderId: [{ value:  topSelectedServiceLine.jRenderingProviderId, disabled: true }, [Validators.maxLength(10), Validators.minLength(10)]],
        proceduceC: '',
        proceduceCount: this.serviceLine.length + 1,
        ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),
        lineNote: '',
        ndcQtyQual: '',
        anesStart: '',
        ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),
        anesStop1: '',
        anesStop2: '',
        anesStop3: '',
        ndcQual: '',
        ndcCode: '',
      });
  
      this.serviceLine.push(empGroup);
      this.calculateTotal.emit();
      this.dateValidators();
      if (this.serviceLine.invalid) {
        submitValidateAllFields.validateAllFields(this.serviceLineInfo);
        return;
      }
    }
    }
  }

  getUnitChargesByPayerId(){
    let charges =PriceCost.zeroPrice
       if(!!this.claimFormData.claimViewModel){
       charges = this.claimFormData.claimViewModel?.payerId === "41212" || this.claimFormData.claimViewModel?.payerId === "20133" || this.claimFormData.claimViewModel?.payerId === "59354" ?  PriceCost.zeroPrice  :  PriceCost.zeroPointZeroOnePrice
        }
       else if(this.claimFormData.isAddClaim){
       charges = this.claimFormData.payerItem.payerId ==="41212"|| this.claimFormData.payerItem?.payerId === "20133" || this.claimFormData.payerItem?.payerId === "59354" ?  PriceCost.zeroPrice :  PriceCost.zeroPointZeroOnePrice
       }
       return charges
  }
}



