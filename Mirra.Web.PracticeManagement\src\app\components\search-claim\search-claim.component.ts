import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { CellDoubleClickedEvent, ColDef, ColumnApi, DragStoppedEvent, GridApi, GridReadyEvent, IDatasource, IGetRowsParams } from 'ag-grid-community';
import { distinctUntilChanged, first, Subject, takeUntil } from 'rxjs';
import { AllPlaceOfServices } from 'src/app/classmodels/ResponseModel/ClaimForm/AllPlaceOfServices';
import { UserIpa } from 'src/app/classmodels/ResponseModel/Dashboard/UserIpa';
import { AllPlaceOfServicesService } from 'src/app/services/ClaimForm/all-place-of-services.service';
import { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';

import { GRID_CONFIG, changeRowColor } from 'src/app/shared/config-ag-grid/config-grid';
import { ClaimStatus } from 'src/app/shared/constant/constatnt';
import { ExportService } from 'src/app/services/export-service/export.service';
import * as moment from 'moment';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { LocalStorageKey } from 'src/app/shared/constant/constatnt';
import { EditClaimActionRenderer } from 'src/app/shared/Renderer/search-cliam/EditClaimActionRenderer';
import { MasterdataService } from 'src/app/services/Shared/masterdata.service';
import { ConfigurationMaster } from 'src/app/classmodels/ResponseModel/MasterData/ConfigurationMaster';
import { COMMON_VALUES, SwalFire } from 'src/app/common/common-static';
import { SaveReportComponent } from './save-report/save-report.component';
import { CacheService } from 'src/app/services/cache-service/cache.service';

import ChildClaimDeActiveClaimIndicatorRenderer from 'src/app/shared/Renderer/claim-grid/ClaimColorIndicatorRenderer';

import { SubjectService } from 'src/app/shared/services/subject.service';
import { AcceptedSearchLockerRenderer } from 'src/app/shared/Renderer/search-cliam/searchClaimAcceptLocker';

export interface GlobalSearchRequestModel {
  pClaimId?: string | null;
  pDisplayMemberFirstName?: string | null;
  pDisplayMemberLastName?: string | null;
  pMemberID?: string | null;
  pIpaCode?: string | null;
  pIpaName?: string | null;
  pTaxId?: string | null;
  pFromDos?: Date | null;
  pToDos?: Date | null;
  pFromDOC?: Date | null;
  pToDOC?: Date | null;
  pBillingProviderFirstName?: string | null;
  pBillingProviderLastName?: string | null;
  pBillingProviderNpi?: string | null;
  pRenderingProviderFirstName?: string | null;
  pRenderingProviderLastName?: string | null;
  pRenderingProviderNpi?: string | null;
  pAge?: number | null;
  pCPTCodes?: string | null;
  pICDCodes?: string | null;
  pInsuranceName?: string | null;
  pBucketName?: string | null;
  pPos?: string | null;
  pBillerName?: string | null;
  pSource?: string | null;
  pPatientDOB?: string | null;
  mbi?: string | null;
  uniqueEncounterNumber?: string | null;
  resubmissionCount?: number | null;
  pageNo?: number;
  pageSize?: number;
  sortColumn?: string;
  sortDirection?: string;
  username?: string;
}
@Component({
  selector: 'app-search-claim',
  templateUrl: './search-claim.component.html',
  styleUrls: ['./search-claim.component.scss']
})
export class SearchClaimComponent implements OnInit {
  resetCalendar = false;
  pageNo: number = 1;
  pageSize: number = 100;
  totalPages: number = 0;
  searchMode = true;
  additionalFilters = false;
  searchForm: FormGroup;
  source: ConfigurationMaster[] = [];
  bucketStatusCodes: any[] = [];
  todayDate: Date = new Date();
  minDate: Date = new Date(new Date().getFullYear() - 2, 0, 1); // 2 years ago from January 1st
  maxDate: Date = new Date(); // Today's date
  sortColumn: string = 'FormCreatedDate';
  sortDirection: string = 'asc';
  gridOptions: any = {
    ...JSON.parse(JSON.stringify(GRID_CONFIG.scrollConfigDetails_SearchClaim.gridOptions)),
    // Enable auto-sizing features
    skipHeaderOnAutoSize: false,
    autoSizeStrategy: {
      type: 'fitGridWidth',
      defaultMinWidth: 100
    },
    suppressColumnVirtualisation: false,
    suppressAutoSize: false,
    // Enable cell text selection
    enableCellTextSelection: true,
    enableRangeSelection: true,
    suppressRowClickSelection: false,
    ensureDomOrder: true,
    defaultColDef: {
      ...JSON.parse(JSON.stringify(GRID_CONFIG.scrollConfigDetails_SearchClaim.gridOptions)).defaultColDef,
      suppressCellSelection: false, // Override to enable cell selection
      enableCellTextSelection: true,
      cellStyle: {
        'user-select': 'text',
        '-webkit-user-select': 'text',
        '-moz-user-select': 'text',
        '-ms-user-select': 'text'
      }
    }
  };
  claimSearchResult = [];
  gridApi!: GridApi;
  destroy$: Subject<boolean> = new Subject<boolean>();
  columnDefs: ColDef[] = [
    { headerName: '', width: 100, lockVisible: true, filter: false, resizable: true, field: 'claimId', pinned: true, cellRenderer: ChildClaimDeActiveClaimIndicatorRenderer },
    {
      headerName: 'S.No',
      width: 90,
      maxWidth: 90,
      minWidth: 90,
      lockPosition: 'left',
      filter: false,
      resizable: false,
      field: '',
      pinned: 'left',
      valueGetter: params => { return params?.node && params?.node?.rowIndex + 1 },
      cellClass: 'ag-grid-row-number-cell',
      cellStyle: { textAlign: 'center' },
      headerClass: 'ag-header-cell-no-ellipsis',
      lockVisible: true
    },

    { headerName: '', width: 100, lockVisible: true, filter: false, resizable: true, field: 'claimId', pinned: true, cellRenderer: AcceptedSearchLockerRenderer },
    { headerName: 'Claim Id', minWidth: 160, resizable: true, field: 'claimId', tooltipField: "claimId", pinned: true, lockVisible: true, cellStyle: { 'user-select': 'text', '-webkit-user-select': 'text', '-moz-user-select': 'text', '-ms-user-select': 'text' } },
    { headerName: 'Member  Name', minWidth: 200, resizable: true, field: 'displayMemberFullName', tooltipField: "displayMemberFirstName", sortable: true, filter: true, filterParams: true, cellStyle: { 'user-select': 'text', '-webkit-user-select': 'text', '-moz-user-select': 'text', '-ms-user-select': 'text' } },

    { headerName: 'Member ID', minWidth: 160, resizable: true, field: 'subscribeId', tooltipField: 'subscribeId', sortable: true, lockVisible: true, cellStyle: { 'user-select': 'text', '-webkit-user-select': 'text', '-moz-user-select': 'text', '-ms-user-select': 'text' } },
    { headerName: 'Date Of Birth', minWidth: 150, resizable: true, field: 'patientDOB', tooltipField: "patientDOB", lockVisible: true, sortable: true, },
    { headerName: 'IPA', minWidth: 200, resizable: true, field: 'ipaName', tooltipField: 'ipaName', lockVisible: true, sortable: true, filter: true },

    {
      headerName: 'DOS From', minWidth: 150, resizable: true, field: 'dosFrom', lockVisible: true, sortable: true, cellRenderer: params => {
        if (params.value) {
          // Convert yyyyMMdd to MM/dd/yyyy
          const dateStr = params.value.toString();
          if (dateStr.length === 8) {
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            return `${month}/${day}/${year}`;
          }
        }
        return params.value;
      }, tooltipValueGetter: params => {
        if (params.value) {
          // Convert yyyyMMdd to MM/dd/yyyy for tooltip
          const dateStr = params.value.toString();
          if (dateStr.length === 8) {
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            return `${month}/${day}/${year}`;
          }
        }
        return params.value;
      }
    },
    {
      headerName: 'DOS To', minWidth: 150, resizable: true, field: 'dosTo', lockVisible: true, sortable: true, cellRenderer: params => {
        if (params.value) {
          // Convert yyyyMMdd to MM/dd/yyyy
          const dateStr = params.value.toString();
          if (dateStr.length === 8) {
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            return `${month}/${day}/${year}`;
          }
        }
        return params.value;
      }, tooltipValueGetter: params => {
        if (params.value) {
          // Convert yyyyMMdd to MM/dd/yyyy for tooltip
          const dateStr = params.value.toString();
          if (dateStr.length === 8) {
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            return `${month}/${day}/${year}`;
          }
        }
        return params.value;
      }
    },
    {
      headerName: 'DOC', minWidth: 150, resizable: true, field: 'doc', tooltipField: "doc", lockVisible: true, sortable: true, cellRenderer: params => {
        if (params.value) {
          // Convert yyyyMMdd to MM/dd/yyyy
          const dateStr = params.value.toString();
          if (dateStr.length === 8) {
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            return `${month}/${day}/${year}`;
          }
        }
        return params.value;
      }, tooltipValueGetter: params => {
        if (params.value) {
          // Convert yyyyMMdd to MM/dd/yyyy for tooltip
          const dateStr = params.value.toString();
          if (dateStr.length === 8) {
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(4, 6);
            const day = dateStr.substring(6, 8);
            return `${month}/${day}/${year}`;
          }
        }
        return params.value;
      }
    },
    //  { headerName: 'Billing Provider First Name', minWidth: 280, resizable: true, field: 'billingProviderFirstName', tooltipField: "billingProviderFirstName", lockVisible: true, sortable: true },
    { headerName: 'Billing Provider Name', minWidth: 250, resizable: true, field: 'billingProviderLastName', tooltipField: "billingProviderName", lockVisible: true, sortable: true },
    { headerName: 'Billing Provider NPI', minWidth: 200, resizable: true, field: 'billingProviderNpi', tooltipField: "billingProviderNpi", lockVisible: true, sortable: true },

    { headerName: 'Rendering Provider  Name', minWidth: 300, resizable: true, field: 'renderingProviderFullName', sortable: true, tooltipField: "renderingProviderFirstName", lockVisible: true },


    { headerName: 'Rendering Provider NPI', minWidth: 250, resizable: true, field: 'renderingProviderNPI', tooltipField: "renderingProviderNPI", lockVisible: true, sortable: true },
    { headerName: 'Age', minWidth: 130, resizable: true, field: 'age', tooltipField: "age", lockVisible: true, sortable: true },
    { headerName: 'CPT Code(s)', minWidth: 180, resizable: true, field: 'cptCodes', tooltipField: "cptCodes", lockVisible: true, sortable: true, },
    { headerName: 'ICD Code(s)', minWidth: 200, resizable: true, field: 'icdCodes', tooltipField: "icdCodes", lockVisible: true, sortable: true },
    { headerName: 'Insurance Name', minWidth: 200, resizable: true, field: 'insuranceName', tooltipField: "insuranceName", lockVisible: true, sortable: true },
    { headerName: 'Bucket Name', minWidth: 220, resizable: true, field: 'bucketName', tooltipField: "bucketName", lockVisible: true, sortable: true, },
    { headerName: 'POS', minWidth: 200, resizable: true, field: 'placeOfService', tooltipField: "placeOfService", lockVisible: true, sortable: true },
    { headerName: 'Biller Name', minWidth: 200, resizable: true, field: 'billerName', tooltipField: "billerName", lockVisible: true, sortable: true },
    //{ headerName: 'MBI', minWidth: 180, resizable: true, field: 'mbi', tooltipField: "mbi", lockVisible: true , sortable: true},
    { headerName: 'Unique Encounter Number', minWidth: 280, resizable: true, field: 'uniqueEncounterNumber', tooltipField: "uniqueEncounterNumber", lockVisible: true, sortable: true },
    { headerName: 'Resubmission Count', minWidth: 230, resizable: true, field: 'resubmissionCount', tooltipField: "resubmissionCount", lockVisible: true, sortable: true },

    { headerName: 'Action', lockPosition: "right", minWidth: 120, resizable: true, lockVisible: true, sortable: false, type: 'centerAligned', filter: false, hide: false, pinned: 'right', cellRenderer: EditClaimActionRenderer },
  ];
  public defaultColDef: ColDef = {
    filter: false,           // enable filtering
    floatingFilter: false,   // show the little input under each header
    resizable: false,
    suppressMenu: false,



  };
  commonSettings = {
    singleSelection: false
    , enableCheckAll: true
    , allowSearchFilter: true
    , limitSelection: -1
    , clearSearchFilter: true
    , maxHeight: 197
    , itemsShowLimit: 1
    , searchPlaceholderText: 'Search'
    , noDataAvailablePlaceholderText: 'Not Found'
    , closeDropDownOnSelection: false
    , showSelectedItemsAtTop: false
  };
  userIpas;
  ipaDropdownSettings;
  bucketDropdownSettings;

  allPlaceOfServices: AllPlaceOfServices[];
  gridColumnApi: ColumnApi;
  dragAndDropCoumns: any;

  constructor(private readonly dailog: MatDialog,
    private readonly formBuilder: FormBuilder,
    private readonly subjectService: SubjectService,
    private masterDataService: MasterdataService,
    private readonly claimReportService: ClaimReportServiceService,
    private readonly alllPlaceOfService: AllPlaceOfServicesService,
    private readonly exportService: ExportService,
    private readonly cacheService: CacheService) {
    this.createFromGroup();
    this.placeOfServiceData();
    this.getSourceMasterData()
    this.getBucketStatusCodes();
  }

  ngOnInit(): void {
    this.drogAndDropRefresh();
    this.fetchUserIpas();
    this.subjectRefresh();
    this.ipaDropdownSettings = JSON.parse(JSON.stringify(this.commonSettings));
    this.ipaDropdownSettings.idField = 'ipaCode';
    this.ipaDropdownSettings.textField = 'name';
    this.bucketDropdownSettings = JSON.parse(JSON.stringify(this.commonSettings));
    this.bucketDropdownSettings.idField = 'statusCode';
    this.bucketDropdownSettings.textField = 'title';

    // Add date range validation for DOS fields
    this.setupDateRangeValidation();
  }

  setupDateRangeValidation() {
    // Watch for changes in fromDOS and toDOS fields
    this.searchForm.get('fromDOS')?.valueChanges.subscribe(() => {
      this.checkDOSDateRange();
    });

    this.searchForm.get('toDOS')?.valueChanges.subscribe(() => {
      this.checkDOSDateRange();
    });

    // Watch for changes in fromDOC and toDOC fields
    this.searchForm.get('fromDOC')?.valueChanges.subscribe(() => {
      this.checkDOCDateRange();
    });

    this.searchForm.get('toDOC')?.valueChanges.subscribe(() => {
      this.checkDOCDateRange();
    });
  }

  checkDOSDateRange() {
    const fromDOS = this.searchForm.get('fromDOS')?.value;
    const toDOS = this.searchForm.get('toDOS')?.value;

    if (fromDOS && toDOS) {
      const startDate = new Date(fromDOS);
      const endDate = new Date(toDOS);

      // Calculate the difference in days
      const timeDifference = endDate.getTime() - startDate.getTime();
      const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));

      if (daysDifference > 365) {
        // Automatically limit the end date to exactly 365 days from start date
        const maxEndDate = new Date(startDate);
        maxEndDate.setDate(startDate.getDate() + 365);
        this.searchForm.get('toDOS')?.patchValue(maxEndDate);

        // Show informational message
        Swal.fire({
          title: 'Date Range Adjusted',
          text: 'Date of Service range has been automatically limited to 365 days from the start date.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#3085d6',
          timer: 3000,
          timerProgressBar: true
        });
      }
    }
  }

  checkDOCDateRange() {
    const fromDOC = this.searchForm.get('fromDOC')?.value;
    const toDOC = this.searchForm.get('toDOC')?.value;

    if (fromDOC && toDOC) {
      const startDate = new Date(fromDOC);
      const endDate = new Date(toDOC);

      // Calculate the difference in days
      const timeDifference = endDate.getTime() - startDate.getTime();
      const daysDifference = Math.ceil(timeDifference / (1000 * 3600 * 24));

      if (daysDifference > 365) {
        // Automatically limit the end date to exactly 365 days from start date
        const maxEndDate = new Date(startDate);
        maxEndDate.setDate(startDate.getDate() + 365);
        this.searchForm.get('toDOC')?.patchValue(maxEndDate);

        // Show informational message
        Swal.fire({
          title: 'Date Range Adjusted',
          text: 'Date of Creation range has been automatically limited to 365 days from the start date.',
          icon: 'info',
          confirmButtonText: 'OK',
          confirmButtonColor: '#3085d6',
          timer: 3000,
          timerProgressBar: true
        });
      }
    }
  }

  drogAndDropRefresh() {
    this.gridOptions = {
      onCellDoubleClicked: (event: CellDoubleClickedEvent) =>
        console.log('Cell was clicked'),
      onDragStopped: (event: DragStoppedEvent) => this.drogAndDropColumns(event),
    }
    this.gridOptions.getRowStyle = this.changeRowColor;
  }

  drogAndDropColumns(params: any) {
    let columnDragState = params.columnApi.getColumnState();
    const colIds = params.columnApi.getAllDisplayedColumns().map((e) => {
      return e.colDef;
    });
    let arr3 = colIds.map((item, i) =>
      Object.assign({}, item, columnDragState[i])
    );
    this.dragAndDropCoumns = arr3;
  }

  createFromGroup() {
    this.searchForm = this.formBuilder.group({
      claimId: new FormControl(null),
      memberFirstName: new FormControl(null),
      memberLastName: new FormControl(null),
      memberId: new FormControl(null),
      ipaCodes: new FormControl(null),
      ipaName: new FormControl(null),
      taxId: new FormControl(null),
      fromDOS: new FormControl(null),
      toDOS: new FormControl(null),
      fromDOC: new FormControl(null),
      toDOC: new FormControl(null),
      billingProviderFirstName: new FormControl(null),
      billingProviderLastName: new FormControl(null),
      billingProviderNpi: new FormControl(null),
      renderingProviderFirstName: new FormControl(null),
      renderingProviderLastName: new FormControl(null),
      renderingProviderNpi: new FormControl(null),
      age: new FormControl(null),
      cptCodes: new FormControl(null),
      icdCodes: new FormControl(null),
      insuranceName: new FormControl(null),
      bucketName: new FormControl(null),
      pos: new FormControl(null),
      billerName: new FormControl(null),
      source: new FormControl(null),
      patientDob: new FormControl(null),
      mbi: new FormControl(null),
      uniqueEncounterNumber: new FormControl(null),
      resubmissionCount: new FormControl(null)
    });
    return this.searchForm;
  }
  onGridSizeChanged(params: any) {
    if (params && params.api && params.columnApi) {
      // Auto-size columns to fit content first
      const allColumnIds = params.columnApi.getAllColumns().map((column: any) => column.getId());
      params.columnApi.autoSizeColumns(allColumnIds);

      // Then size columns to fit the grid width
      params.api.sizeColumnsToFit();
    }
  }


  changeRowColor(params) {
    return changeRowColor(params);
  }
  fetchUserIpas() {
    if (!!this.cacheService.localStorageGetItem(LocalStorageKey.userIpaDropdown)) {
      this.userIpas = this.cacheService.localStorageGetItem(LocalStorageKey.userIpaDropdown) as UserIpa[];

    }
    else {
      this.claimReportService.fetchUserIPAs().pipe(first()).subscribe(
        data => {
          if (data.statusCode == 200) {
            this.userIpas = data.content as UserIpa[];
          }
        }
      )
    }
  }


  getSourceMasterData() {
    this.masterDataService.fetchchMasterData("Claimsource").pipe(first()).subscribe(response => {
      if (response) {
        this.source = response;
        this.searchForm.patchValue({
          source: this.source.find(item => item.keyValue.toLowerCase() == COMMON_VALUES.all.toLowerCase()).keyValue
        })
      }
    }
    );
  }

  getBucketStatusCodes() {
    this.masterDataService.getBucketStatusCodes().subscribe(response => {
      if (response.statusCode == 200) {
        if ((response.content || []).length > 0) {
          this.bucketStatusCodes = response.content.filter(status => status.statusCode?.toUpperCase() !== 'MB')
        }
      }
    }
    );
  }

  showFilterParamaters() {
    this.searchMode = true;
    this.clearFilters();
  }
  clearFilters() {
    this.searchMode = true;
    this.resetCalendar = true;
    this.searchForm.reset();
    setTimeout(() => {
      this.resetCalendar = false;
    }, 1500);

  }
  getPayload(): GlobalSearchRequestModel | false {
    let source = null;
    this.dragAndDropCoumns = null;
    if (!!this.searchForm.value?.source && this.searchForm.value?.source?.toLowerCase() == COMMON_VALUES.all.toLowerCase()) {
      source = null;
    }
    else if (!!this.searchForm.value?.source && this.searchForm.value?.source?.toLowerCase() != COMMON_VALUES.all.toLowerCase()) {
      source = this.searchForm.value?.source;
    }
    else {
      source = null;
    }
    let email = localStorage.getItem('email') || '';
    email = email.replace(/^"|"$/g, '');
    let request: GlobalSearchRequestModel = {
      pClaimId: this.searchForm.value.claimId,
      pDisplayMemberFirstName: this.searchForm.value.memberFirstName,
      pDisplayMemberLastName: this.searchForm.value.memberLastName,
      pMemberID: this.searchForm.value.memberId,
      pIpaCode: !!this.searchForm.value.ipaCodes ? this.searchForm.value.ipaCodes.map(s => s.ipaCode).toString() : null,
      pIpaName: this.searchForm.value.ipaName,
      pTaxId: this.searchForm.value.taxId,
      pFromDos: !!this.searchForm.value.fromDOS ? this.searchForm.value.fromDOS : null,
      pToDos: !!this.searchForm.value.toDOS ? this.searchForm.value.toDOS : null,
      pFromDOC: !!this.searchForm.value.fromDOC ? this.searchForm.value.fromDOC : null,
      pToDOC: !!this.searchForm.value.toDOC ? this.searchForm.value.toDOC : null,
      pBillingProviderFirstName: this.searchForm.value.billingProviderFirstName,
      pBillingProviderLastName: this.searchForm.value.billingProviderLastName,
      pBillingProviderNpi: this.searchForm.value.billingProviderNpi,
      pRenderingProviderFirstName: this.searchForm.value.renderingProviderFirstName,
      pRenderingProviderLastName: this.searchForm.value.renderingProviderLastName,
      pRenderingProviderNpi: this.searchForm.value.renderingProviderNpi,
      pAge: !!this.searchForm.value.age ? this.searchForm.value.age : null,
      pCPTCodes: this.searchForm.value.cptCodes,
      pICDCodes: this.searchForm.value.icdCodes,
      pInsuranceName: this.searchForm.value.insuranceName,
      pBucketName: !!this.searchForm.value.bucketName ? this.searchForm.value.bucketName.map(s => s.statusCode).toString() : null,
      pPos: this.searchForm.value.pos,
      pBillerName: this.searchForm.value.billerName,
      pSource: source,
      pPatientDOB: this.searchForm.value.patientDob && (moment.isMoment(this.searchForm.value.patientDob) || this.searchForm.value.patientDob.includes('T')) ? moment(this.searchForm.value.patientDob).format('YYYY-MM-DD') : this.searchForm.value.patientDob,
      mbi: this.searchForm.value.mbi,
      uniqueEncounterNumber: this.searchForm.value.uniqueEncounterNumber,
      resubmissionCount: this.searchForm.value.resubmissionCount ? this.searchForm.value.resubmissionCount : null,
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sortColumn: this.sortColumn,
      sortDirection: this.sortDirection,
      username: email
    }

    const hasDosRange = this.searchForm.value.fromDOS || this.searchForm.value.toDOS;
    const hasDocRange = this.searchForm.value.fromDOC || this.searchForm.value.toDOC;
    this.searchForm.controls['fromDOS'].setErrors(null);
    this.searchForm.controls['fromDOC'].setErrors(null);
    this.searchForm.controls['toDOS'].setErrors(null);
    this.searchForm.controls['toDOC'].setErrors(null);

    if (!hasDosRange && !hasDocRange) {
      this.searchForm.controls['fromDOS'].setErrors({ require: true });
      this.searchForm.controls['toDOS'].setErrors({ require: true });
      this.searchForm.controls['fromDOC'].setErrors({ require: true });
      this.searchForm.controls['toDOC'].setErrors({ require: true });
    }
    if (this.searchForm.invalid) { return false; }
    return request;
  }

  getClaims(request: GlobalSearchRequestModel) {
    this.claimReportService.getClaimByGlobalSearch(request).pipe(takeUntil(this.destroy$)).subscribe(
      data => {
        if (data.statusCode == 200) {
          this.searchMode = false;
          this.claimSearchResult = [];
          this.claimSearchResult = (data.content.claims || []);
          this.pageNo = data.content.totalPages;
          this.pageSize = 50;
          this.totalClaims = data.content.totalClaims;
          this.totalPages = Math.ceil(Math.round((data.content.totalClaims / data.content.pageSize + Number.EPSILON) * 100) / 100);
        }
      }
    )
  }
  searchID: number = 0;
  totalClaims: number = 0;





  export() {
    const payload = this.getPayload();
    if (payload === false) {
      return; // Form is invalid, don't proceed with export
    }

    this.claimReportService.extractReportSFTPPath(payload).subscribe(
      data => {
        if (data.statusCode == 200) {
          this.dailog.open(SaveReportComponent, {
            width: '30%',
          })
        }
      });
  }

  exportToExcel(selectedColumns: any) {
    const dataToDisplay = [];
    this.gridApi.forEachNodeAfterFilterAndSort((node) => {
      if (!!node && node.data) {
        dataToDisplay.push(node.data);
      }
    })
    let workbook = new Workbook();
    let worksheet = workbook.addWorksheet('search-claim');
    let columns = [];
    let numberOfColumnsToBeShown: number = 0;
    for (const colItem of selectedColumns) {
      if (colItem.headerName != '' && colItem.headerName != 'Action') {
        columns.push({ header: colItem.headerName, key: colItem.field, width: 25 });
        numberOfColumnsToBeShown = numberOfColumnsToBeShown + 1;
      }
    }
    worksheet.columns = columns;
    dataToDisplay.forEach(data => {
      worksheet.addRow(data, "");
    });
    worksheet.getRow(1).font = {
      bold: true
    };
    worksheet.columns.forEach(column => {
      const lengths = column.values.map(v => v.toString().length);
      lengths.push(25);
      let maxLength = Math.max(...lengths.filter(v => typeof v === 'number'));
      if (maxLength > 400) {
        maxLength = 400;
      }
      column.width = maxLength;
    });
    worksheet = this.exportService.addBorderToCells(worksheet, { row: 1, col: 1 }, { row: dataToDisplay.length + 1, col: numberOfColumnsToBeShown }, 'thin');
    worksheet = this.exportService.addBorderToCells(worksheet, { row: 1, col: 1 }, { row: 1, col: numberOfColumnsToBeShown }, 'thin');
    for (let i = 1; i < numberOfColumnsToBeShown + 1; i++) {
      worksheet = this.exportService.addBorderToCells(worksheet, { row: 1, col: i }, { row: dataToDisplay.length + 1, col: i }, 'thin');
    }
    worksheet.getRow(1).eachCell(c => c.style.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '6594EE' }
    })
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, 'Untitled');
    })
  }
  placeOfServiceData() {
    if (!!this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices)) {
      this.allPlaceOfServices = this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices);
    }
    else {
      this.alllPlaceOfService.fetchchAllPlaceOfServices().subscribe((res: AllPlaceOfServices[]) => {
        this.allPlaceOfServices = res;
      })
    }
  }


  searchClaim() {
    // Check date ranges before proceeding
    this.checkDOSDateRange();
    this.checkDOCDateRange();

    const payload = this.getPayload();
    if (this.searchForm.invalid || payload === false) {
      return;
    }

    this.pageNo = 1;
    this.searchMode = false;
  }
  currentPageNo: number = 1;

  onGridReady(params: any) {
    if (!this.searchMode) {
      this.gridApi = params.api;

      // Auto-size columns to fit content and grid width
      setTimeout(() => {
        if (params.api && params.columnApi) {
          // First auto-size all columns to fit their content
          const allColumnIds = params.columnApi.getAllColumns().map(column => column.getId());
          params.columnApi.autoSizeColumns(allColumnIds);

          // Then size columns to fit the grid width
          params.api.sizeColumnsToFit();
        }
      }, 100);

      if (this.searchForm.invalid) {
        return;
      }
      const dataSource = {
        rowCount: undefined, // unknown at start
        getRows: (params: IGetRowsParams) => {
          const pageNumber = Math.floor(params.startRow / this.pageSize) + 1;
          this.pageNo = pageNumber;

          if (params.sortModel.length > 0) {
            this.sortColumn = params.sortModel[0].colId;
            this.sortDirection = params.sortModel[0].sort;
          }

          if (Object.keys(params.filterModel).length != 0) {
            this.setFilterParamter(params.filterModel);
          }


          const payload = this.getPayload();
          if (payload === false) {
            params.failCallback();
            return;
          }

          this.claimReportService.getClaimByGlobalSearch(payload)
            .subscribe(resp => {
              this.totalClaims = resp.content?.totalCount;
              this.totalPages = resp.content.totalPages
              params.successCallback(resp.content?.claims, resp.content?.totalCount);

            }, _ => {
              params.failCallback();
            });
        }
      };
      this.gridApi.setDatasource(dataSource);
    }
  }
  setFilterParamter(filterParam: any) {
    // Map filter parameters to corresponding form fields
    const formUpdates: any = {};

    if (!filterParam || Object.keys(filterParam).length === 0) {
      // Clear all filter-related form fields when filterParam is empty
      formUpdates.claimId = null;
      formUpdates.memberFirstName = null;
      formUpdates.memberLastName = null;
      formUpdates.memberId = null;
      formUpdates.taxId = null;
      formUpdates.billingProviderLastName = null;
      formUpdates.billingProviderNpi = null;
      formUpdates.renderingProviderFirstName = null;
      formUpdates.renderingProviderLastName = null;
      formUpdates.renderingProviderNpi = null;
      formUpdates.age = null;
      formUpdates.cptCodes = null;
      formUpdates.icdCodes = null;
      formUpdates.insuranceName = null;
      formUpdates.pos = null;
      formUpdates.billerName = null;
      formUpdates.source = null;
      formUpdates.uniqueEncounterNumber = null;
      formUpdates.resubmissionCount = null;

      this.searchForm.patchValue(formUpdates);
      return;
    }

    if (filterParam.claimId?.filter) {
      formUpdates.claimId = filterParam.claimId.filter;
    }
    if (filterParam.displayMemberFirstName?.filter) {
      formUpdates.memberFirstName = filterParam.displayMemberFirstName.filter;
    }
    if (filterParam.displayMemberLastName?.filter) {
      formUpdates.memberLastName = filterParam.displayMemberLastName.filter;
    }
    if (filterParam.subscribeId?.filter) {
      formUpdates.memberId = filterParam.subscribeId.filter;
    }
    if (filterParam.taxId?.filter) {
      formUpdates.taxId = filterParam.taxId.filter;
    }
    if (filterParam.billingProviderLastName?.filter) {
      formUpdates.billingProviderLastName = filterParam.billingProviderLastName.filter;
    }
    if (filterParam.billingProviderNpi?.filter) {
      formUpdates.billingProviderNpi = filterParam.billingProviderNpi.filter;
    }
    if (filterParam.renderingProviderFirstName?.filter) {
      formUpdates.renderingProviderFirstName = filterParam.renderingProviderFirstName.filter;
    }
    if (filterParam.renderingProviderLastName?.filter) {
      formUpdates.renderingProviderLastName = filterParam.renderingProviderLastName.filter;
    }
    if (filterParam.renderingProviderNPI?.filter) {
      formUpdates.renderingProviderNpi = filterParam.renderingProviderNPI.filter;
    }
    if (filterParam.age?.filter) {
      formUpdates.age = filterParam.age.filter;
    }
    if (filterParam.cptCodes?.filter) {
      formUpdates.cptCodes = filterParam.cptCodes.filter;
    }
    if (filterParam.icdCodes?.filter) {
      formUpdates.icdCodes = filterParam.icdCodes.filter;
    }
    if (filterParam.insuranceName?.filter) {
      formUpdates.insuranceName = filterParam.insuranceName.filter;
    }
    if (filterParam.placeOfService?.filter) {
      formUpdates.pos = filterParam.placeOfService.filter;
    }
    if (filterParam.billerName?.filter) {
      formUpdates.billerName = filterParam.billerName.filter;
    }
    if (filterParam.source?.filter) {
      formUpdates.source = filterParam.source.filter;
    }
    if (filterParam.uniqueEncounterNumber?.filter) {
      formUpdates.uniqueEncounterNumber = filterParam.uniqueEncounterNumber.filter;
    }
    if (filterParam.resubmissionCount?.filter) {
      formUpdates.resubmissionCount = filterParam.resubmissionCount.filter;
    }

    // Apply all updates at once
    this.searchForm.patchValue(formUpdates);
  }

  subjectRefresh() {
    this.subjectService.getIsClaimMovedOtherBucket().pipe(takeUntil(this.destroy$)).subscribe((isRefresh: boolean) => {
      debugger
      if (isRefresh) {
        const payload = this.getPayload();
        if (payload !== false) {
          
          this.getClaims(payload);
        }
        this.subjectService.resetIsClaimMovedOtherBucket();
      }
    })


  }
  ngOnDestroy() {
    if (this.gridApi) {
      this.gridApi = null;
    }
    this.destroy$.next(true);
    this.destroy$.unsubscribe();
  }
   refreshGrid() {
      if (this.gridApi) {
       //this.currentTabTotalFileCount = this.tabData.count- this.tabData.additionalCount;
       this.onGridReady(this.gridApi);
      }
     
  }
}
