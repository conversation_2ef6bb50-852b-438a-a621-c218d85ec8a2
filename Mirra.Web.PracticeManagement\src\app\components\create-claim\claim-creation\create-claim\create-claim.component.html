<div class="container-fluid create-claim-component" [ngClass]="{'view-claim-component' : !isShowUpdateBtn && !isNewClaim}">
    <div class="mat-card mt-3 create-claim-form-styles">
        <div class="create-claim" style="display: flow-root !important;">
            <div class="create-claim-title" style="margin-bottom: -30px;">
                {{isNewClaim?'Create':isShowUpdateBtn?'Edit':'View'}} Claim (CMS 1500)
            </div>

            <button class="btn btn-primary create-claim-action primary-btn btn-height" style="float: right; display: block;" type="button" form="claimForm" *ngIf="isNewClaim"
                (click)="onSubmit()"><i class="fa fa-floppy-o icon-margin"></i>Save</button>
            <div class="create-claim-action" *ngIf="claimFormData.claimViewModel?.isActive">


                <div
                    *ngIf=" isShowUpdateBtn && (claimFormData.claimViewModel?.claimFormStatusCode==='OH' || claimFormData.claimViewModel?.claimFormStatusCode==='AC' )">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm" (click)="open('ON')"><i class="fa fa-unlock icon-margin"></i>Open</button>
                </div>
                <div
                    *ngIf="isAcceptedClaimBtnShow && isShowUpdateBtn && (claimFormData.claimViewModel?.claimFormStatusCode==='OH'|| claimFormData.claimViewModel?.claimFormStatusCode==='ON' || claimFormData.claimViewModel?.claimFormStatusCode==='NTR')">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm"
                        (click)="accept('AC')"><i class="fa fa-thumbs-o-up icon-margin"></i>Accept</button>
                </div>
                <div
                    *ngIf="isDeactivateClaimBtnShow && isShowUpdateBtn && (claimFormData.claimViewModel?.claimFormStatusCode==='OH' || claimFormData.claimViewModel?.claimFormStatusCode==='ON'|| claimFormData.claimViewModel?.claimFormStatusCode==='NTR')">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm"
                        (click)="deactivateClaim()"><i class="material-icons icon-margin">do_disturb_on</i>Deactivate</button>
                </div>
                <div
                    *ngIf=" isOnHoldClainBtnShow && isShowUpdateBtn &&  (claimFormData.claimViewModel?.claimFormStatusCode==='AC'  ||claimFormData.claimViewModel?.claimFormStatusCode==='ON' )">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm" (click)="onHold('OH')"><i class="material-icons icon-margin" style="font-size: 13px !important;">pause_circle</i>On
                        Hold</button>
                </div>

                <div *ngIf="isShowUpdateBtn ||claimFormData.isViewClaim || isNewClaim">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm" [disabled]="notesLength()===0"
                        (click)="displayNotes()"><i class="material-icons icon-margin">{{showNotes? 'speaker_notes_off' : 'speaker_notes'}}</i>{{showNotes?'Hide':'Show'}} Notes
                        ( {{notesLength()}} )</button>
                </div>
                <div>
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm" (click)="addNote()"><i class="fa fa-sticky-note-o icon-margin"></i>Add
                        Notes</button>
                </div>
                <div *ngIf="claimFormData.isViewClaim">
                    <button class="btn btn-primary" [disabled]="!saveAllNotesEnable" (click)="saveAllNotes()">
                        <i class="fa fa-save" data-placement="top" title="Save All Notes"></i>
                        Save All Notes
                    </button>
                </div>
                <div *ngIf="isShowUpdateBtn && isUpdateClaimBtnShow">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm" (click)="onSubmit()"><i class="material-icons icon-margin">save_as</i>Update</button>
                </div>
                <div *ngIf="claimFormData.isViewClaim &&  showResubmit() ">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm"
                        (click)="resubmission()"><i class="fa fa-retweet icon-margin"></i>Resubmit</button>
                </div>
                <div *ngIf="isShowUpdateBtn || !isNewClaim">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm"
                        (click)="exportClaim()" matTooltip="Export CMS1500 Form" matTooltipPosition="above"><i class="fa fa-file-pdf-o icon-margin"></i>Export</button>
                </div>
                <div *ngIf="!isShowUpdateBtn && !isNewClaim">
                    <button class="btn btn-common common-btn" type="button" form="claimForm"
                        (click)="viewClaimClose()">Close</button>
                </div>
                <div *ngIf="isShowUpdateBtn">
                    <button class="btn btn-common common-btn" type="button" form="claimForm"
                        (click)="viewClaimClose()">Close</button>
                </div>
            </div>

            <div class="create-claim-action" *ngIf="!claimFormData.claimViewModel?.isActive && isActivateClaimBtnShow">
                <button class="btn btn-primary primary-btn" type="button" *ngIf="isShowUpdateBtn" (click)="activateClaim()"><i class="material-icons icon-margin">check_circle_outline</i>Activate</button>

                <div *ngIf="isShowUpdateBtn ||claimFormData.isViewClaim || isNewClaim">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm" [disabled]="notesLength()===0"
                        (click)="displayNotes()"><i class="material-icons icon-margin">{{showNotes? 'speaker_notes_off' : 'speaker_notes'}}</i>{{showNotes?'Hide':'Show'}} Notes
                        ( {{notesLength()}} )</button>
                </div>
                <div>
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm" (click)="addNote()"><i class="fa fa-sticky-note-o icon-margin"></i>Add
                        Notes</button>                      
                </div>
                <div *ngIf="claimFormData.isViewClaim">
                    <button class="btn btn-primary" [disabled]="!saveAllNotesEnable" (click)="saveAllNotes()">
                        <i class="fa fa-save"  data-placement="top" title="Save All Notes"></i>
                        Save All Notes
                    </button>
                </div>
  
                <div *ngIf="isShowUpdateBtn||claimFormData.isViewClaim">
                    <button class="btn btn-primary primary-btn" type="button" form="claimForm"
                        (click)="exportClaim()" matTooltip="Export CMS1500 Form" matTooltipPosition="above"><i class="fa fa-file-pdf-o icon-margin"></i>Export</button>
                </div>
                <button class="btn btn-common common-btn" type="button" *ngIf="!isNewClaim" form="claimForm" (click)="viewClaimClose()">Close</button>
            </div>
        </div>
        <div class="row" style="position:absolute;top:120px;right:20px; z-index:999;text-align:center;"
            *ngIf="(isShowUpdateBtn || claimFormData.isViewClaim || isNewClaim) &&  showNotes ">
            <div *ngFor="let note of claimFormData.claimViewModel?.notes">

           
            <app-notes *ngIf="!note?.isRemoved" (dismiss)="deleteNote($event)"  
                (focusout)="saveNote($event)" id="{{note?.notesPkId}}">{{note?.description}}</app-notes>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 form-border">
                <div *ngIf="!claimFormData.isAddClaim">
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">
                                Current Claim Status:</span>
                            {{changeStatusText(claimFormData.claimViewModel?.claimFormStatusCode | uppercase)}}
                            <div *ngIf="!claimFormData.claimViewModel?.isActive">
                                (DEACTIVE)
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display"> Created on:</span>
                            {{claimFormData.claimViewModel?.formCreatedDate| date : "MM/dd/yyyy HH:mm:ss"}}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display"> Created By:</span>
                            {{claimFormData.claimViewModel?.userLastName | uppercase}}
                            {{claimFormData.claimViewModel?.userFirstName | uppercase}}
                        </div>
                    </div>
                    <div class="row mt-2"
                        *ngIf="claimFormData.claimViewModel?.parentPatientCtrlNo!=null && claimFormData.claimViewModel?.parentPatientCtrlNo!=((claimFormData.isParentClaim)?claimFormData.claimViewModel?.parentPatientCtrlNo:claimFormData.claimViewModel?.patientCtrlNo)">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display"> Parent ID:</span>

                            <a (click)="parentClaimNavigation()" class="hyper-link">
                                {{claimFormData.claimViewModel?.parentPatientCtrlNo}}</a>
                        </div>

                    </div>
                    <div class="row mt-2">
                        <div class="col-md">
                            <label class="" for="flexCheckDefault"></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 form-border">
                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='OH'">
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display"> Held On
                                :</span>{{claimFormData.claimViewModel?.lastModifiedDate| date : "MM/dd/yyyy HH:mm:ss"}}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display"> Held By
                                :</span>{{claimFormData.claimViewModel?.lastModifiedByLastName | uppercase}}
                            {{claimFormData.claimViewModel?.lastModifiedByFirstName | uppercase }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display"> Reason for holding :</span>
                            {{reasonHoldingDesc(claimFormData.claimViewModel?.reasons)  | uppercase}}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display"> Description :</span>
                             {{reasonDesc(claimFormData.claimViewModel?.reasons)  | uppercase}}
                        </div>
                    </div>
                </div>
                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='AC'">
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            Accepted On :{{claimFormData.claimViewModel?.lastModifiedDate| date : "MM/dd/yyyy
                            HH:mm:ss"}}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            Accepted By :{{claimFormData.claimViewModel?.lastModifiedByLastName | uppercase}}
                            {{claimFormData.claimViewModel?.lastModifiedByFirstName | uppercase}}
                        </div>
                    </div>
                </div>
                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='ANCH'">
                    <div class="row mt-2" *ngIf="(!!claimFormData.claimViewModel?._999processedOnCh || !!claimFormData.claimViewModel?.incomingFileId) && claimFormData.claimViewModel?.responseFileClaimMapings.length>0">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Acknowledged By Clearing House On: </span><span *ngIf="!!claimFormData.claimViewModel?._999processedOnCh">{{claimFormData.claimViewModel?._999processedOnCh| date : "MM/dd/yyyy"}}</span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId || !!claimFormData.claimViewModel?.responseFileClaimMapings[0].incomeFileLoggerId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                        </div>
                    </div>
                </div>

                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='ABCH'">
                    <div class="row mt-2" *ngIf="(!!claimFormData.claimViewModel?._277processedOnCh || !!claimFormData.claimViewModel?.incomingFileId)">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Accepted By Clearing House On: </span><span *ngIf="!!claimFormData.claimViewModel?._277processedOnCh">{{claimFormData.claimViewModel?._999processedOnCh| date : "MM/dd/yyyy"}}</span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                        </div>
                    </div>
                </div>

                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='EOB'">
                    <div class="row mt-2" *ngIf="!!claimFormData.claimViewModel?._835processedOnDate || !!claimFormData.claimViewModel?.incomingFileId">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">EOB Received On: </span><span *ngIf="!!claimFormData.claimViewModel?._835processedOnDate">{{claimFormData.claimViewModel?._835processedOnDate| date : "MM/dd/yyyy"}}</span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                        </div>
                    </div>
                    <div class="row mt-2" *ngIf="!!claimFormData.claimViewModel?.lstLinkedClaimDetails && claimFormData.claimViewModel?.lstLinkedClaimDetails.length > 0">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Linked Claims: </span><a class="hyper-link" (click)="viewLinkedClaim(claimFormData.claimViewModel?.lstLinkedClaimDetails[0])">{{claimFormData.claimViewModel?.lstLinkedClaimDetails[0].patientCtrlNo}}</a>
                            <div class="round-circle" style="display: inline-block;" *ngIf="claimFormData.claimViewModel?.lstLinkedClaimDetails.length > 1" (click)="openLinkedClaims()">+{{claimFormData.claimViewModel?.lstLinkedClaimDetails.length - 1}}
                            </div>
                        </div>
                    </div>
                </div>

                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='Pend'">
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                           <span *ngIf="claimFormData.claimViewModel?._277pprocessedOn"> Pending On :{{claimFormData.claimViewModel?._277pprocessedOn| date : "MM/dd/yyyy"}} </span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                            </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <a class="hyper-link" (click)="pendingDetails()">Pending Details</a>
                        </div>
                    </div>
                </div>
                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='RP'">
                    <div class="row mt-2" *ngIf="(!!claimFormData.claimViewModel?._277processedOnPayer || !!claimFormData.claimViewModel?._999processedOnPayer || !!claimFormData.claimViewModel?.incomingFileId) && claimFormData.claimViewModel?.responseFileClaimMapings.length>0">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Rejected By Payer On: </span>
                            <span *ngIf="!!claimFormData.claimViewModel?._999processedOnPayer">{{claimFormData.claimViewModel?._999processedOnPayer| date :"MM/dd/yyyy"}}</span>
                            <span *ngIf="!(!!claimFormData.claimViewModel?._999processedOnPayer) && !!claimFormData.claimViewModel?._277processedOnPayer">{{claimFormData.claimViewModel?._277processedOnPayer| date :"MM/dd/yyyy"}}</span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Rejected In: </span>  
                            <a class="hyper-link" *ngIf="claimFormData.claimViewModel?._277processedOnPayer" (click)="rejectedReason()" matTooltip="Details" matTooltipPosition="above" matTooltipClass="above">277</a>
 
                        </div>
                    </div>
                    <div class="row mt-2" *ngIf="!!claimFormData.claimViewModel?.ignoreReason">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Reason for Ignore: </span>{{claimFormData.claimViewModel?.ignoreReason}}
                        </div>
                    </div>
                </div>
                
                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='RCH'">
                    <div class="row mt-2" *ngIf="!!claimFormData.claimViewModel?._277processedOnCh || !!claimFormData.claimViewModel?._999processedOnCh || !!claimFormData.claimViewModel?.incomingFileId">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Rejected By Clearing House On: </span>
                            <span *ngIf="!!claimFormData.claimViewModel?._999processedOnCh">{{claimFormData.claimViewModel?._999processedOnCh| date :"MM/dd/yyyy"}}</span>
                            <span *ngIf="!(!!claimFormData.claimViewModel?._999processedOnCh) && !!claimFormData.claimViewModel?._277processedOnCh">{{claimFormData.claimViewModel?._277processedOnCh| date :"MM/dd/yyyy"}}</span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Rejected In: </span>
                            <a class="hyper-link" (click)="rejectedReason()" *ngIf="claimFormData.claimViewModel?.fileType!='005010X231A1'" matTooltip="Details" matTooltipPosition="above" matTooltipClass="above">277</a>
                            <span *ngIf="claimFormData.claimViewModel?.fileType==='005010X231A1'">999</span>
                        </div>
                    </div>
                    <div class="row mt-2" *ngIf="!!claimFormData.claimViewModel?.ignoreReason">
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Reason for Ignore: </span>{{claimFormData.claimViewModel?.ignoreReason}}
                        </div>
                    </div>
                </div>
                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='ABP'">
                    
                    <div class="row mt-2" >
                        <div class="col-md claim-title">
                            <span class="sub-title-name-display">Accepted By Payer On: </span>
                            <span *ngIf="!!claimFormData.claimViewModel?._277processedOnPayer">{{claimFormData.claimViewModel?._277processedOnPayer  | date :"MM/dd/yyyy"}}</span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                        </div>
                    </div>
                </div>
                <div *ngIf="!claimFormData.isAddClaim && claimFormData.claimViewModel?.claimFormStatusCode=='DBP'">
                    
                    <div class="row mt-2" >
                        <div class="col-md claim-title" *ngIf="claimFormData.claimViewModel?.responseFileClaimMapings.length>0">
                            <span class="sub-title-name-display">Denied By Payer On: </span>
                            <span *ngIf="!!claimFormData.claimViewModel?._835processedOnDate">{{claimFormData.claimViewModel?._835processedOnDate  | date :"MM/dd/yyyy"}}</span>
                            <i class="fa fa-download download-icon" *ngIf="!!claimFormData.claimViewModel?.incomingFileId" (click)="downloadLatestResponseFile()" matTooltip="Download Latest Response File" matTooltipPosition="above" matTooltipClass="above"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 form-border">
                <!--  --> <app-payer-info  (payerItemChange)="updatePayerItem($event)" [payerItem]="claimFormData?.payerItem" [claimFormData]="claimFormData"
                    [allStateData]="claimFormData?.allStatesBySearchstring"></app-payer-info>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8 form-border">
                <!--  --> <app-medi-group [payerItem]="claimFormData?.payerItem"
                    [claimFormData]="claimFormData"></app-medi-group>
            </div>
            <div class="col-md-4 form-border">
                <!--     --> <app-subscriber-info [subscriberId]="claimFormData?.memberResult?.subscriberID"
                    [claimFormData]="claimFormData"></app-subscriber-info>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 form-border">
                <!--   --> <app-patient-info [memberResult]="claimFormData?.memberResult?.member"
                    [claimFormData]="claimFormData"></app-patient-info>
            </div>
            <div class="col-md-4 form-border">
                <!--  --> <app-patient-dob [memberResult]="claimFormData?.memberResult?.member"
                    [claimFormData]="claimFormData"></app-patient-dob>
            </div>
            <div class="col-md-4 form-border">
                <!--   --> <app-insure-info [insuredPerson]="claimFormData?.memberResult?.insuredPerson"
                    [claimFormData]="claimFormData"></app-insure-info>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 form-border">
                <!--  --> <app-patient-address [memberResult]="claimFormData?.memberResult?.member"
                    [claimFormData]="claimFormData" [allStateData]="claimFormData?.allStatesBySearchstring"
                    (getInsureData)="setInsureData($event)"></app-patient-address>
            </div>
            <div class="col-md-4 form-border">
                <!-- --> <app-patient-relationship [memberProfile]="claimFormData?.memberResult"
                    [claimFormData]="claimFormData"></app-patient-relationship>
            </div>
            <div class="col-md-4 form-border">
                <!--    --><app-insure-address [insuredPerson]="claimFormData?.memberResult?.insuredPerson"
                    [claimFormData]="claimFormData"
                    [allStateData]="claimFormData?.allStatesBySearchstring"></app-insure-address>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 form-border">
                <!--  --> <app-patient-other-insure [claimFormData]="claimFormData"></app-patient-other-insure>
            </div>
            <div class="col-md-4 form-border">
                <!--   --> <app-patient-condition [allStateData]="claimFormData?.allStatesBySearchstring"
                    [claimFormData]="claimFormData"></app-patient-condition>
            </div>
            <div class="col-md-4 form-border">
                <!-- --> <app-insure-policy-group
                    (anotherHealthInsurenceValidate)="anotherHealthInsurenceValidate($event)"
                    [insuredPerson]="claimFormData?.memberResult?.insuredPerson"
                    [claimFormData]="claimFormData"></app-insure-policy-group>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8 form-border">
                <!--    --> <app-patient-signature [claimFormData]="claimFormData"></app-patient-signature>
            </div>
            <div class="col-md-4 form-border">
                <!--    --> <app-insure-authorized [claimFormData]="claimFormData"></app-insure-authorized>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 form-border">
                <!--   --> <app-current-illness [claimFormData]="claimFormData" (currentIllnessDateChanged)="validateCurrentIllnessDate()"
                    [qualifierDataByType]="claimFormData?.qualifierDataByType"></app-current-illness>
            </div>
            <div class="col-md-4 form-border">
                <!--  --> <app-other-date [claimFormData]="claimFormData" (changeOtherQual)="validateAccidentDate()"
                    [qualifierDataByType]="claimFormData?.qualifierDataByType"></app-other-date>
            </div>
            <div class="col-md-4 form-border">
                <!--   --> <app-patient-current-occuption
                    [claimFormData]="claimFormData"></app-patient-current-occuption>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 form-border">
                <!--  --> <app-other-provider [otherProvider]="claimFormData?.referringProviderInfo"
                    [claimFormData]="claimFormData" [provider]="claimFormData?.provider"></app-other-provider>
            </div>
            <div class="col-md-4 form-border">
                <!--    --> <app-other-provider-payer [otherProvider]="claimFormData?.referringProviderInfo"
                    [claimFormData]="claimFormData" [provider]="claimFormData?.provider"></app-other-provider-payer>
            </div>
            <div class="col-md-4 form-border">
                <!--    --> <app-hospitalization-service [claimFormData]="claimFormData"></app-hospitalization-service>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8 form-border">
                <!--       --> <app-additional-claim [claimFormData]="claimFormData"></app-additional-claim>
            </div>
            <div class="col-md-4 form-border">
                <!--    --> <app-outside-lab></app-outside-lab>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8 form-border">
                <!--  --> <app-diagnosis-nature [claimFormData]="claimFormData" (getICDCountData)="validateDiagnosisPointerData($event)"
                    [serviceLineInfo]="claimFormData?.serviceLineInfo" [formServiceLine]="ServiceLineClaimInfo"></app-diagnosis-nature>
            </div>
            <div class="col-md-4 form-border">
                <!--     --> <app-resubmission-authorized-num [claimFormData]="claimFormData"
                    [resubmissionCode]="claimFormData?.resubmissionCode"></app-resubmission-authorized-num>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 form-border">
                <app-service-line-claim (calculateTotal)="calculateTotal($event)"
                      (dosFromChanged)="validateDosFrom()" [allPlaceOfServices]="allPlaceOfServices"
                    [serviceLineInfos]="claimFormData?.serviceLineInfo" [memberProfile]="claimFormData?.profileMember"
                    [claimFormData]="claimFormData"></app-service-line-claim>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 form-border">
                <app-federal-tax [claimFormData]="claimFormData"></app-federal-tax>
            </div>
            <div class="col-md-4 form-border">
                <app-patient-account-accept-assignment
                    [claimFormData]="claimFormData"></app-patient-account-accept-assignment>
            </div>
            <div class="col-md-4 form-border">
                <app-charges [serviceLineInfos]="claimFormData?.serviceLineInfo"
                    [claimFormData]="claimFormData"></app-charges>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 form-border">
                <app-provider-sign [claimFormData]="claimFormData"
                    [supervisingProviderInfo]="claimFormData?.supervisingProviderInfo"></app-provider-sign>
            </div>
            <div class="col-md-4 form-border">
                <!-- --> <app-service-facility [claimFormData]="claimFormData"
                    [allStateData]="claimFormData?.allStatesBySearchstring"  [billingInfoForm]= "BillingInfoInfo.f"></app-service-facility>
            </div>
            <div class="col-md-4 form-border">
                <app-billing-info [claimFormData]="claimFormData"
                    [allStateData]="claimFormData?.allStatesBySearchstring" [serviceFacilityForm] ="ServiceFacilityForm.f"  [federalTaxForm] ="FederalTaxInfo.f"
                    [taxonomyCode]="claimFormData?.taxonomyCode"></app-billing-info>
            </div>
            
        </div>
    </div>
</div>