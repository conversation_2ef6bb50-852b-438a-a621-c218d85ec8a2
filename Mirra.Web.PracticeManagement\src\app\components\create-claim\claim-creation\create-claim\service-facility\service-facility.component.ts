import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { AllStatesBySearchstring } from 'src/app/classmodels/ResponseModel/ClaimForm/AllStatesBySearchstring';
import { FacilityResult } from 'src/app/classmodels/ResponseModel/Facility/FacilityResult';
import { FacilityInfo } from 'src/app/classmodels/ResponseModel/RenderingProvider/Facility';
import { submitValidateAllFields } from 'src/app/common/form.validators';
import { FacilityServiceModalComponent } from 'src/app/modals/facility-service-modal/facility-service-modal.component';
import { ClaimInfoModel } from 'src/app/models/Providers/ClaimInfoModel';
import { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';
import { npiNumberValidatorWithoutRequired, zipCodeValidator, zipNineCodeValidator } from 'src/app/shared/functions/customFormValidators';
import { stateSearch } from 'src/app/shared/functions/statefunction';
import { SubjectService } from 'src/app/shared/services/subject.service';

@Component({
  selector: 'app-service-facility',
  templateUrl: './service-facility.component.html',
  styleUrls: ['./service-facility.component.scss']
})
export class ServiceFacilityComponent implements OnChanges, OnInit {

  
  @Input() allStateData: AllStatesBySearchstring[];
  @Input() billingInfoForm:any;  
  @Input() claimFormData: ClaimInfoModel;
  serviceInfo: FormGroup;
  isSubmitted = false;
  constructor(private serviceInfoform: FormBuilder, private subscriber: SubjectService, private claimReportServiceService: ClaimReportServiceService,
    private dialog: MatDialog,
  ) {
    this.createForm();
    this.subscriber.getFacilityData().subscribe(res => {
      if (res) {
        
        this.patchValues();
      }
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
  }
  

  ngOnInit(): void {
    this.patchValues();

  }
  get f() { return this.serviceInfo.controls; }
  patchValues() {
    debugger
    if (this.claimFormData.isAddClaim) {
      this.serviceInfo.patchValue({
        nm103LabFacilityName: (this.claimFormData.useFacilityNameAndNPI) ? this.claimFormData.provider.facility?.facilityName : this.claimFormData?.provider?.billingProvider?.billingProviderLastOrOrganizationName,
        n301LabFacilityAddress1: this.claimFormData?.provider?.facility?.facilityAddress1,
        n302LabFacilityAddress2: this.claimFormData?.provider?.facility?.facilityAddress2,
        n401LabFacilityCity: this.claimFormData?.provider?.facility?.facilityCity,
        n402LabFacilityState: this.claimFormData?.provider?.facility?.facilityState,
        n403LabFacilityZip: this.claimFormData?.provider?.facility?.facilityZip,
        FacilityLocationNPI: (this.claimFormData.useFacilityNameAndNPI) ? this.claimFormData.provider.facility?.facilityOrganizationNPI : this.claimFormData?.provider?.billingProvider?.billingProviderNPI,

        FacilityId: this.claimFormData?.facilityInfo?.facilityId,
      });
   //   submitValidateAllFields.disableControlWithEmpty(this.serviceInfo, [""]);
    } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {
      this.serviceInfo.patchValue({
        nm103LabFacilityName: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm103LabFacilityName,
        n301LabFacilityAddress1: this.claimFormData?.claimViewModel?.claimsProfessional837?.n301LabFacilityAddress1,
        n302LabFacilityAddress2: this.claimFormData?.claimViewModel?.claimsProfessional837?.n302LabFacilityAddress2,
        n401LabFacilityCity: this.claimFormData?.claimViewModel?.claimsProfessional837?.n401LabFacilityCity,
        n402LabFacilityState: this.claimFormData?.claimViewModel?.claimsProfessional837?.n402LabFacilityState,
        n403LabFacilityZip: this.claimFormData?.claimViewModel?.claimsProfessional837?.n403LabFacilityZip,
        FacilityLocationNPI: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm109LabFacilityIdentifier,
        FacilityId: this.claimFormData?.claimViewModel?.claimsProfessional837?.serviceLineProfessional837s[0]?.svServiceFacilityId2,
      });
      if (this.claimFormData.isViewClaim) {
        this.serviceInfo.disable();
      }  
     // submitValidateAllFields.disableControlWithEmpty(this.serviceInfo, [""]);
    }
    if (!!this.serviceInfo.controls.FacilityLocationNPI.value) {
      this.serviceInfo.controls.FacilityLocationNPI.setValue(this.serviceInfo.controls.FacilityLocationNPI.value.trim().replace(/[^0-9]/g, ''));
    }
    if (!!this.serviceInfo.controls.nm103LabFacilityName.value) {
      this.serviceInfo.controls.nm103LabFacilityName.setValue(this.serviceInfo.controls.nm103LabFacilityName.value.trim().replace(/[^a-z A-Z0-9]/g, ''));
    }
    if (!!this.serviceInfo.controls.n403LabFacilityZip.value) {
      this.serviceInfo.controls.n403LabFacilityZip.setValue(this.serviceInfo.controls.n403LabFacilityZip.value.trim().replace(/[^0-9]/g, ''));
    }
    if (!!this.serviceInfo.controls.n301LabFacilityAddress1.value) {
      this.serviceInfo.controls.n301LabFacilityAddress1.setValue(this.serviceInfo.controls.n301LabFacilityAddress1.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));
    }
    if (!!this.serviceInfo.controls.n302LabFacilityAddress2.value) {
      this.serviceInfo.controls.n302LabFacilityAddress2.setValue(this.serviceInfo.controls.n302LabFacilityAddress2.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));
    }
    if (!!this.serviceInfo.controls.n401LabFacilityCity.value) {
      this.serviceInfo.controls.n401LabFacilityCity.setValue(this.serviceInfo.controls.n401LabFacilityCity.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));
    }

  }
  validateForm() {
    if (this.serviceInfo.invalid) {
      submitValidateAllFields.validateAllFields(this.serviceInfo);
      return false;
    }
    return true;
  }
  createForm() {
    this.serviceInfo = this.serviceInfoform.group({
      nm103LabFacilityName: new FormControl('', Validators.required),
      n301LabFacilityAddress1: new FormControl(''),
      n302LabFacilityAddress2: new FormControl(''),
      n401LabFacilityCity: new FormControl(''),
      n402LabFacilityState: new FormControl(''),
      n403LabFacilityZip: new FormControl('', zipNineCodeValidator),
      FacilityLocationNPI: new FormControl('', npiNumberValidatorWithoutRequired),
      FacilityId: new FormControl(''),
    })
    return this.serviceInfo;
  }

  stateSearch(term: string, item: AllStatesBySearchstring) {
    return stateSearch.StateSearch(term, item);
  }
 
  changeFacility() {
      let isFacility = this.passFacilityData();
      let dialogRef = this.dialog.open(FacilityServiceModalComponent, {
        height: '650px',
        width: '1100px',
        autoFocus: false,
        restoreFocus: false,
        panelClass: 'custom-dialog-containers',
        data: {          
          useFacilityNpi: isFacility
        },
        disableClose:true
      });
      dialogRef.afterClosed().subscribe((data: any) => {

        let resultData: FacilityResult = data?.facilityData;
        
        if (resultData) {
          if (data.useFacility === 'Y') {
            this.serviceInfo.patchValue({
              nm103LabFacilityName: resultData.facilityName,
              FacilityLocationNPI: resultData.organizationNPI,
            });
          }
          else{
            this.patchBillingToFacilityData();
          }
          this.serviceInfo.patchValue({
            n301LabFacilityAddress1: resultData.address?.addressLine1,
            n302LabFacilityAddress2: resultData.address?.addressLine2,
            n401LabFacilityCity: resultData.address?.city,
            n402LabFacilityState: resultData.address?.state,
            n403LabFacilityZip: resultData.address?.postalCode,
            FacilityId: this.claimFormData?.claimViewModel?.claimsProfessional837?.serviceLineProfessional837s[0]?.svServiceFacilityId2,
          });

          this.serviceInfo.updateValueAndValidity();
        }
      });
  }

  passFacilityData() {
     if (this.claimFormData.isEditClaim ||this.claimFormData.isAddClaim  ) {
      if (this.serviceInfo.controls["nm103LabFacilityName"].value === this.billingInfoForm.billingProviderFullName.value)
      {
        return 'N';
      }
    }
    return  'Y';
  }

  patchBillingToFacilityData() {
      this.serviceInfo.patchValue({
        nm103LabFacilityName: this.billingInfoForm.billingProviderFullName.value,
        FacilityLocationNPI: this.billingInfoForm.groupNPI.value,
      });
  }
}
