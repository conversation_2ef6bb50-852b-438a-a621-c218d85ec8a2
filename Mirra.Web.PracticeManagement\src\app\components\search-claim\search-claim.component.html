<div class="main-card card search-claim">
    <div class="appointment-flex">
        <div class="appointment-header">
            <div>
                <h3 class="appointment-title">Search Claims</h3>
            </div>
        </div>
    </div>
    <form [formGroup]="searchForm" class="filter-claim-component">
        <div class="card search-card" *ngIf="searchMode">
            <div class="row mt-2">
                <div class="col-2">
                    <label for="claimId">Claim ID</label>
                    <input type="text" id="claimId" numbersAndAlphabetsOnly formControlName="claimId"
                        placeholder="Claim ID" autocomplete="off" class="form-control">
                </div>
                <div class="col-2">
                    <label for="memberName">Member First Name</label>
                    <input type="text" id="memberFirstName" numbersAndAlphabetsWithSpace placeholder="First Name"
                        formControlName="memberFirstName" autocomplete="off" class="form-control">
                </div>
                <div class="col-2">
                    <label for="memberName">Member Last Name</label>
                    <input type="text" id="memberLastName" numbersAndAlphabetsWithSpace placeholder="Last Name"
                        formControlName="memberLastName" autocomplete="off" class="form-control">
                </div>
                <div class="col-2">
                    <label for="billerName">Member ID</label>
                    <input type="text" id="billerName" placeholder="Member ID" numbersAndAlphabetsOnly
                        autocomplete="off" formControlName="memberId" class="form-control">
                </div>
                <div class="col-2">
                    <label for="dob">Date Of Birth</label>
                    <input matInput class="form-control" [matDatepicker]="patientDob" formControlName="patientDob"
                        [max]="todayDate" (click)="patientDob.open()" placeholder="MM/DD/YYYY" id="patientDob"
                        [ngClass]="{ 'is-invalid': searchForm.controls['patientDob'].invalid && (searchForm.controls['patientDob'].dirty || searchForm.controls['patientDob'].touched) && searchForm.controls['patientDob'].errors }">
                    <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                    <mat-datepicker #patientDob></mat-datepicker>

                    <!-- <input type="date" id="patientDob"      formControlName="patientDob"  class="form-control"> -->
                    <div class="invalid-feedback"
                        *ngIf="searchForm.controls['patientDob'].invalid && (searchForm.controls['patientDob'].dirty || searchForm.controls['patientDob'].touched) && searchForm.controls['patientDob'].errors">
                        <div *ngIf="searchForm.controls['patientDob'].errors?.matDatepickerParse">
                            Please select valid date.
                        </div>
                    </div>

                    <div class="invalid-feedback"
                        *ngIf="searchForm.controls['patientDob'].invalid && searchForm.controls['patientDob'].errors?.matDatepickerMax">
                        <div *ngIf="searchForm.controls['patientDob'].errors?.matDatepickerMax">
                            Date of birth cannot be a future date
                        </div>
                    </div>
                </div>
                <div class="col-2 padding">
                    <label for="billingProviderNpi">Billing Provider NPI</label>
                    <input type="text" id="billingProviderNpi" [maxlength]="10" numbersOnly
                        placeholder="Billing Provider NPI" autocomplete="off" formControlName="billingProviderNpi"
                        class="form-control">
                </div>
            </div>
            <div class="row mt-2">

                <div class="col">
                    <label for="billingProviderLastName">Billing Provider Name</label>
                    <input type="text" id="billingProviderLastName" numbersAndAlphabetsWithSpace
                        placeholder="Billing Provider Name" autocomplete="off" formControlName="billingProviderLastName"
                        class="form-control">
                </div>
                <div class="col">
                    <label for="renderingProviderFirstName">Rendering Provider First Name</label>
                    <input type="text" id="renderingProviderFirstName" numbersAndAlphabetsWithSpace
                        placeholder="First Name" autocomplete="off" formControlName="renderingProviderFirstName"
                        class="form-control">
                </div>
                <div class="col">
                    <label for="renderingProviderLastName">Rendering Provider Last Name</label>
                    <input type="text" id="renderingProviderLastName" numbersAndAlphabetsWithSpace
                        placeholder="Last Name" autocomplete="off" formControlName="renderingProviderLastName"
                        class="form-control">
                </div>
                <div class="col">
                    <label for="npi">Rendering Provider NPI</label>
                    <input type="text" id="renderingProviderNpi" [maxlength]="10" numbersOnly
                        placeholder="Rendering Provider NPI" formControlName="renderingProviderNpi" autocomplete="off"
                        class="form-control">
                </div>

            </div>

            <div class="row mt-2">

                <div class="col-3">
                    <label class="form-label">Date of Creation (Select Date Range)</label><br>
                    <mat-custom-range-date-picker [reset]="resetCalendar" label="Select Date Range"
                        startDateControlName="fromDOC" endDateFromControlName="toDOC" matTooltip="Select Date Range"
                        [ngClass]="{ 'is-invalid': searchForm.controls['fromDOC'].invalid || searchForm.controls['toDOC'].invalid }"
                        matTooltipPosition="above" >
                    </mat-custom-range-date-picker>
                    <!-- Combined Validation Message -->
                    <div class="invalid-feedback"
                        *ngIf="searchForm.controls['fromDOC'].errors?.require || searchForm.controls['toDOC'].errors?.require">

                        <div *ngIf="searchForm.controls['fromDOC'].errors?.require">
                            From Date is required.
                        </div>
                        <div *ngIf="searchForm.controls['toDOC'].errors?.require">
                            To Date is required.
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <label for="npi">Insurance Name</label>
                    <input type="text" id="insuranceName" numbersAndAlphabetsWithSpace placeholder="Insurance Name"
                        formControlName="insuranceName" autocomplete="off" class="form-control">
                </div>
                <div class="col-3">
                    <label for="ipa">IPA</label>
                    <!--  <ng-multiselect-dropdown #multiSelect placeholder="Select IPA" formControlName="ipaCodes"
                        [settings]="ipaDropdownSettings" [data]="userIpas" optionLabel="name">
                    </ng-multiselect-dropdown>-->
                    <input type="text" placeholder="IPA Name" formControlName="ipaName" autocomplete="off"
                        class="form-control">
                </div>
                <div class="col-3">
                    <div class="form-group bucket-dropdown-wrapper">
                        <label for="bucketName">Bucket Name</label>
                        <ng-multiselect-dropdown #multiSelect placeholder="Select Bucket" formControlName="bucketName"
                            [settings]="bucketDropdownSettings" [data]="bucketStatusCodes" optionLabel="status">
                        </ng-multiselect-dropdown>
                    </div>
                </div>

            </div>
            <div class="row mt-2">
                <div class="col-3">
                    <label class="form-label">Date of Service (Select Date Range)</label><br>
                    <mat-custom-range-date-picker [reset]="resetCalendar" label="Select Date Range" class="cal-height"
                        startDateControlName="fromDOS" endDateFromControlName="toDOS"
                        [ngClass]="{ 'is-invalid': searchForm.controls['fromDOS'].invalid || searchForm.controls['toDOS'].invalid }"
                        matTooltip="Select Date Range" matTooltipPosition="above">
                    </mat-custom-range-date-picker>
                    <div class="invalid-feedback"
                        *ngIf="searchForm.controls['fromDOS'].errors?.require || searchForm.controls['toDOS'].errors?.require">

                        <div *ngIf="searchForm.controls['fromDOS'].errors?.require">
                            From Date is required.
                        </div>
                        <div *ngIf="searchForm.controls['toDOS'].errors?.require">
                            To Date is required.
                        </div>
                    </div>
                </div>

                <div class="col">
                    <label for="taxId">Tax ID</label>
                    <input type="text" id="taxId" numbersOnly placeholder="Tax ID" formControlName="taxId"
                        autocomplete="off" class="form-control">
                </div>

                <div class="col ">
                    <label for="dob">Biller Name</label>
                    <input type="text" id="billername" placeholder="Biller Name" formControlName="billerName"
                        autocomplete="off" class="form-control">
                </div>


                <div class="col padding">
                    <label for="taxId">Unique Encounter Number</label>
                    <input type="text" id="uniqueEncounterNumber" placeholder="Unique Encounter Number"
                        formControlName="uniqueEncounterNumber" autocomplete="off" class="form-control">
                </div>
                <div class="col ">
                    <label for="dob">Resubmission Count</label>
                    <input type="text" id="resubmissionCount" numbersAndAlphabetsWithSpace
                        placeholder="Resubmission Count" formControlName="resubmissionCount" autocomplete="off"
                        class="form-control">
                </div>
            </div>


            <div class="row mt-2" *ngIf="additionalFilters">
                <div class="col-3 padding">
                    <label for="bucketName">Place Of Service</label>
                    <ng-select class="form-control form-control-sm wdt-100" placeholder="Place Of Service"
                        appendTo="body" bindLabel="text" bindValue="value" formControlName="pos">
                        <ng-option *ngFor="let item of allPlaceOfServices" [value]="item">
                            {{item.text | uppercase}}
                        </ng-option>
                    </ng-select>
                </div>
                <div class="col">
                    <label for="RenderingProviderNpi">Age</label>
                    <input type="text" id="age" placeholder="Age" numbersOnly autocomplete="off" formControlName="age"
                        class="form-control">
                </div>
                <div class="col">
                    <label for="npi">CPT Code</label>
                    <input type="text" id="cptCodes" placeholder="CPT Code" formControlName="cptCodes"
                        autocomplete="off" class="form-control">
                </div>

                <div class="col">
                    <label for="npi">ICD Code</label>
                    <input type="text" id="icdCodes" placeholder="ICD Code" formControlName="icdCodes"
                        autocomplete="off" class="form-control">
                </div>

                <div class="col ">
                    <label for="insurance">Source</label>
                    <ng-select class="form-control form-control-sm" #assignto [items]="source" placeholder="Source"
                        bindLabel="keyValue" bindValue="keyValue" formControlName="source">
                        <ng-template ng-option-tmp ng-label-tmp let-item="item">
                            {{item.keyValue | uppercase}}
                        </ng-template>
                    </ng-select>
                </div>

            </div>
            <div class="row mt-3">
                <div class="col-7">&nbsp;</div>
                <div class="col-5" style="text-align: right;">
                    <div class="search-buttons">
                        <button type="button" class="btn btn-common" (click)="additionalFilters = !additionalFilters"><i
                                class="fa fa-filter" aria-hidden="true"></i>{{additionalFilters ? 'Hide Additional
                            Filters' : 'Show Additional Filters'}}</button>
                        <button type="button" class="btn btn-common" (click)="clearFilters()"><i class="fa fa-undo"
                                aria-hidden="true"></i>Reset
                            Filters</button>
                        <button type="button" class="btn btn-primary" (click)="searchClaim()"><i class="fa fa-search"
                                aria-hidden="true"></i>Search</button>
                    </div>
                </div>
            </div>

        </div>
        <div *ngIf="!searchMode">
            <div style="display: flex; justify-content: end;">
                <button type="button" class="btn-primary primary-btn" (click)="showFilterParamaters()">
                    <i class="fa fa-eye icon-margin"></i>Show Filtered Parameters</button>
                <button type="button" class="btn-primary primary-btn" (click)="export()" [disabled]="">
                    <i class="fa fa-download icon-margin"></i>Export</button>
                <!--   <button type="button" class="btn-primary primary-btn" (click)="customizeGridColumns()">
                    <i class="material-icons icon-margin icon-height icon-align"
                        style="padding: 0 !important;">view_carousel</i>Customize</button>-->
            </div>
            <div class="outer-status-box">
                <div class="grid-display left-text">
                    <div class="inner-status-box padding">
                        <i class="fa fa-lock lock_claim icon-margin"></i>
                        <span>This Claim is Locked By for 837 Generation </span>
                    </div>
                    <div class="inner-status-box padding">
                        <i class="fa fa-exclamation-circle exclamation-circle orange icon-margin"></i>
                        <span>Payer is not mapped to Clearing House </span>
                    </div>
                    <div class="inner-status-box padding">
                        <i class="fa fa-exclamation-circle exclamation-circle icon-margin"></i>
                        <span class="mr-2">The Claim Reached maximum Limit of Re-Submissions</span>
                    </div>
                </div>
                <div class="grid-display">
                    <div class="inner-status-box padding">
                        <span class="foo2 blue"></span>
                        <span>RESUBMITTED CLAIMS</span>
                    </div>
                    <div class="inner-status-box padding">
                        <span class="foo2 red"></span>
                        <span>DEACTIVATED CLAIMS </span>
                    </div>
                </div>
            </div>
            <div class="card mt-3 data-card">
                <!-- Total Claims Display -->
                <div class="row p-3" *ngIf="totalClaims > 0">
                    <div class="col-12">
                        <h5 class="mb-0">
                            <strong>Total Claims: {{totalClaims | number}}</strong>
                        </h5>
                    </div>
                </div>

                <ag-grid-angular class="claim-ag-grid ag-theme-alpine ag-grid-view" style="width:100%; height:600px;"
                    [columnDefs]="columnDefs" [defaultColDef]="defaultColDef" [rowModelType]="'infinite'"
                    [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"
                    (gridSizeChanged)="onGridSizeChanged($event)" (firstDataRendered)="onGridSizeChanged($event)">
                </ag-grid-angular>



            </div>
        </div>
    </form>
</div>