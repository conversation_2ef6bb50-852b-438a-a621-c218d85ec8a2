import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { checkIfFileValidWithAllValidations } from 'src/app/shared/functions/checkIfFileTypeValid';
import { MasterdataService } from 'src/app/shared/services/masterdata.service';
import { NotificationService } from 'src/app/services/Notification/notification.service';
import { FileService } from 'src/app/services/file/file.service';
import { SubjectService } from 'src/app/shared/services/subject.service';

@Component({
  selector: 'app-upload-file',
  templateUrl: './upload-file.component.html',
  styleUrls: ['./upload-file.component.scss']
})
export class UploadFileComponent implements OnInit {
  inputVariables = {
    inputSource: '',
    fileType: '',
    ipaName: '',
    files: []
  };
  fileTypes;
  inputSources;
  ipaNames;

  constructor(public dialogRef: MatDialogRef<UploadFileComponent>, @Inject(MAT_DIALOG_DATA) public modalData: any, private masterService: MasterdataService,
    private notificationService: NotificationService, private fileService: FileService, private subjectService: SubjectService) {
    dialogRef.disableClose = true;
    this.inputVariables.fileType = this.modalData
  }

  ngOnInit(): void {
    this.getAllMasterData();
  }
  reset() {
    this.inputVariables.files = [];
    this.inputVariables.fileType = this.modalData;
  }
  close() {
    this.dialogRef.close(true);
  }
  getAllMasterData() {
    this.masterService.fetchchMasterData('EDIFileUploadType').subscribe((res) => {
      this.fileTypes = !!res ? res : [];
    })
    this.masterService.fetchchMasterData('EDIFileUploadInputSource').subscribe((res) => {
      this.inputSources = !!res ? res : [];
      this.inputVariables.inputSource = 'Others';
    })
    this.masterService.fetchchMasterData('EdiFileUploadIPAName').subscribe((res) => {
      this.ipaNames = !!res ? res : [];
      this.inputVariables.ipaName = this.ipaNames.length > 0 ? this.ipaNames[0].keyValue : '';
    })
  }
  addFile(e) {
    let result = {
      fileTypeValid: true,
      fileMinSizeValid: true,
      fileNameValid: true
    }
    let numberIssue = false;
    let msg = '';
    
    for (let file of e.target.files) {
      if (this.validateFile(file) == true) {
        if (this.inputVariables.files.length < 50) {
          this.inputVariables.files.push(file);
        } else {
          numberIssue = true;
        }
      } else {
        let fileValid: any = this.validateFile(file)
        result.fileMinSizeValid = result.fileMinSizeValid && fileValid.fileMinSizeValid;
        result.fileTypeValid = result.fileTypeValid && fileValid.fileTypeValid;
        result.fileNameValid = result.fileNameValid && fileValid.fileNameValid;
      }
    }
    if (numberIssue) {
      this.notificationService.showWarning('', 'Maximum number of files allowed to upload at the same time is 50. Please delete some file, if not needed, or try uploading them next time.', 8000);
    }
    if (!(result.fileMinSizeValid && result.fileNameValid && result.fileTypeValid)) {
      msg = msg + 'One or more files selected are invalid. '
    }
    if (!result.fileTypeValid) {
      let deniedExtensions = ['png', 'pdf', 'zip', 'xls', 'xlsx', 'mp3', 'mp4', 'avi', '7z'];
    
      if (this.inputVariables.fileType === "837CAP") {
        deniedExtensions.push('txt');
      }
    
      msg += `Files with these extensions - [${deniedExtensions.join(', ')}] are not allowed. `;
    }

    if (!result.fileMinSizeValid) {
      if (!result.fileTypeValid) {
        msg = msg + 'Also please ';
      } else {
        msg = msg + 'Please ';
      }
      msg = msg + 'select files greater than 0 Bytes in size. ';
    }
    if (!result.fileNameValid) {
      msg = msg + 'Multiple selected files have same name and type so the first one will be uploaded. Please try to upload the remaining ones by renaming them.';
    }
    if (msg != '') {
      this.notificationService.showWarning('', msg, 8000);
    }
    e.target.value = null;
  }
  getSize(value) {
    if (Math.floor(value / 1024) > 0) {
      return Math.floor(value / 1024) + ' KB'
    } else {
      return value + ' Bytes'
    }
  }
  deleteFile(index) {
    this.inputVariables.files.splice(index, 1);
  }
  validateFile(file) {
    
    let fileType = ['png', 'pdf', 'zip', 'xls', 'xlsx', 'mp3', 'mp4', 'avi', '7z'];

    if (this.inputVariables.fileType === "837CAP") {
      fileType.push('txt');
    }

    let validResult = checkIfFileValidWithAllValidations(file,fileType , this.inputVariables.files);
    if (validResult.fileMinSizeValid && validResult.fileNameValid && validResult.fileTypeValid) {
      return true;
    } else {
      return validResult;
    }
  }
  saveFiles() {
    this.fileService.uploadEdiFile(this.inputVariables.files, this.inputVariables.inputSource, this.inputVariables.fileType, this.inputVariables.ipaName).subscribe((res) => {
      if (res.statusCode == 200) {
        this.notificationService.showSuccess('File Uploaded Successfully. File Translation is in process. Please check after some time.', 'Success!', 4000);
       // this.subjectService.setFileRefreshAfterAdd(true);
        if (this.inputVariables.fileType.includes('837')) {
          //this.subjectService.setBucketClaimGridRefresh();
        }
        this.reset();
        this.close();
      } else {
        if (res.message.match("File name already exist")) {
          this.notificationService.showError(res.message, 'Error!', 4000);
        } else {
          this.notificationService.showError('File could not be uploaded. Please try again later.', 'Error!', 4000);
        }
      }
    }, (error) => {
      this.notificationService.showError('File could not be uploaded. Please try again later.', 'Error!', 4000);
    })
  }
}
