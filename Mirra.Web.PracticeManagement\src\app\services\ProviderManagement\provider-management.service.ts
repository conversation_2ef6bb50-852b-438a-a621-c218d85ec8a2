import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';

import { UrlClass } from 'src/app/classmodels/App/UrlClass';
import { map } from 'rxjs/operators';
import { HeaderData } from 'src/app/classmodels/App/HeaderData';
import { UserAuthenticationService } from '../UserAuthentication/UserAuthentication.service';
import { environment } from 'src/environments/environment';
import { TaxonomyCodeModel } from 'src/app/models/ClaimForm/Providers/Taxonomy.model';
import * as JSLZString from 'lz-string';
import { CacheService } from '../cache-service/cache.service';
import { LocalStorageKey } from 'src/app/shared/constant/constatnt';

@Injectable({
  providedIn: 'root'
})
export class ProviderManagementService {
  appUrl: UrlClass;
  ViewOrEditForm: string;
  uniqueProviderId: string;

  headerData: HeaderData = {};

  constructor(private userAuthenticationService: UserAuthenticationService, private http: HttpClient,
    private cacheService:CacheService
  ) {
    this.appUrl = environment.apiUrl;
  }

  SearchProvider(searchProvider) {

    // const SearchProviderModel = {
    //   searchProviderModel: searchProvider,
    // }

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    return this.http.post<any>(`${environment.apiUrl}/provider/capitated/SearchProviderForProviderBridge`, searchProvider, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(this.headerData)
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }

  SetIdForProviderSearch(uniqueProviderId) {
    this.uniqueProviderId = uniqueProviderId;
  }

  fetchProviderData(providerId) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'providerProfileID': providerId },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.get<any>(`${environment.apiUrl}/Provider/capitated/GetProviderProfileById`, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }
  fetchFacilityInfo(facilityId) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };
    let requestBody = {
      "organizationNPI": "",
      "facilityName": "",
      "addressLine1": "",
      "searchParameters": [
        {
          "fieldName": "UniqueOrgCode",
          "fieldValue": facilityId
        }
      ],
      "ipaCode": "",
      "index": 0,
      "sortingType": "",
      "sortBy": "",
      "countOfList": 0
    }

    const httpOptions = {
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/facilitysearch`, requestBody, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }

  fetchFacilityData(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData), "Content-Type": "application/json", },),
    };

    return this.http.post<any>(`${environment.apiUrl}/provider/capitated/GetAllFacilitiesForClaims`, searchTerm, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }

  fetchSpecialityData(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/GetAllSpecialityTaxonomyCodes`, httpOptions).pipe(
      map(response => {
        if (!!response && !!response.content) {
           this.cacheService.localStorageSetItem(LocalStorageKey.allSpecialityTaxonomyCodes,response.content);
        }
        return response.content as TaxonomyCodeModel[];;
      })
    );
  }
  fetchProviderSpecialties(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/GetAllSpecialities`, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }
  fetchAllFacilityTypes() {
    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired,
    };
    const httpOptions = {
      params: {},
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    }
    return this.http.get<any>(`${environment.apiUrl}/MDMData/GetAllFacilityTypes`, httpOptions).pipe(
      map(response => {
        return response.content;
      })
    );
  }

  fetchPlansData(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };
    let requestBody = {
      insuranceCompanyName: searchTerm,
      ipaCodes: ""
    }
    const httpOptions = {
      // params: { 'insuranceCompanyName': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),

    };

    return this.http.post<any>(`${environment.apiUrl}/mdmdata/getmasterpayerbyipa`, requestBody, httpOptions).pipe(
      map(response => {
        if (!!response && !!response.content) {
          localStorage.setItem('insuranceCompaniesOrAllPlans', JSLZString.compress(JSON.stringify(response.content)));
        }
        return response;
      })
    );
  }

  fetchGroupsData(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/GetAllGroups`, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }

  fetchIPAData(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': '' },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    }

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/getallipas`, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }

  fetchCountiesData(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),

    }

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/getallcounties`, httpOptions).pipe(
      map(response => {
        if (!!response && !!response.content) {
          localStorage.setItem('allCounties', JSLZString.compress(JSON.stringify(response.content)));
        }
        return response;
      })
    );
  }
  getIPAbyUUID() {

    let uuidData = JSON.parse(localStorage.getItem('uuid'));
    let headerData: any = {
      ServiceBaseUrl: '',
      // JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired,
      uuid: uuidData,
    };
    // let uuidData = JSON.parse(localStorage.getItem('uuid'));
    const httpOptions = {
      params: { uuid: uuidData },
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(headerData),
        'Authorization': JSON.parse(localStorage.getItem('currentUser')),
      }),

    }

    return this.http.get<any>(environment.apiUrl + '/miscellaneous/GetIPAListByUUID', httpOptions)
      .pipe(
        map(response => {
          return response;
        })
      );
  }

  fetchCountriesData(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    }

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/getallcountries`, httpOptions).pipe(
      map(response => {
        if (!!response && !!response.content) {
          localStorage.setItem('allCountries', JSLZString.compress(JSON.stringify(response.content)));
        }
        return response;
      })
    );
  }

  fetchCitiesData() {
    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    }

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/getallcities`, httpOptions).pipe(
      map(response => {
        if (!!response && !!response.content) {
          localStorage.setItem('allCities', JSLZString.compress(response.content))
        }
        return response;
      })
    );
  }

  fetchCitiesBySearchTerm(searchTerm: string) {
    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    }

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/searchcities`, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }

  CheckNpi(searchTerm) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/IsNPIExists`, null, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }

  GetCorrectAddress(LocationDetails) {
    const headerData = {
      ServiceBaseUrl: '',
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired,
      uuid: JSON.parse(localStorage.getItem('uuid'))
    };
    return this.http.post<any>(`${environment.apiUrl}/miscellaneous/CheckAddress`, LocationDetails, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(headerData),
        'Authorization': JSON.parse(localStorage.getItem('currentUser')),
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }

  UpdatePracticeLocation(requestBody) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/UpdatePracticeLocation`, requestBody, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(this.headerData)
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }

  AddPracticeLocation(requestBody) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/AddNewPracticeLocation`, requestBody, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(this.headerData)
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }

  GetMDMCode(searchTerm, ipacodes) {

    this.headerData = {
      ServiceBaseUrl: environment.serviceUrls.mdmServiceBase,
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    const httpOptions = {
      params: { 'searchTerm': searchTerm, ipacodes: ipacodes },
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.get<any>(`${environment.apiUrl}/MDMData/GetAllIPA`, httpOptions).pipe(
      map(response => {
        return response;
      })
    );
  }

  AddFacility(facility) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/AddNewFacility`, facility, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(this.headerData)
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }

  UpdateFacility(facility) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/UpdateFacility`, facility, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(this.headerData)
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }
  updateFacility(facility) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/UpdateFacility`, facility, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(this.headerData)
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }

  AddProvider(provider) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')) + "," + JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired
    };

    return this.http.post<any>(`${environment.apiUrl}/Provider/capitated/AddProvider`, provider, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        "serializedHeaderData": JSON.stringify(this.headerData),
      })
    }).pipe(
      map(response => {
        return response;
      })
    );
  }
  getTabNameForProvider(data) {
    let result = [];
    if (data.npiNumber) {
      result.push(data.npiNumber)
    }
    if (data.fullName) {
      result.push(data.fullName)
    }
    return result.join(' - ')
  }

  getLocationDetailsByStreetAndZipCode(locationDetails: any) {
    const headerData = {
      ServiceBaseUrl: '',
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired,
      uuid: JSON.parse(localStorage.getItem('uuid'))
    };
    return this.http.post<any>(`${environment.apiUrl}/miscellaneous/CheckStreetAndZipCode`, locationDetails, {
      headers: new HttpHeaders({
        "Content-Type": "application/json",
        'serializedHeaderData': JSON.stringify(headerData),
        'Authorization': JSON.parse(localStorage.getItem('currentUser')),
      })
    });
  }

  fetchPayerDetailByPayerName(payerName: string) {

    this.headerData = {
      ServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      JWTAutherizationToken: JSON.parse(localStorage.getItem('currentUser')),
      Username: JSON.parse(localStorage.getItem('email')),
      IsLogRequired: this.userAuthenticationService.IsLogRequired,
      IsDebugLogRequired: this.userAuthenticationService.IsDebugLogRequired,

    };

    const httpOptions = {
      headers: new HttpHeaders({ 'serializedHeaderData': JSON.stringify(this.headerData) }),
    };

    return this.http.get<any>(`${environment.apiUrl}/mdmdata/getpayerdetailbypayername/${payerName}`, httpOptions);
  }
}
