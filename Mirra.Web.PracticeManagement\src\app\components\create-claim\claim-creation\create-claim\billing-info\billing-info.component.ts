import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { AllStatesBySearchstring } from 'src/app/classmodels/ResponseModel/ClaimForm/AllStatesBySearchstring';
import { submitValidateAllFields } from 'src/app/common/form.validators';
import { TaxonomyCodeModel } from 'src/app/models/ClaimForm/Providers/Taxonomy.model';
import { ClaimInfoModel } from 'src/app/models/Providers/ClaimInfoModel';
import { zipCodeValidator, zipNineCodeValidator } from 'src/app/shared/functions/customFormValidators';
import { stateSearch } from 'src/app/shared/functions/statefunction';
import { SubjectService } from 'src/app/shared/services/subject.service';
import { RenderProviderComponent } from '../../search/search-provider/render-provider/render-provider.component';
import { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';
import { RenderingProviderModalComponent } from 'src/app/modals/rendering-provider-modal/rendering-provider-modal.component';
import { BillingProviderModalComponent } from 'src/app/modals/billing-provider-modal/billing-provider-modal.component';
import { BillingProviderResult } from 'src/app/models/ClaimForm/Providers/BillingProviderResult';
import { CacheService } from 'src/app/services/cache-service/cache.service';
import { LocalStorageKey } from 'src/app/shared/constant/constatnt';
import { result } from 'lodash';
@Component({
  selector: 'app-billing-info',
  templateUrl: './billing-info.component.html',
  styleUrls: ['./billing-info.component.scss']
})
export class BillingInfoComponent implements OnInit {

  billingInfo: FormGroup;
  @Input() claimFormData: ClaimInfoModel;
  @Input() allStateData: AllStatesBySearchstring[];
  @Input() taxonomyCode: TaxonomyCodeModel[]
  @Input() serviceFacilityForm:any;
  @Input() federalTaxForm:any;
  constructor(private billingform: FormBuilder,
    private dialog: MatDialog,
    private claimReportServiceService: ClaimReportServiceService,
    private subjectService: SubjectService,
    private cacheService:CacheService) {
    this.subjectService.getServicelineProviderNPIChange().subscribe(res => {
      if (res || res == '') {
        this.billingInfo.controls['providerNPI'].patchValue(res);
        this.subjectService.resetServicelineProviderNPIChange();
      }
    })
  }
  isSubmitted
  get f() { return this.billingInfo.controls; }
  ngOnInit(): void {
    this.patchValue();
  }

  createForm() {
    this.billingInfo = this.billingform.group({
      billingProviderFullName: new FormControl('', [Validators.required]),
      nm104BillingProviderFirst: new FormControl(''),
      nm103BillingProviderMiddle: new FormControl(''),
      n301BillingProviderAddr1: new FormControl('', Validators.required),
      n302BillingProviderAddr2: new FormControl(''),
      n401BillingProviderCity: new FormControl('', Validators.required),
      n402BillingProviderState: new FormControl('', Validators.required),
      n403BillingProviderZip: new FormControl('', [Validators.required, zipNineCodeValidator]),
      billingProviderTelephone: new FormControl(''),
      billingproviderSpecialty: new FormControl(''),
      nm103RenderingProviderLastOrOrganizationName: new FormControl('', [Validators.required]),
      nm104RenderingProviderFirst: new FormControl(''),
      nm105RenderingProviderMiddle: new FormControl(''),
      providerSpecialty: new FormControl(''),
      providerNPI: new FormControl('', [Validators.minLength(10), Validators.maxLength(10), Validators.required]),
      renderingProviderPin: new FormControl(''),
      groupNPI: new FormControl('', [Validators.minLength(10), Validators.maxLength(10), Validators.required]),
      providerId: new FormControl(''),
      idQual: new FormControl('')
    })
    return this.billingInfo;
  }
  patchValue() {
    if (this.claimFormData.isAddClaim && this.claimFormData?.provider?.billingProvider) {
      let speciality = null;
      if (this.claimFormData?.provider?.renderingProvider?.speciality && this.claimFormData?.provider?.renderingProvider?.taxonomy) {
        speciality = this.claimFormData?.taxonomyCode?.find(t => t.taxonomyCode.toLocaleUpperCase() == this.claimFormData?.provider?.renderingProvider?.taxonomy.toLocaleUpperCase())?.taxonomyCode;

      }
      this.billingInfo.patchValue({
        billingProviderFullName: this.claimFormData?.provider?.billingProvider?.billingProviderLastOrOrganizationName,
        nm104BillingProviderFirst: this.claimFormData?.provider?.billingProvider?.billingProviderFirstName,
        nm103BillingProviderMiddle: this.claimFormData?.provider?.billingProvider?.billingContactMiddleName,
        n301BillingProviderAddr1: this.claimFormData?.provider?.billingProvider?.billingProviderFirstAddress,
        n302BillingProviderAddr2: this.claimFormData?.provider?.billingProvider?.billingProviderSecondAddress,
        n401BillingProviderCity: this.claimFormData?.provider?.billingProvider?.billingProviderCity,
        n402BillingProviderState: this.claimFormData?.provider?.billingProvider?.billingProviderState,
        n403BillingProviderZip: this.claimFormData?.provider?.billingProvider?.billingProviderZip,
        billingProviderTelephone: this.claimFormData?.provider?.billingProvider?.billingContactPersonPhoneNumber,
        billingproviderSpecialty: this.claimFormData?.provider?.billingProvider?.billingProviderTaxonomy == '' ? null : this.claimFormData?.provider?.billingProvider?.billingProviderTaxonomy,
        nm103RenderingProviderLastOrOrganizationName: this.claimFormData?.provider?.renderingProvider?.providerLastName,
        nm104RenderingProviderFirst: this.claimFormData?.provider?.renderingProvider?.providerFirstName,
        nm105RenderingProviderMiddle: this.claimFormData?.provider?.renderingProvider?.providerMiddleName,
        providerSpecialty: this.claimFormData?.provider?.renderingProvider?.taxonomy,
        providerNPI: this.claimFormData?.provider?.renderingProvider?.providerNPI,
        renderingProviderPin: this.claimFormData?.provider?.renderingProvider?.pPID,
        groupNPI: this.claimFormData?.provider?.billingProvider?.billingProviderNPI,
        providerId: '',
        idQual: ''
      })
      submitValidateAllFields.disableControlWithEmpty(this.billingInfo, ["n403BillingProviderZip", "groupNPI", "renderingProviderPin", "idQual","providerId"]);

    } else if (this.claimFormData.isEditClaim || this.claimFormData.isViewClaim) {

      this.billingInfo.patchValue({
        billingProviderFullName: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.nm103BillingProviderLastOrOrganizationName,
        nm104BillingProviderFirst: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.nm104BillingProviderFirst,
        nm103BillingProviderMiddle: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.nm105BillingProviderMiddle,
        n301BillingProviderAddr1: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.n301BillingProviderAddr1,
        n302BillingProviderAddr2: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.n302BillingProviderAddr2,
        n401BillingProviderCity: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.n401BillingProviderCity,
        n402BillingProviderState: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.n402BillingProviderState,
        n403BillingProviderZip: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.n403BillingProviderZip,
        billingProviderTelephone: this.omit_spl_charcter(this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.perAdministrativeCommunicationContactProfessional837s[0]?.per0xPhoneNo),
        billingproviderSpecialty: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.prv03BillingProviderIdCode,
        nm103RenderingProviderLastOrOrganizationName: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm103RenderingProviderLastOrOrganizationName,
        nm104RenderingProviderFirst: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm104RenderingProviderFirst,
        nm105RenderingProviderMiddle: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm105RenderingProviderMiddle,
        providerSpecialty: this.claimFormData?.claimViewModel?.claimsProfessional837?.prv03ProviderTaxonomyCode,
        providerNPI: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm109RenderingProviderIdentifier,
        renderingProviderPin: this.claimFormData?.claimViewModel?.claimsProfessional837?.otherInfoProfessionalOtherInfoProfessional?.renderingProviderPin,
        groupNPI: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.nm109BillingProviderIdCode,
        providerId: this.claimFormData?.claimViewModel?.claimsProfessional837?.otherInfoProfessionalOtherInfoProfessional?.billingGroupNumber,
        idQual: this.claimFormData?.claimViewModel?.claimsProfessional837?.otherInfoProfessionalOtherInfoProfessional?.billingGroupIdQlfr,
      })
      submitValidateAllFields.disableControlWithEmpty(this.billingInfo, ["n403BillingProviderZip", "groupNPI", "renderingProviderPin", "idQual","providerId"]);
      if (this.claimFormData.isViewClaim) {
        this.billingInfo.disable();
      }
      submitValidateAllFields.disableControlWithEmpty(this.billingInfo, ["n403BillingProviderZip", "groupNPI", "renderingProviderPin", "idQual","providerId"]);
    }

    if (!!this.billingInfo.controls.groupNPI.value) {
      this.billingInfo.controls.groupNPI.setValue(this.billingInfo.controls.groupNPI.value.trim().replace(/[^0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.n403BillingProviderZip.value) {
      this.billingInfo.controls.n403BillingProviderZip.setValue(this.billingInfo.controls.n403BillingProviderZip.value.trim().replace(/[^0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.billingProviderTelephone.value) {
      this.billingInfo.controls.billingProviderTelephone.setValue(this.billingInfo.controls.billingProviderTelephone.value.trim().replace(/[^a-zA-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.nm104BillingProviderFirst.value) {
      this.billingInfo.controls.nm104BillingProviderFirst.setValue(this.billingInfo.controls.nm104BillingProviderFirst.value.trim().replace(/[^a-z A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.nm103BillingProviderMiddle.value) {
      this.billingInfo.controls.nm103BillingProviderMiddle.setValue(this.billingInfo.controls.nm103BillingProviderMiddle.value.trim().replace(/[^a-z A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.nm103RenderingProviderLastOrOrganizationName.value) {
      this.billingInfo.controls.nm103RenderingProviderLastOrOrganizationName.setValue(this.billingInfo.controls.nm103RenderingProviderLastOrOrganizationName.value.trim().replace(/[^a-z A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.nm104RenderingProviderFirst.value) {
      this.billingInfo.controls.nm104RenderingProviderFirst.setValue(this.billingInfo.controls.nm104RenderingProviderFirst.value.trim().replace(/[^a-z A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.nm105RenderingProviderMiddle.value) {
      this.billingInfo.controls.nm105RenderingProviderMiddle.setValue(this.billingInfo.controls.nm105RenderingProviderMiddle.value.trim().replace(/[^a-z A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.n301BillingProviderAddr1.value) {
      this.billingInfo.controls.n301BillingProviderAddr1.setValue(this.billingInfo.controls.n301BillingProviderAddr1.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.n302BillingProviderAddr2.value) {
      this.billingInfo.controls.n302BillingProviderAddr2.setValue(this.billingInfo.controls.n302BillingProviderAddr2.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.n401BillingProviderCity.value) {
      this.billingInfo.controls.n401BillingProviderCity.setValue(this.billingInfo.controls.n401BillingProviderCity.value.trim().replace(/[^a-z ,-./'A-Z0-9]/g, ''));
    }
    if (!!this.billingInfo.controls.billingProviderFullName.value) {
      this.billingInfo.controls.billingProviderFullName.setValue(this.billingInfo.controls.billingProviderFullName.value.trim().replace(/[^a-z A-Z0-9,-]/g, ''));
    }
  }

  validateForm() {

    if (this.claimFormData.isEditClaim && this.billingInfo.controls['providerNPI'].value.length != 10) {
      this.billingInfo.controls['providerNPI'].setErrors({ maxlength: true });
    }

    if (this.billingInfo.invalid) {
      submitValidateAllFields.validateAllFields(this.billingInfo);
      return false;
    }
    return true;
  }
  omit_spl_charcter(number: string) {
    if (number)

      return number.replace(/[^0-9]+/ig, '');
    return number;
  }
  renderingProvider(e: any) {

    this.subjectService.setBillingProviderNPIChange(e.target.value);
  }

  stateSearch(term: string, item: AllStatesBySearchstring) {
    return stateSearch.StateSearch(term, item);
  }
  changeRenderProvider() {
     if(!!this.cacheService.localStorageGetItem(LocalStorageKey.userIpas) )
      {
        this.changeRenderProviderChildMethod(this.cacheService.localStorageGetItem(LocalStorageKey.userIpas));
      }
     else
     {
      this.claimReportServiceService.fetchUserIPAs().subscribe((res: any) => {
        this.changeRenderProviderChildMethod(res);
      });
     }
  }
  changeRenderProviderChildMethod(ipaCode:any){
    let dosFromDate:Date;
        let dosToDate:Date;
        if(this.claimFormData.isEditClaim){
          dosFromDate = new Date(this.claimFormData.claimViewModel.claimDosfrom);
          dosToDate = new Date(this.claimFormData.claimViewModel.claimDosto);
        }
        else if(this.claimFormData.isAddClaim){
          dosFromDate = new Date(this.claimFormData.profileMember.dOSFrom);
          dosToDate = new Date(this.claimFormData.profileMember.dOSTo);
        }
        let dialogRef = this.dialog.open(RenderingProviderModalComponent, {
          height: '650px',
          width: '1100px',
          autoFocus: false,
          restoreFocus: false,
          panelClass: 'custom-dialog-containers',
          data: {
            ipaCode: ipaCode,
            dosFromDate:dosFromDate,
            dosToDate:dosToDate
          },
          disableClose:true
        });
  
        dialogRef.afterClosed().subscribe((data: any) => {
          if (data.element) {
            let speciality = null;
            if (data.element.providerTaxonomy) {
              speciality = this.claimFormData?.taxonomyCode?.find(t => t.taxonomyCode == data.element.providerTaxonomy)?.taxonomyCode;
  
            }
            this.billingInfo.patchValue({
              nm104RenderingProviderFirst: data.element.providerFirstName,
              nm103RenderingProviderLastOrOrganizationName: data.element.providerLastName,
              nm105RenderingProviderMiddle: data.element.providerMiddleName,
              providerSpecialty: speciality,
              providerNPI: data.element?.providerNPI
              ,
            });
            if(this.claimFormData.isAddClaim){
              this.claimFormData.provider.renderingProvider.providerNPI = data.element?.providerNPI;
              this.claimFormData.renderingProviderInfo.providerNPI = data.element?.providerNPI;
            }
           
            this.subjectService.setBillingProviderNPIChange(data.element?.providerNPI);
            submitValidateAllFields.validateDisable(this.billingInfo, ["nm103RenderingProviderLastOrOrganizationName", "nm104RenderingProviderFirst", "nm105RenderingProviderMiddle", "providerSpecialty", "providerNPI"]);
          }
        });
  }



  changeBillingProvider() {
    //this.claimReportServiceService.fetchUserIPAs().subscribe((res: any) => {
      let dialogRef = this.dialog.open(BillingProviderModalComponent, {
        height: '650px',
        width: '1100px',
        autoFocus: false,
        restoreFocus: false,
        panelClass: 'custom-dialog-containers'   ,   disableClose:true     
      });
      dialogRef.afterClosed().subscribe((data: any) => {
        if (!!data && !!data.billingProviderDetails) {
          let resultData: BillingProviderResult = data.billingProviderDetails;
          if (resultData.payToDetails[0]?.paytoName) {
            this.validateBillingProvider(resultData.payToDetails[0]?.paytoName, resultData.payToDetails[0].payToNPI);
         
            this.validateFederaltaxId(resultData?.taxIdOrSSN);
            this.billingInfo.patchValue({
              billingProviderFullName: resultData.payToDetails[0]?.paytoName,
              n301BillingProviderAddr1: resultData.payToDetails[0]?.payToStreet,
              n302BillingProviderAddr2: resultData.payToDetails[0]?.payToSuite,
              n401BillingProviderCity: resultData.payToDetails[0]?.payToCity,
              n402BillingProviderState: resultData.payToDetails[0]?.payToState,
              n403BillingProviderZip: resultData.payToDetails[0]?.payToZip,
              billingProviderTelephone: resultData.contactPeople[0]?.contactPersonPhone,
              groupNPI: resultData.payToDetails[0]?.payToNPI
            });
          }
        }
      });
    //});
  }

  validateBillingProvider(newBillingProviderName: string, newNpi: string) {

    if(this.claimFormData.isAddClaim || this.claimFormData.isEditClaim){
      if(this.serviceFacilityForm.nm103LabFacilityName.value === this.billingInfo.controls['billingProviderFullName'].value){
        this.serviceFacilityForm.nm103LabFacilityName.setValue(newBillingProviderName);
        this.serviceFacilityForm.FacilityLocationNPI.setValue(newNpi);
      }
    }
  }
  validateFederaltaxId(newTaxId:string){
  
        this.federalTaxForm.federalTaxNumber.setValue(newTaxId);
      
  }
}
