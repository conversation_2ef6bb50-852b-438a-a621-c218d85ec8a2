import { Component, Input, OnInit } from '@angular/core';
import { ColDef, GridApi, IGetRowsParams } from 'ag-grid-community';
import * as moment from 'moment';
import { FilesFilterModel } from 'src/app/models/files-filter-model';
import { FileService } from 'src/app/services/file/file.service';
import { ClaimListForFileRender } from 'src/app/shared/Renderer/file/claimListForFileRenderer';
import { fileRender } from 'src/app/shared/Renderer/file/fileRender';
import { FileStatusRender } from 'src/app/shared/Renderer/file/fileStatusRenderer';
import { GRID_CONFIG, changeRowColor } from 'src/app/shared/config-ag-grid/config-grid';
import { DateFormatter, NameFormatter } from 'src/app/shared/functions/dateFormatterFunction';
import { SortDateValues, SortForTwoDates, SortStringValues } from 'src/app/shared/functions/sort-for-dates';
import { SubjectService } from 'src/app/shared/services/subject.service';

@Component({
  selector: 'app-ack997-ack999-electronic-files',
  templateUrl: './ack997-ack999-electronic-files.component.html',
  styleUrls: ['./ack997-ack999-electronic-files.component.scss'],
})
export class Ack997Ack999ElectronicFilesComponent implements OnInit {
  @Input() tabData;
  @Input() fileData;
  @Input() searchDates;
  gridOptions: any = JSON.parse(JSON.stringify(GRID_CONFIG.scrollConfigDetails.gridOptions));
  gridOptionsForClaim: any = JSON.parse(JSON.stringify(GRID_CONFIG.configDetails.gridOptions));
  public paginationPageSize = GRID_CONFIG.configDetails.pagination;
  claimRadioValue = '2';
  professionalCheckbox = {
    received: true,
    dispatched: true
  }
  duplicateFileData;
  showClaimList = false;
  claimsListData = {
    heading: '',
    data: []
  }
  fileCol: any = [];
  fileColms: ColDef[] = [
    { headerName: 'Control Number', minWidth: 180, resizable: true, field: 'controlNumber', sortable: true, tooltipField: "controlNumber", lockVisible: true },
    { headerName: 'File Name', minWidth: 400, resizable: true, field: 'fileName', sortable: true, tooltipField: "fileName", lockVisible: true },
    { headerName: 'Sender', minWidth: 180, resizable: true, field: 'sender', tooltipField: 'sender', sortable: true, lockVisible: true },
    { headerName: 'Receiver ', minWidth: 180, resizable: true, field: 'receiver', tooltipField: "receiver", sortable: true, lockVisible: true },
    {
      headerName: 'Date of Creation', minWidth: 180, resizable: true, field: 'dateOfCreation', lockVisible: true, sortable: true, cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.dateOfCreation)
      },
    },
    {
      headerName: 'Received Date', minWidth: 180, resizable: true, field: 'receivedDate', lockVisible: true, sortable: true, cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.receivedDate)
      },
    },
    { headerName: 'Age (Days)', minWidth: 180, resizable: true, sortable: true, field: 'ageInDays', tooltipField: "ageInDays", lockVisible: true },
    { headerName: 'Status', minWidth: 180, resizable: true, sortable: true, cellRenderer: FileStatusRender, tooltipField: "status", field: "status", lockVisible: true },
    { headerName: 'Service Type', minWidth: 180, resizable: true, sortable: true, field: 'serviceType', tooltipField: "serviceType", lockVisible: true },
    { headerName: 'Action', minWidth: 150, resizable: true, lockVisible: true, sortable: false, type: 'centerAligned', cellRenderer: fileRender, filter: false, hide: false, pinned: 'right' },
  ]
  claimFileCol: ColDef[] = [
    { headerName: 'Claim ID', minWidth: 180, resizable: true, field: 'claimNumber', tooltipField: "claimNumber", lockVisible: true },
    {
      headerName: 'Member', minWidth: 200, resizable: true, lockVisible: true, cellRenderer: params => {
        return NameFormatter(params.data, 'memLastName', 'memFirstName')
      }, tooltipValueGetter: params => { return NameFormatter(params.data, 'memLastName', 'memFirstName') }, filterValueGetter: params => {
        return NameFormatter(params.data, 'memLastName', 'memFirstName')
      }, comparator(valueA, valueB, nodeA, nodeB) {
        return SortStringValues(NameFormatter(nodeA.data, 'memLastName', 'memFirstName'), NameFormatter(nodeB.data, 'memLastName', 'memFirstName'))
      },
    },
    {
      headerName: 'Rendering Provider', minWidth: 200, resizable: true, sortable: true, lockVisible: true, cellRenderer: params => {
        return NameFormatter(params.data, 'providerLastName', 'providerFirstName')
      }, tooltipValueGetter: params => { return NameFormatter(params.data, 'providerLastName', 'providerFirstName') }, filterValueGetter: params => {
        return NameFormatter(params.data, 'providerLastName', 'providerFirstName')
      }, comparator(valueA, valueB, nodeA, nodeB) {
        return SortStringValues(NameFormatter(nodeA.data, 'providerLastName', 'providerFirstName'), NameFormatter(nodeB.data, 'providerLastName', 'providerFirstName'))
      },
    },
    { headerName: 'Payer ', minWidth: 200, resizable: true, field: 'payerName', tooltipField: "payerName", lockVisible: true },
    {
      headerName: 'DOS', minWidth: 200, resizable: true, field: 'dateOfCreation', lockVisible: true, sortable: true, cellRenderer: params => {
        return DateFormatter(params.data.dosFrom) + ' - ' + DateFormatter(params.data.dosTo)
      }, tooltipValueGetter: params => { return DateFormatter(params.data.dosFrom) + ' - ' + DateFormatter(params.data.dosTo) },
      filterValueGetter: params => { return DateFormatter(params.data.dosFrom) + ' - ' + DateFormatter(params.data.dosTo) },
      comparator(valueA, valueB, nodeA, nodeB) { return SortForTwoDates(nodeA.data.dosFrom, nodeB.data.dosFrom, nodeA.data.dosTo, nodeB.data.dosTo,) },
    },
    {
      headerName: 'Date Created', minWidth: 150, resizable: true, field: 'dateCreated', lockVisible: true, sortable: true, cellRenderer: params => {
        return DateFormatter(params.value)
      }, tooltipValueGetter: params => { return DateFormatter(params.value) }, comparator(valueA, valueB) {
        return SortDateValues(valueA, valueB)
      }, filterValueGetter: params => {
        return DateFormatter(params.data.dateCreated)
      },
    },
    {
      headerName: 'Claimed Amount', minWidth: 150, resizable: true, cellClass: 'align-right', field: 'amount', tooltipField: "amount", lockVisible: true, cellRenderer: params => {
        return '$' + params.value
      }, tooltipValueGetter: params => { return '$' + params.value }, filterValueGetter: params => {
        return '$' + params.data.amount
      },
    },
    { headerName: 'Claim Type', minWidth: 180, resizable: true, tooltipField: "claimType", field: "claimType", lockVisible: true },
    { headerName: 'Age (DOC)', minWidth: 150, resizable: true, field: 'ageOfDOC', tooltipField: "ageOfDOC", lockVisible: true },
    { headerName: 'Age (DOS)', minWidth: 150, resizable: true, field: 'ageOfDOS', tooltipField: "ageOfDOS", lockVisible: true },
    { headerName: 'Status', minWidth: 220, resizable: true, field: 'status', tooltipField: "status", lockVisible: true },
    { headerName: 'Action', minWidth: 150, resizable: true, lockVisible: true, sortable: false, type: 'centerAligned', cellRenderer: ClaimListForFileRender, filter: false, hide: false, pinned: 'right', lockPosition: "right" },
  ]

  currentTabTotalFileCount: number = 0;
  currentTabAddtionalFileCount: number = 0;
  isFilterGrid: boolean = false;
  requestBody: FilesFilterModel = new FilesFilterModel();
  public maxConcurrentDatasourceRequests = 2;
  public infiniteInitialRowCount = 1;
  gridApi!: GridApi;
  isCheckBoxEventFired: boolean = false;
  constructor(private fileService: FileService, private subjectService: SubjectService) {
    this.fileCol = this.fileColms;
  }

  ngOnInit(): void {
    //this.currentTabTotalFileCount = this.tabData.count- this.tabData.additionalCount;
    this.currentTabAddtionalFileCount = this.tabData.additionalCount;
    this.gridOptions.getRowStyle = this.changeRowColor;
    this.gridOptionsForClaim.getRowStyle = this.changeRowColor;

    this.gridOptions.cacheBlockSize = 50,
      this.gridOptions.maxBlocksInCache = 2
    this.subjectService.getClaimListForFile().subscribe((res) => {
      if (!!res) {
        this.subjectService.resetClaimListForFile();
        if (this.tabData.tabType == 0) {
          this.showClaimList = true;
          this.claimsListData = JSON.parse(JSON.stringify(res));
        }
      }
    })

    debugger
  }
  resetClaimList() {
    this.showClaimList = false;
    this.claimsListData = {
      heading: '',
      data: []
    }
  }
  changeRowColor(params) {
    return changeRowColor(params);
  }
  refreshGrid() {
    if (this.gridApi) {
      //this.currentTabTotalFileCount = this.tabData.count- this.tabData.additionalCount;
      this.onGridReady(this.gridApi);
    }

  }
  ngOnChanges(changes) {
    if (!!changes && !!changes.tabData && !!changes.tabData.previousValue && changes.tabData.previousValue.tabType != changes.tabData.currentValue.tabType) {
      if (!!this.gridOptions.columnApi) {
        this.gridOptions.columnApi.setColumnsVisible(this.tabData.columnsToShow, true);
        this.gridOptions.columnApi.setColumnsVisible(this.tabData.columnsToNotShow, false);
        this.gridOptions.columnApi.applyColumnState({
          state: [],
          defaultState: { sort: null },
        });
        if (!!this.gridOptions.api) {
          this.gridOptions.api.setFilterModel(null);
        }
      }
      this.resetVariables();
    }
    if (!!changes && !!changes.tabData && !!changes.tabData.currentValue && changes.tabData.currentValue.professionalCheckbox) {
      //this.duplicateFileData = JSON.parse(JSON.stringify(this.fileData));
    }
    if (!!changes && !!changes.fileData && !!changes.fileData.previousValue && !!changes.fileData.currentValue) {
      this.resetVariables();
      if (this.tabData.professionalCheckbox) {
        // this.duplicateFileData = JSON.parse(JSON.stringify(this.fileData));
      }
    }

  }


  resetVariables() {
    this.resetClaimList();
    this.claimRadioValue = '2';
    this.professionalCheckbox = {
      received: true,
      dispatched: true
    }
  }
  onGridReady(params: any) {
    this.gridApi = params;
    this.gridOptions.columnApi.setColumnsVisible(this.tabData.columnsToShow, true);
    this.gridOptions.columnApi.setColumnsVisible(this.tabData.columnsToNotShow, false);
    this.gridOptions.columnApi.applyColumnState({
      defaultState: { sort: null },
    });
    params.api.setDatasource(this.getDataSource());

  }

  private getDataSource() {
    debugger
    const dataSource = {
      rowCount: undefined,
      getRows: (params: IGetRowsParams) => {
        if (this.currentTabTotalFileCount >= params.startRow) {
          this.gridPayloadRequest(params);
          if (params.startRow == 0 && Object.keys(params.filterModel).length == 0 && Object.keys(params.sortModel).length == 0 && !this.isCheckBoxEventFired) {
            this.restFilterModel();
            this.getFilesBySearch(params);
          }
          else {
            if (this.isCheckBoxEventFired) {
              this.isCheckBoxEventFired = false;
            }
            this.restFilterModel();
            this.getGridFilterModelData(params)
          }
        }
      },
    };
    return { ...dataSource };
  }

  restFilterModel() {
    if (!!this.requestBody.controlNumber) {
      this.requestBody.controlNumber = null;
    }
    if (!!this.requestBody.fileName) {
      this.requestBody.fileName = null
    }
    if (!!this.requestBody.sender) {
      this.requestBody.sender = null;
    }
    if (!!this.requestBody.receiver) {
      this.requestBody.receiver = null
    }
    if (!!this.requestBody.dateOfCreation) {
      this.requestBody.dateOfCreation = null;
    }
    if (!!this.requestBody.ageInDays) {
      this.requestBody.ageInDays = null;
    }

    if (!!this.requestBody.status) {
      this.requestBody.status = null;
    }
  }

  gridPayloadRequest(params: any) {
    this.requestBody.dateFrom = this.searchDates.fromDate;
    this.requestBody.dateTo = this.searchDates.toDate;
    this.requestBody.uuid = JSON.parse(localStorage.getItem('uuid'));
    this.requestBody.sortingColumn = "IncomeFileLoggerID";
    this.requestBody.fileType = this.tabData.claimsRadio ? parseInt(this.claimRadioValue) : this.tabData.fileType;
    this.requestBody.index = params.startRow;
    this.requestBody.sortOrder = false;
  }

  getGridFilterModelData(params: any) {

    // Grid columns search filters without sorting
    if (Object.keys(params.filterModel).length > 0 && Object.keys(params.sortModel).length == 0) {

      this.gridFilterModel(params);
      console.log('Filter params', params);
      if (params.dateOfCreation.length > 2)
        this.getFilesBySearch(params);
    }
    //Grid columns search filters with sorting
    else if (Object.keys(params.filterModel).length > 0 && Object.keys(params.sortModel).length > 0) {
      this.gridFilterModel(params);
      console.log('Sort and filter params', params);
      this.GridSortModel(params);
      this.getFilesBySearch(params);
    }
    //Grid columns without search filters with sorting
    else if (Object.keys(params.filterModel).length == 0 && Object.keys(params.sortModel).length > 0) {
      this.GridSortModel(params);
      this.isFilterGrid = false;
      this.getFilesBySearch(params);
    }
    else {
      this.getFilesBySearch(params);
    }
  }


  gridFilterModel(params: any) {
    if (!!params.filterModel?.controlNumber?.filter) { // for grid contorlNumber column filters.
      this.requestBody.controlNumber = params.filterModel?.controlNumber?.filter,
        this.isFilterGrid = true;

    }
    if (!!params.filterModel?.fileName?.filter) { // for grid fileName column filters.
      this.isFilterGrid = true;
      this.requestBody.fileName = params.filterModel?.fileName?.filter
      //this.requestBody= requestBody;
    }
    if (!!params.filterModel?.sender?.filter) { // for grid sender column filters.
      this.isFilterGrid = true;
      this.requestBody.sender = params.filterModel?.sender?.filter;
      //this.requestBody= requestBody;
    }
    if (!!params.filterModel?.receiver?.filter) { //for grid receiver column filters. 
      this.isFilterGrid = true;
      this.requestBody.receiver = params.filterModel?.receiver?.filter
      //this.requestBody= requestBody;
    }
    if (!!params.filterModel?.dateOfCreation?.filter) { //  for grid dateOfCreation column filters. 
      this.isFilterGrid = true;
      this.requestBody.dateOfCreation = moment.utc(params.filterModel?.dateOfCreation?.filter, "MM/DD/YYYY").toDate();
      //this.requestBody= requestBody;
    }
    if (!!params.filterModel?.ageInDays?.filter) {  //  for grid ageInDays column filters. 
      this.isFilterGrid = true;
      this.requestBody.ageInDays = params.filterModel?.ageInDays?.filter
      //this.requestBody= requestBody;
    }
    if (!!params.filterModel?.status?.filter) {  //  for grid status column filters. 
      this.isFilterGrid = true;
      this.requestBody.status = params.filterModel?.status?.filter;
      //this.requestBody= requestBody;
    }
    else {  // direct grid columns api call
      // this.isFilterGrid = false;
      /// this.restFilterModel();
      // this.requestBody= requestBody;
      // this.getFiles(params);
    }
  }

  GridSortModel(params: any) {

    if (params.sortModel.length > 0) {
      this.requestBody.sortingColumn = params.sortModel[0].colId;
      this.requestBody.sortOrder = params.sortModel[0].sort == 'asc' ? true : false;
    }


  }

  onGridSizeChanged(e) {
    //  this.gridOptions.api.sizeColumnsToFit();
  }
  onGridSizeChangedForClaims(e) {
    this.gridOptionsForClaim.api.sizeColumnsToFit();
  }
  claimsRadioChanged(e) {
    this.isCheckBoxEventFired = true;
    this.requestBody.fileType = e.value;
    if (e.value == '2') {
      this.tabData.heading = 'Claim Acknowledgment 277 Files';
      this.requestBody.fileType = e.value;// this.tabData.claimsRadio? parseInt(this.claimRadioValue):this.tabData.fileType;
    } else {
      this.tabData.heading = 'Claim Pending 277 Files';
    }

    this.refreshGrid();
  }

  getFiles(params: any) {

    this.fileService.getFilesByType(this.requestBody).subscribe((res) => {
      this.fileData = !!res.content ? res.content : [];
      if ((!!res.content && (res.content || []).length > 0)) {
        this.fileData = res.content
        //this.duplicateFileData = this.fileData;
        if (this.isFilterGrid) {
          params.successCallback(this.fileData, Number(this.fileData.length));
        }
        else {
          if (this.currentTabTotalFileCount > 0 && this.fileData.length > 0) {
            params.successCallback(this.fileData, this.currentTabTotalFileCount);
          }
        }
      }
      else {
        params.successCallback(this.fileData, this.currentTabTotalFileCount);
      }
    })
  }

  getFilesBySearch(params: any) {

    if (this.requestBody.fileType == 1) {
      this.professionalClaimsCheckbox();
    }

    this.fileService.getFilesByTypeSearch(this.requestBody).subscribe((res) => {
      if (!!res.content) {
        this.fileData = (res.content.filesList || []);
        this.duplicateFileData = (res.content.filesList || []);
        this.currentTabTotalFileCount = res.content.totalRecordsCount;
        if (this.isFilterGrid && this.fileData.length) {
          params.successCallback(this.fileData, this.currentTabTotalFileCount);
        }
        else {

        }
      }
      params.successCallback(this.fileData, this.currentTabTotalFileCount);
    })
  }

  changeProfessionalClaim() {
    this.isCheckBoxEventFired = true;
    this.professionalClaimsCheckbox();
    this.refreshGrid();
  }

  professionalClaimsCheckbox() {
    if (this.professionalCheckbox.dispatched && this.professionalCheckbox.received) {
      this.requestBody.dispatched = 'Dispatched';
      this.requestBody.received = 'Received';
      this.tabData.heading = 'Received & Dispatched 837 Files';
      //this.fileData = JSON.parse(JSON.stringify(this.duplicateFileData));
    }
    else if (this.professionalCheckbox.dispatched && !this.professionalCheckbox.received) {
      this.requestBody.dispatched = 'Dispatched';
      this.requestBody.received = null;
      this.tabData.heading = 'Dispatched 837 Files';
    }
    else if (!this.professionalCheckbox.dispatched && this.professionalCheckbox.received) {
      this.requestBody.dispatched = null;
      this.requestBody.received = 'Received';
      this.tabData.heading = 'Received 837 Files';
    }
    else if (!this.professionalCheckbox.dispatched && !this.professionalCheckbox.received) {
      this.requestBody.dispatched = null;
      this.requestBody.received = null;
      this.tabData.heading = 'Received & Dispatched 837 Files';
      this.fileData = [];
    }
  }
}
