
import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { _MatTabGroupBase } from '@angular/material/tabs';
import { PayerInfoComponent } from './payer-info/payer-info.component';
import { DiagnosisNatureComponent } from './diagnosis-nature/diagnosis-nature.component';
import { CRCConditionasIndicatoeProfessional837s, ClaimListDetailsModel, ClaimsProfessional837Model, CreateClaimModel, DTPDateTimePeriodProfessional837, DependentProfessional837sModel, ICDCodesModel, InfoSourcekeyNavigationModel, OtherInfoProfessionalOtherInfoProfessionalModel, PerAdministrativeCommunicationContactProfessional837sModel, ServiceLineDetailsModel, ServiceLineProfessional837sModel, SubscriberkeyNavigationModel } from 'src/app/models/ClaimForm/claim.create.model';
import { PatientInfoComponent } from './patient-info/patient-info.component';
import { ServiceFacilityComponent } from './service-facility/service-facility.component';
import { SubscriberInfoComponent } from './subscriber-info/subscriber-info.component';
import { PatientDobComponent } from './patient-dob/patient-dob.component';
import { PatientAddressComponent } from './patient-address/patient-address.component';
import { PatientRelationshipComponent } from './patient-relationship/patient-relationship.component';
import { InsureInfoComponent } from './insure-info/insure-info.component';
import { InsureAddressComponent } from './insure-address/insure-address.component';
import { InsurePolicyGroupComponent } from './insure-policy-group/insure-policy-group.component';
import { PatientSignatureComponent } from './patient-signature/patient-signature.component';
import { OtherProviderComponent } from './other-provider/other-provider.component';
import { CurrentIllnessComponent } from './current-illness/current-illness.component';
import { OtherDateComponent } from './other-date/other-date.component';
import { InsureAuthorizedComponent } from './insure-authorized/insure-authorized.component';
import { OtherProviderPayerComponent } from './other-provider-payer/other-provider-payer.component';
import { OutsideLabComponent } from './outside-lab/outside-lab.component';
import { PatientCurrentOccuptionComponent } from './patient-current-occuption/patient-current-occuption.component';
import { HospitalizationServiceComponent } from './hospitalization-service/hospitalization-service.component';
import { ServiceLineClaimComponent } from './service-line-claim/service-line-claim.component';
import { BillingInfoComponent } from './billing-info/billing-info.component';
import { ProviderSignComponent } from './provider-sign/provider-sign.component';
import { FederalTaxComponent } from './federal-tax/federal-tax.component';
import { PatientAccountAcceptAssignmentComponent } from './patient-account-accept-assignment/patient-account-accept-assignment.component';
import { ChargesComponent } from './charges/charges.component';
import { ResubmissionAuthorizedNumComponent } from './resubmission-authorized-num/resubmission-authorized-num.component';
import { AdditionalClaimComponent } from './additional-claim/additional-claim.component';
import { ClaimInfoModel } from 'src/app/models/Providers/ClaimInfoModel';
import { PatientOtherInsureComponent } from './patient-other-insure/patient-other-insure.component';
import { PatientConditionComponent } from './patient-condition/patient-condition.component';
import { InfoSourceProfessional837 } from 'src/app/classmodels/ResponseModel/ClaimForm/ClaimPreviewModels/InfoSourceProfessional837';
import { SaveModalComponent } from 'src/app/modals/save-modal/save-modal.component';
import { MatDialog } from '@angular/material/dialog';
import { ClaimSearchMemberModel } from 'src/app/models/ClaimForm/claim.member.provider.line.model';
import { MediGroupComponent } from './medi-group/medi-group.component';
import { ClaimService } from 'src/app/services/ClaimForm/claim.service';
import { OtherSubscriberInfoProfessional837 } from 'src/app/classmodels/ResponseModel/ClaimForm/ClaimPreviewModels/OtherSubscriberInfoProfessional837';
import { SubjectService } from 'src/app/shared/services/subject.service';
import { ClaimStatusModel } from 'src/app/models/ClaimForm/change.claim.status.model';
import { IWindow } from './notes/notes.component';
import { Validators } from '@angular/forms';
import { submitValidateAllFields } from 'src/app/common/form.validators';
import { UpdateClaimComponent } from 'src/app/modals/update-claim/update-claim.component';
import { DublicateClaimComponent } from 'src/app/modals/dublicate-claim/dublicate-claim.component';
import { NavTabFromDetails, Tabs } from 'src/app/common/nav-constant';
import { CLAIM_TYPE, PREVILEGES, ValidationMsgs } from 'src/app/common/common-static';
import { ReasonToHoldComponent } from 'src/app/modals/reason-to-hold/reason-to-hold.component';
import { ResubmitClaimComponent } from 'src/app/components/popups/resubmit-claim/resubmit-claim.component';
import { NotificationService } from 'src/app/services/Notification/notification.service';
import { RejectedInComponent } from 'src/app/components/popups/rejected-in/rejected-in.component';
import { PendingDetailsComponent } from 'src/app/components/popups/pending-details/pending-details.component';
import { FileService } from 'src/app/services/file/file.service';
import { LinkedClaimsComponent } from 'src/app/components/popups/linked-claims/linked-claims.component';
import { Observable, distinctUntilChanged, forkJoin, map, of, switchMap } from 'rxjs';
import { DateFormatService } from 'src/app/shared/services/dateformat';
import { MemberValidateClaimMoveFromOpen, ValidateAddressResponse } from 'src/app/models/ClaimForm/Validation.Claim.From';
import { ValidateAddressComponent } from 'src/app/components/popups/validate-address/validate-address.component';
import { GlobalService } from 'src/app/shared/services/global.service';
import { ProviderManagementService } from 'src/app/services/ProviderManagement/provider-management.service';
import { AddressMismatchComponent } from 'src/app/shared/components/address-mismatch/address-mismatch.component';
import * as moment from 'moment';

@Component({
  selector: 'app-create-claim',
  templateUrl: './create-claim.component.html',
  styleUrls: ['./create-claim.component.scss']
})

export class CreateClaimComponent implements OnInit {

  claimModel: CreateClaimModel[] = [];
  @Input() allPlaceOfServices;
  icdIdentifier: string = 'ICD10';
  max_total_cpt = 13;
  @Input() claimFormData: ClaimInfoModel;

  @Output() closeClaim: EventEmitter<any> = new EventEmitter();
  @Input() SearchMemberModel: ClaimSearchMemberModel;
  @ViewChild(PayerInfoComponent, { static: true, })
  PayerInfoForm: PayerInfoComponent;

  @ViewChild(MediGroupComponent, { static: true, })
  MediGroup: MediGroupComponent;
  @ViewChild(ServiceFacilityComponent, { static: true, })
  ServiceFacilityForm: ServiceFacilityComponent;
  @ViewChild(SubscriberInfoComponent, { static: true, })
  SubscriberInfo: SubscriberInfoComponent;
  @ViewChild(PatientInfoComponent, { static: true, })
  PatientInfo: PatientInfoComponent;
  @ViewChild(PatientDobComponent, { static: true, })
  PatientDOBInfo: PatientDobComponent;
  @ViewChild(PatientAddressComponent, { static: true, })
  PatientAddressInfo: PatientAddressComponent;
  @ViewChild(PatientRelationshipComponent, { static: true, })
  PatientRelationshipInfo: PatientRelationshipComponent;
  @ViewChild(PatientSignatureComponent, { static: true, })
  PatientSignatureInfo: PatientSignatureComponent;

  @ViewChild(FederalTaxComponent, { static: true, })
  FederalTaxInfo: FederalTaxComponent;


  @ViewChild(PatientCurrentOccuptionComponent, { static: true, })
  PatientCurrentOccuptionInfo: PatientCurrentOccuptionComponent;


  @ViewChild(CurrentIllnessComponent, { static: true, })
  CurrentIllnessInfo: CurrentIllnessComponent;
  @ViewChild(OtherDateComponent, { static: true, })
  OtherDateInfo: OtherDateComponent;


  @ViewChild(InsureInfoComponent, { static: true, })
  InsureInfo: InsureInfoComponent;


  @ViewChild(InsurePolicyGroupComponent, { static: true, })
  InsurePolicyGroup: InsurePolicyGroupComponent;


  @ViewChild(InsureAddressComponent, { static: true, })
  InsureAddressInfo: InsureAddressComponent;

  @ViewChild(InsurePolicyGroupComponent, { static: true, })
  InsurePolicyGroupInfo: InsurePolicyGroupComponent;
  @ViewChild(InsureAuthorizedComponent, { static: true, })
  InsureAuthorizedInfo: InsureAuthorizedComponent;

  @ViewChild(OtherProviderComponent, { static: true, })
  OtherProviderInfo: OtherProviderComponent;

  @ViewChild(OtherProviderPayerComponent, { static: true, })
  OtherProviderPayerInfo: OtherProviderPayerComponent;

  @ViewChild(OutsideLabComponent, { static: true, })
  OutsideLabInfo: OutsideLabComponent;

  @ViewChild(HospitalizationServiceComponent, { static: true, })
  HospitalizationServiceInfo: HospitalizationServiceComponent;
  @ViewChild(DiagnosisNatureComponent, { static: true, })
  DiagnosisNature: DiagnosisNatureComponent;

  @ViewChild(ServiceLineClaimComponent, { static: true, })
  ServiceLineClaimInfo: ServiceLineClaimComponent;
  @ViewChild(BillingInfoComponent, { static: true, })
  BillingInfoInfo: BillingInfoComponent;

  @ViewChild(ProviderSignComponent, { static: true, })
  ProviderSignInfo: ProviderSignComponent;
  @ViewChild(ChargesComponent, { static: true, })
  ChargesInfo: ChargesComponent;
  @ViewChild(PatientAccountAcceptAssignmentComponent, { static: true, })
  PatientAccountAcceptAssignmentInfo: PatientAccountAcceptAssignmentComponent;

  @ViewChild(ResubmissionAuthorizedNumComponent, { static: true, })
  ResubmissionAuthorizedNumInfo: ResubmissionAuthorizedNumComponent;
  @ViewChild(AdditionalClaimComponent, { static: true, })
  AdditionalClaimInfo: AdditionalClaimComponent;

  @ViewChild(PatientOtherInsureComponent, { static: true, })
  PatientOtherInsureInfo: PatientOtherInsureComponent;

  @ViewChild(PatientConditionComponent, { static: true, })
  PatientConditionInfo: PatientConditionComponent;

    @ViewChild(PatientRelationshipComponent, { static: true, })
  patientRelationshipComponent: PatientRelationshipComponent;

  isShowUpdateBtn: boolean = false;
  recognition: any;
  isDeactivateClaimBtnShow: boolean = false;
  isAcceptedClaimBtnShow: boolean = false;
  isActivateClaimBtnShow: boolean = false;
  isOnHoldClainBtnShow: boolean = false;
  isUpdateClaimBtnShow: boolean = false;
 isToastShowing: boolean = false;
  constructor(private el: ElementRef,
    private claimService: ClaimService,
    private dialog: MatDialog,
    private subjectService: SubjectService,
    private notificationService: NotificationService,
    private fileService: FileService,
    private dateFormatService: DateFormatService,
    private providerManagementService: ProviderManagementService,
    private readonly globalService: GlobalService,
  ) {
    this.getPrivilegesByRole();
    const { webkitSpeechRecognition }: IWindow = <IWindow><unknown>window;
    this.recognition = new webkitSpeechRecognition();
    this.recognition.onresult = (event) => {
      this.el.nativeElement.querySelectorAll(".content")[0].innerText = event.results[0][0].transcript
    };
  }

  isNewClaim: boolean = false
  saveAllNotesEnable: boolean = false;
  ngOnInit(): void {
    if (this.claimFormData.isEditClaim) {
      this.isShowUpdateBtn = true;
    }
    this.claimFormCreation();
    setTimeout(() => {
      if (this.claimFormData.isEditClaim) {
        //this.initLoadForEditClaimSmaryStreetValidate();
      }
    }, 1000);
    if (this.claimFormData.isAddClaim) {
      this.isNewClaim = true;
      //this.initLoadSmartyStreetValidate();
    }

  }

  timeout(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  addNote() {
    this.saveAllNotesEnable = true;
    this.showNotes = true;
    let max: number = 0;
    this.claimFormData?.claimViewModel?.notes
      .forEach(element => {
        max = +element.notesPkId;
      });
    this.claimFormData?.claimViewModel?.notes?.push({
      notesPkId: max + 1, content: '', title: 'new',
      isRemoved:false
    })
  };

  saveNote(event) {
    const id = event.srcElement.parentElement.parentElement.getAttribute('id');
    const content = event.target.innerText;
    event.target.innerText = content;
    this.claimFormData?.claimViewModel?.notes.forEach(element => {
      if (element.notesPkId == id)
        element.description = content;
    });
  }
  showNotes: boolean = false;
deleteNote(event: Event) {
  
  const idAttr = (event.target as HTMLElement)
    .closest('[id]')         // find nearest ancestor with an “id” attribute
    ?.getAttribute('id');
  if (!idAttr) return;
  const id = Number(idAttr);

  const notes = this.claimFormData?.claimViewModel?.notes;
  if (!notes) return;

  
  const idx = notes.findIndex(n => n.notesPkId === id);
  if (idx < 0) return;

  
    notes[idx].isRemoved = !notes[idx].isRemoved;
  
}


  displayNotes() {
    this.saveAllNotesEnable = true;
    this.showNotes = !this.showNotes;
    if (this.claimFormData?.claimViewModel?.notes.length === 0) {
      this.addNote();
    }

  }
  record(event) {
    this.recognition.start();
    this.addNote();
  }
   getNotesToSave() {
    this.claimFormData?.claimViewModel?.notes.forEach(element => {
      if (element.title == 'new') {
        element.notesPkId = 0;
        
        element.title = '';
      }


    });
    return this.claimFormData?.claimViewModel?.notes;
  }

  saveAllNotes(){
   let data: CreateClaimModel = {
      insuranceCompany: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm109PayerIdCode'].value),
      fromDos: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,
      claimDosfrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,
      claimDosto: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.claimFormData?.claimViewModel?.claimDosto,
      claimsProfessional837: undefined,
      paidAmount: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['paidAmount'].value),
      displayMemberFirstnameName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),
      displayMemberLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),
      facilityId: null,
      formCreatedDate: this.dateFormatService.formatDate(new Date()),
      ipacode: this.claimFormData?.isAddClaim ? this.claimFormData?.profileMember?.ipaCode : this.claimFormData?.claimViewModel?.ipacode,
      lastModifiedBy: JSON.parse(localStorage.getItem('email')),
      lastModifiedByFirstName: null,
      lastModifiedDate: null,
      userName: localStorage.getItem('userName'),
      claimFormStatusCode: this.claimFormData?.claimViewModel?.claimFormStatusCode ? this.claimFormData?.claimViewModel?.claimFormStatusCode : 'ON',
      uuid: JSON.parse(localStorage.getItem('uuid')),
      claimType: 'CAP',
      totalCharges: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['totalCharges'].value.toString()),
      patientCtrlNo: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.patientCtrlNo,
      isActive: true,
      claimForm837Pid: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimForm837Pid,
      claimsProfessional837id: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837id,
      filename: null,
      uploadExcelWarningMessage: null,
      status: 'Active',
      source: 'Gateway',
      notes: this.getNotesToSave(),
      userFirstName: JSON.parse(localStorage.getItem('userFirstName')),
      userLastName: JSON.parse(localStorage.getItem('userLastName')),
      subscribeId: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),
      patientFirstName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),
      patientLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),
      patientMiddleName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm105PatientMiddle'].value),
      patientDob: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg02PatientBirthDate'].value),
      responseFileClaimMapings: this.claimFormData?.claimViewModel?.responseFileClaimMapings,
      resubmissionTrackerId: this.claimFormData?.claimViewModel?.resubmissionTrackerId,
      _277pprocessedOn: this.claimFormData?.claimViewModel?._277pprocessedOn,
      _835processedOnDate: this.claimFormData?.claimViewModel?._835processedOnDate,
      _999processedOnCh: this.claimFormData?.claimViewModel?._999processedOnCh
    }
    this.claimModel.push(data);
    this.claimService.updateNotes(this.claimModel).subscribe((res: any) => {
      if (res.statusCode == 200) {
        this.notificationService.showSuccess('Notes Updated Successfully', ValidationMsgs.success, 4000);
        this.showNotes = !this.showNotes;
      }
    });
  }
  onSubmit() {
    if (this.showNotes) {
      this.showNotes = false;
    }
    if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {
      this.claimModel = [];

      let requestBody = {
        addressLineOne: this.InsureAddressInfo.f.n301SubscriberAddr1.value,
        city: this.InsureAddressInfo.f.n401SubscriberCity.value,
        state: this.InsureAddressInfo.f.n402SubscriberState.value,
        givenZipcode: this.InsureAddressInfo.f.n403SubscriberZip.value,
      };
      let patientrequestbody = {
        addressLineOne: this.PatientAddressInfo.f.address1.value,
        city: this.PatientAddressInfo.f.city.value,
        state: this.PatientAddressInfo.f.state.value,
        givenZipcode: this.PatientAddressInfo.f.zip.value,
      };
      let initialRequest = this.providerManagementService.GetCorrectAddress(requestBody);
      let alternativeRequest = this.providerManagementService.GetCorrectAddress(patientrequestbody);
      forkJoin([initialRequest, alternativeRequest]).subscribe(
        (responses) => {
          let initialResponse = responses[0];
          let alternativeResponse = responses[1];

          if (this.isValidResponse(initialResponse)) {
            this.handleResponse(initialResponse);
          } else if (this.isValidResponse(alternativeResponse)) {
            this.handleResponse(alternativeResponse);
          } else {
            this.fallback();
          }
        },
        (error) => {
          this.fallback();
        }
      );
    }
    // else if (this.validateAllForms()) {
    //   this.fallback();
    // }
  }


  patientOrInsuredAddressIsDirtyCheck() {
    if (this.InsureAddressInfo.patientAddress.controls["n301SubscriberAddr1"].dirty ||
      this.InsureAddressInfo.patientAddress.controls["n401SubscriberCity"].dirty ||
      this.InsureAddressInfo.patientAddress.controls["n402SubscriberState"].dirty ||
      this.InsureAddressInfo.patientAddress.controls["n403SubscriberZip"].dirty ||
      this.PatientAddressInfo.patientAddressInfo.controls["address1"].dirty ||
      this.PatientAddressInfo.patientAddressInfo.controls["city"].dirty ||
      this.PatientAddressInfo.patientAddressInfo.controls["state"].dirty ||
      this.PatientAddressInfo.patientAddressInfo.controls["zip"].dirty
    ) {
      return true;
    }
    else {
      return true;
    }
  }

  isValidResponse(response: any): boolean {
    return !!response && !!response.content && response.content.isSmartAddressFound && !response.content.isAdderssCorrect;
  }

  handleResponse(response: any): void {
    const dialogRef = this.dialog.open(AddressMismatchComponent, {
      width: '800px',
      data: [
        { entity: 'Patient & Insured', address: response.content }
      ]
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.InsureAddressInfo.f.n301SubscriberAddr1.setValue(response.content.addressLineOne);
        this.InsureAddressInfo.f.n401SubscriberCity.setValue(response.content.city);
        this.InsureAddressInfo.f.n402SubscriberState.setValue(response.content.state);
        this.InsureAddressInfo.f.n403SubscriberZip.setValue(response.content.zipcode);
        this.PatientAddressInfo.f.address1.setValue(response.content.addressLineOne);
        this.PatientAddressInfo.f.city.setValue(response.content.city);
        this.PatientAddressInfo.f.state.setValue(response.content.state);
        this.PatientAddressInfo.f.zip.setValue(response.content.zipcode);
        this.PatientAddressInfo.patientAddressInfo.updateValueAndValidity();
        this.InsureAddressInfo.patientAddress.updateValueAndValidity();
        // this.PayerInfoForm.payerInfo.updateValueAndValidity();
        if (this.validateAllForms()) {
          this.claimModelConvertion();
          this.saveClaimData();
        }
      }
    });



    //   this.providerManagementService.GetCorrectAddress(requestBody).subscribe((res) => {
    //     if (!!res && !!res.content && res.content.isSmartAddressFound && !res.content.isAdderssCorrect) {
    //       const dialogRef = this.dialog.open(AddressMismatchComponent, {
    //         width: '800px',
    //         data: [
    //           {entity: 'Payer',
    //           address: res.content}
    //         ]
    //       })
    //       dialogRef.afterClosed().subscribe((confirmed: boolean) => {
    //         if (confirmed) {
    //           this.PayerInfoForm.f.n301PayerAddr1.setValue(res.content.addressLineOne);
    //           this.PayerInfoForm.f.n401PayerCity.setValue(res.content.city);
    //           this.PayerInfoForm.f.n402PayerState.setValue(res.content.state);
    //           this.PayerInfoForm.f.n403PayerZip.setValue(res.content.zipcode);
    //           this.PayerInfoForm.payerInfo.updateValueAndValidity();
    //           if (this.validateAllForms()) {
    //             this.claimModelConvertion();
    //             this.saveClaimData();
    //           }
    //         }
    //       });
    //     } else {
    //       this.claimModelConvertion();
    //       this.saveClaimData();
    //     }
    //   }, (err)=> {
    //     this.claimModelConvertion();
    //     this.saveClaimData();
    //   })

    // }
  }
  fallback(): void {
    this.claimModelConvertion();
    this.saveClaimData();
  }
  saveClaimData() {
    if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {
      if (this.claimFormData?.isAddClaim) {
        this.dublicateClaimCheck();
      } else {
        this.updateClaimData();
      }
    }
  }
  validateCPTCode() {
    for (let i = 0; i < this.ServiceLineClaimInfo.serviceLine.length; i++) {
      for (let j = 1; j < 5; j++) {
        if (!this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).errors && this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).errors?.required)
          this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors(null)
      }
    }
    /**/for (let i = 0; i < this.ServiceLineClaimInfo.serviceLine.length; i++) {
      for (let h = 1; h <= this.ServiceLineClaimInfo.serviceLine.length - 1; h++) {
        if (this.ServiceLineClaimInfo.serviceLine.at(i).get('cpt').value === this.ServiceLineClaimInfo.serviceLine.at(h).get('cpt').value) {
          for (let j = 1; j <= 4; j++) {
            for (let k = 1; k <= 4; k++) {
              if (i != h && this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value && this.ServiceLineClaimInfo.serviceLine.at(h).get('diagnosispointer' + k).value && this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value === this.ServiceLineClaimInfo.serviceLine.at(h).get('diagnosispointer' + k).value && this.ServiceLineClaimInfo.serviceLine.at(i).get('cpt').value === this.ServiceLineClaimInfo.serviceLine.at(h).get('cpt').value) {
                this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors({ dublicate: true })
                this.ServiceLineClaimInfo.serviceLine.at(h).get('diagnosispointer' + k).setErrors({ dublicate: true })
              }
            }
          }
        }
      }
    }
    let icDLength = 0;

    for (let i = 1; i < this.max_total_cpt; i++) {
      if (this.DiagnosisNature.icdInfo.controls['iCDInput' + i].value) {
        icDLength = i + 1;
      }
    }

    for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.length - 1; j++) {
      for (let i = 1; i <= 4; i++) {
        if (this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).value && (Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).value) >= icDLength || Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).value) === 0)) {
          this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer' + i).setErrors({ numberMax: true })
        }
      }
    }
  }
  validateAllForms() {
    let mediGroupValidate = this.MediGroup.validateForm();
    let payerValidate = this.PayerInfoForm.validateForm();
    let SubscriberValidate = this.SubscriberInfo.validateForm();
    let PatientDOBValidate = this.PatientDOBInfo.validateForm();
    let InsurePolicyValidate = this.InsurePolicyGroupInfo.validateForm();
    let FederalTaxValidate = this.FederalTaxInfo.validateForm();
    let patientRelationShip =this.patientRelationshipComponent.validateForm();
    let InsureAddressValidate = this.InsureAddressInfo.validateForm();
    let BillingInfoValidate = this.BillingInfoInfo.validateForm();
    let hospitalizationValidate = this.HospitalizationServiceInfo.setValiator();
    let patientCurrentOccuptionValidate = this.PatientCurrentOccuptionInfo.setValiator();
    this.validateDosFrom();
    if (this.claimFormData.isAddClaim) {
      this.validateCurrentIllnessDate();
    }
    this.validateCPTCode();
    let ServiceLineValidate = this.ServiceLineClaimInfo.validateForm();
    let otherDate = this.OtherDateInfo.validateControls();
    let currentIllness = this.CurrentIllnessInfo.validateControls();
    let otherProviderPayer = this.OtherProviderPayerInfo.validateForm();
    let otherProviderValidate = this.OtherProviderInfo.validateForm();
    let serviceFacility = this.ServiceFacilityForm.validateForm();
    let patientAddress = this.PatientAddressInfo.validateForm();
    let insureInfoValidate = this.InsureInfo.validateForm();
    let patientSignatuew = this.PatientSignatureInfo.validateForm();
    let resubmissionAuthorizedNumValidate = this.ResubmissionAuthorizedNumInfo.validateForm();
    let hospitalization = this.HospitalizationServiceInfo.setValiator();
    let currentOccupation = this.PatientCurrentOccuptionInfo.setValiator();
    this.PatientOtherInsureInfo.validateRequired(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['anotherHealthBenefitPlan'].value);
    let otherInsure = this.PatientOtherInsureInfo.validateControls();
    /// If acciendent date was selected patient control need to validate 
    if (this.ProviderSignInfo.providerSignInfo.controls['accident'].value && this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value === '') {
      this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].setErrors({ require: true });

    }
    else {
      this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].setErrors(null);
    }
    let PatientConditionInfoValidate = this.PatientConditionInfo.validateForm();
    if (this.DiagnosisNature.icdInfo.controls.iCDInput1.value == null || this.DiagnosisNature.icdInfo.controls.iCDInput1.value == undefined) {
      this.DiagnosisNature.icdInfo.controls.iCDInput1.setErrors({ required: true });
    }
    let diagnosisNatureValidate = this.DiagnosisNature.validateForm();

    this.validateAccidentDate();

    let patientControl = this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value === "AA";
    if (patientControl) {
      if (!this.ProviderSignInfo.providerSignInfo.controls['accident'].value) {
        this.ProviderSignInfo.providerSignInfo.controls['accident'].setErrors({ require: true });
      } else {
        if (this.ProviderSignInfo.providerSignInfo.controls.accident.hasError('require')) {
          delete this.ProviderSignInfo.providerSignInfo.controls.accident.errors['require'];
          this.ProviderSignInfo.providerSignInfo.controls.accident.updateValueAndValidity();
        }
      }
    }

    let ProviderSignValidate = this.ProviderSignInfo.validateForm();
    if (patientRelationShip && otherInsure && currentOccupation && hospitalization && patientSignatuew && otherProviderValidate && diagnosisNatureValidate && insureInfoValidate && resubmissionAuthorizedNumValidate && serviceFacility && patientAddress && otherDate && currentIllness && payerValidate && SubscriberValidate && PatientDOBValidate && FederalTaxValidate && InsurePolicyValidate && InsureAddressValidate && BillingInfoValidate && hospitalizationValidate && patientCurrentOccuptionValidate && ProviderSignValidate && ServiceLineValidate && PatientConditionInfoValidate && otherProviderPayer && mediGroupValidate) {
      return true;
    }
    if(!this.isToastShowing){
      this.isToastShowing = true;
      this.notificationService.showWarning('', `Please fill the mandatory claim form fields`, 4000);
      // Reset flag after toast timeout
      setTimeout(() => {
        this.isToastShowing = false;
      }, 4000);
    }
    return false;
  }
  dublicateClaimCheck() {
    this.claimService.dublicateClaimCheck(this.claimModel).subscribe((res: any) => {
      if (res?.length === 0) {
        this.saveClaim();
      }
      else {
        let claimIds = [];
        res?.forEach(element => {
          claimIds.push(element.patientCtrlNo)
        });
        this.dublicateModal({
          isClaimCreated: false,
          message: "There are some claims found with the same information you have recently filled. Would you like to continue to create this claim? Here is the list of claim(s) found:"
          , title: 'Duplicate Claim Found',
          claimIds: claimIds
        })
      }

    })
  }
  saveClaim() {

    this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
      if (res) {
        this.openDialog({
          isClaimCreated: true,
          message: "Claim Created Successfully!"
          , title: 'Create Claim Confirmation',
          claimId: res
        })
      }
    })
  }
  updateClaimData() {
    this.updateModelMapping();
    this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
      if (res) {
        this.updateClaimShowPopup({
          isClaimCreated: true,
          message: "Claim updated Successfully !"
          , title: 'Updated Claim Confirmation',
          claimId: res
        })
        this.subjectService.setCloseTabRefresh('View Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
      }
    })
  }
  updateClaimShowPopup(res) {
    const dialogRef = this.dialog.open(UpdateClaimComponent, {
      width: '450px',
      data: {
        res
      }
    });

    dialogRef.afterClosed().subscribe((confirmed: number) => {
      if (res.isClaimCreated && confirmed) {
        this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
      }
    });
  }

  dublicateModal(res: any) {
    const dialogRef = this.dialog.open(DublicateClaimComponent, {
      maxHeight: '100vh',
      maxWidth: '60vw',
      minWidth: '40vw',
      data: {
        res
      }
    });

    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.saveClaim();
      }
    });
  }
  openDialog(res: any) {
    const dialogRef = this.dialog.open(SaveModalComponent, {
      maxHeight: '100vh',
      maxWidth: '60vw',
      minWidth: '40vw',
      data: {
        res
      }
    });

    dialogRef.afterClosed().subscribe((confirmed: number) => {
      if (res.isClaimCreated && confirmed) {
        this.claimFormData.provider.billingProvider.billingProviderNPI = this.BillingInfoInfo.billingInfo.controls['groupNPI'].value;
        if (this.claimFormData.provider.referringProvider) {
          this.claimFormData.provider.referringProvider.nPINumber = this.OtherProviderPayerInfo.otherPayerInfo.controls['npi'].value;
        }
        if (this.claimFormData.provider.supervisingProvider) {
          this.claimFormData.provider.supervisingProvider.providerNPI = this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value;
        }
        this.claimFormData.provider.billingProvider.billingProviderZip = this.BillingInfoInfo.billingInfo.controls['n403BillingProviderZip'].value;
        this.claimFormData.memberResult.dateOfBirth = this.InsurePolicyGroup.insurencePolicyInfo.controls['insuredDateOfBirth'].value;
        this.claimFormData.memberResult.gender = this.InsurePolicyGroup.insurencePolicyInfo.controls['genderCode'].value;
        this.claimFormData.memberResult.insuranceCompanyName = this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value;
        this.claimFormData.memberResult.addressLine1 = this.PayerInfoForm.payerInfo.controls['n301PayerAddr1'].value;
        this.claimFormData.memberResult.city = this.PayerInfoForm.payerInfo.controls['n401PayerCity'].value;
        this.claimFormData.memberResult.state = this.PayerInfoForm.payerInfo.controls['n402PayerState'].value;
        this.claimFormData.memberResult.zipCode = this.PayerInfoForm.payerInfo.controls['n403PayerZip'].value;
        this.claimFormData.provider.billingProvider.billingProviderTaxonomy = this.BillingInfoInfo.billingInfo.controls['providerSpecialty'].value;
        this.closeClaim.emit(confirmed);
      }
      else if (confirmed) {
        this.saveClaim();
      }
    });
  }
  claimFormCreation() {
    this.PayerInfoForm.createForm();
    this.PatientInfo.createForm();
    this.PatientDOBInfo.createForm();
    this.InsureInfo.createForm();
    this.PatientAddressInfo.createForm();
    this.PatientRelationshipInfo.createForm();
    this.PatientSignatureInfo.createForm();
    this.InsureAddressInfo.createForm();
    this.InsurePolicyGroupInfo.createForm();
    this.PatientOtherInsureInfo.createForm();
    this.PatientConditionInfo.createForm();
    this.SubscriberInfo.createForm();
    this.OtherProviderInfo.createForm();
    this.ServiceFacilityForm.createForm();
    this.CurrentIllnessInfo.createForm();
    this.OtherDateInfo.createForm();
    this.InsureAuthorizedInfo.createForm();
    this.OtherProviderPayerInfo.createForm();
    this.OutsideLabInfo.createForm();
    this.PatientCurrentOccuptionInfo.createForm();
    this.HospitalizationServiceInfo.createForm();
    this.ServiceLineClaimInfo.createForm();
    this.DiagnosisNature.createForm();
    this.BillingInfoInfo.createForm();
    this.ProviderSignInfo.createForm();
    this.FederalTaxInfo.createForm();
    this.PatientAccountAcceptAssignmentInfo.createForm();
    this.ChargesInfo.createForm();
    this.ResubmissionAuthorizedNumInfo.createForm();
    this.AdditionalClaimInfo.createForm();
    this.MediGroup.createForm();
    this.InsurePolicyGroup.createForm();

    this.OtherProviderInfo.otherProviderInfo.controls['referringProviderLastName'].valueChanges.pipe(distinctUntilChanged()).subscribe((referringProviderLastName: any) => {
      if (!!referringProviderLastName && referringProviderLastName.length > 0) {
        this.OtherProviderPayerInfo.otherPayerInfo.controls.npi.addValidators(Validators.required);
      } else {
        this.OtherProviderPayerInfo.otherPayerInfo.controls.npi.removeValidators(Validators.required);
      }
      this.OtherProviderPayerInfo.otherPayerInfo.controls.npi.updateValueAndValidity();
    })
    this.OtherProviderPayerInfo.otherPayerInfo.controls['npi'].valueChanges.pipe(distinctUntilChanged()).subscribe((npi: any) => {
      if (!!npi && npi.length > 0) {
        this.OtherProviderInfo.otherProviderInfo.controls.referringProviderLastName.addValidators(Validators.required);
      } else {
        this.OtherProviderInfo.otherProviderInfo.controls.referringProviderLastName.removeValidators(Validators.required);
      }
      this.OtherProviderInfo.otherProviderInfo.controls.referringProviderLastName.updateValueAndValidity();
    })
  }
  isShowInsureDetails: any = {
    isCopyInsure: false,
    insureAddress: {
      n301SubscriberAddr1: '',
      n302SubscriberAddr2: '',
      n401SubscriberCity: '',
      n402SubscriberState: '',
      n403SubscriberZip: '',
      per04SubscriberPhoneNo: '',
      nm104SubscriberFirst: '',
      nm105SubscriberMiddle: '',
      nm103SubscriberLastOrOrganizationName: '',
      insuredDateOfBirth: '',
      genderCode: ''
    }



  };
  setInsureData(e: boolean) {


    let insureAddress = {
      isCopyInsure: e,
      n301SubscriberAddr1: this.InsureAddressInfo.patientAddress.controls['n301SubscriberAddr1'].value,
      n302SubscriberAddr2: this.InsureAddressInfo.patientAddress.controls['n302SubscriberAddr2'].value,
      n401SubscriberCity: this.InsureAddressInfo.patientAddress.controls['n401SubscriberCity'].value,
      n402SubscriberState: this.InsureAddressInfo.patientAddress.controls['n402SubscriberState'].value,
      n403SubscriberZip: this.InsureAddressInfo.patientAddress.controls['n403SubscriberZip'].value,
      per04SubscriberPhoneNo: this.InsureAddressInfo.patientAddress.controls['per04SubscriberPhoneNo'].value,
    }
    let insureInfo = {
      isCopyInsure: e,
      nm104SubscriberFirst: this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value,
      nm105SubscriberMiddle: this.InsureInfo.insureInfo.controls['nm105SubscriberMiddle'].value,
      nm103SubscriberLastOrOrganizationName: this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value,

    }

    let insureDob = {
      isCopyInsure: e,
      insuredDateOfBirth: this.InsurePolicyGroup.insurencePolicyInfo.controls['insuredDateOfBirth'].value,
      genderCode: this.InsurePolicyGroup.insurencePolicyInfo.controls['genderCode'].value
    }
    this.subjectService.setInsureAddress(insureAddress);
    this.subjectService.setInsureInfo(insureInfo);
    this.subjectService.setInsureDob(insureDob);
  }
  claimModelConvertion() {
    let data: CreateClaimModel = {
      insuranceCompany: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm109PayerIdCode'].value),
      fromDos: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,
      claimDosfrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.claimFormData?.claimViewModel?.claimDosfrom,
      claimDosto: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.claimFormData?.claimViewModel?.claimDosto,
      claimsProfessional837: undefined,
      paidAmount: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['paidAmount'].value),
      displayMemberFirstnameName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),
      displayMemberLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),
      facilityId: null,
      formCreatedDate: this.dateFormatService.formatDate(new Date()),
      ipacode: this.claimFormData?.isAddClaim ? this.claimFormData?.profileMember?.ipaCode : this.claimFormData?.claimViewModel?.ipacode,
      lastModifiedBy: JSON.parse(localStorage.getItem('email')),
      lastModifiedByFirstName: null,
      lastModifiedDate: null,
      userName: localStorage.getItem('userName'),
      claimFormStatusCode: this.claimFormData?.claimViewModel?.claimFormStatusCode ? this.claimFormData?.claimViewModel?.claimFormStatusCode : 'ON',
      uuid: JSON.parse(localStorage.getItem('uuid')),
      claimType: 'CAP',
      totalCharges: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['totalCharges'].value.toString()),
      patientCtrlNo: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.patientCtrlNo,
      isActive: true,
      claimForm837Pid: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimForm837Pid,
      claimsProfessional837id: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837id,
      filename: null,
      uploadExcelWarningMessage: null,
      status: 'Active',
      source: 'Gateway',
      notes: this.getNotesToSave(),
      userFirstName: JSON.parse(localStorage.getItem('userFirstName')),
      userLastName: JSON.parse(localStorage.getItem('userLastName')),
      subscribeId: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),
      patientFirstName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),
      patientLastName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),
      patientMiddleName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm105PatientMiddle'].value),
      patientDob: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg02PatientBirthDate'].value),
      responseFileClaimMapings: this.claimFormData?.claimViewModel?.responseFileClaimMapings,
      resubmissionTrackerId: this.claimFormData?.claimViewModel?.resubmissionTrackerId,
      _277pprocessedOn: this.claimFormData?.claimViewModel?._277pprocessedOn,
      _835processedOnDate: this.claimFormData?.claimViewModel?._835processedOnDate,
      _999processedOnCh: this.claimFormData?.claimViewModel?._999processedOnCh
    }
    data.claimsProfessional837 = this.claimProfessionalModel();
    data.claimsProfessional837.claimListDetails = []
    data.claimsProfessional837.claimListDetails.push(this.claimListDetailsModel());
    data.claimsProfessional837.subscriberkeyNavigation = this.subscriberkeyNavigationModel();
    data.claimsProfessional837.serviceLineProfessional837s = this.servicelineProfession();
    data.claimsProfessional837.otherInfoProfessionalOtherInfoProfessional = this.otherInforProfressionalModel();
    data.claimsProfessional837.otherSubscriberInfoProfessional837s = this.otherSubscriberInfoProfessional837s();
    this.claimModel.push(data);
  }
  claimProfessionalModel() {
    let claimsProfessional837Model: ClaimsProfessional837Model = {
      claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,
      ref02PayerClaimControlNumber: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['referenceNumber'].value),
      ref02PriorAuthorizationNumber: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['priorAuthorisationNumber'].value),
      subscriberkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkey,
      dtp03HospitalizationDischargeDate: this.controlValueCheck(this.HospitalizationServiceInfo.hospitalizationinfo.controls['dtp03HospitalizationDischargeDate'].value),
      dtp03HospitalizationAdmissionDate: this.controlValueCheck(this.HospitalizationServiceInfo.hospitalizationinfo.controls['dtp03HospitalizationAdmissionDate'].value),
      dtp03LastWorkedDate: this.controlValueCheck(this.PatientCurrentOccuptionInfo.patientOccupationInfo.controls['patientUnableToWorkInCurrentOccupationFrom'].value),
      dtp03WorkReturnDate: this.controlValueCheck(this.PatientCurrentOccuptionInfo.patientOccupationInfo.controls['patientUnableToWorkInCurrentOccupationTo'].value),
      nm107ReferringProviderLastSuffix: null,
      nm105ReferringProviderLastMiddle: this.controlValueCheck(this.OtherProviderInfo.otherProviderInfo.controls['referringProviderMiddleName'].value),
      nm104ReferringProviderLastFirst: this.controlValueCheck(this.OtherProviderInfo.otherProviderInfo.controls['referringProviderFirstName'].value),
      nm103ReferringProviderLastName: this.controlValueCheck(this.OtherProviderInfo.otherProviderInfo.controls['referringProviderLastName'].value),
      nm109ReferringProviderIdentifier: this.controlValueCheck(this.OtherProviderPayerInfo.otherPayerInfo.controls['npi'].value),
      hcp14PolicyComplianceCode: null,
      clm01PatientControlNo: this.claimFormData?.isAddClaim ? null : this.controlValueCheck(this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value),
      dtp03OnsetofCurrentIllnessInjuryDate: this.controlValueCheck(this.CurrentIllnessInfo.currentillnessInfo.controls['dtp03OnsetofCurrentIllnessInjuryDate'].value),
      clm1103RelatedCausesCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToOtherAccident'].value),
      clm1102RelatedCausesCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value),
      clm1101RelatedCausesCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToEmp'].value),
      clm1104AutoAccidentStateCode: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccidentState'].value),
      clm0503ClaimFrequencyCode: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['resubmissionCode'].value),
      clm06SupplierSignatureIndicator: this.controlValueCheck(this.PatientSignatureInfo.patientAuthorizedForm.controls['patientOrAuthorizedSignature'].value),
      clm08BenefitsAssignmentCertIndicator: this.controlValueCheck(this.InsureAuthorizedInfo.insureAuthorizedForm.controls['authorizedPersonsSignature'].value),
      nm109RenderingProviderIdentifier: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerNPI'].value),
      nm104RenderingProviderFirst: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm104RenderingProviderFirst'].value),
      nm105RenderingProviderMiddle: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm105RenderingProviderMiddle'].value),
      nm103RenderingProviderLastOrOrganizationName: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm103RenderingProviderLastOrOrganizationName'].value),
      nm109LabFacilityIdentifier: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['FacilityLocationNPI'].value),
      nm103LabFacilityName: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['nm103LabFacilityName'].value),
      n403LabFacilityZip: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n403LabFacilityZip'].value),
      n402LabFacilityState: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n402LabFacilityState'].value),
      n401LabFacilityCity: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n401LabFacilityCity'].value),
      n302LabFacilityAddress2: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n302LabFacilityAddress2'].value),
      n301LabFacilityAddress1: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['n301LabFacilityAddress1'].value),
      clm0501PlaceOfServiceCode: this.claimFormData?.isAddClaim ? this.claimFormData?.placeOfServiceCode : this.ServiceLineClaimInfo.serviceLineInfo.controls['serviceLines'].value[0].locationOfService,
      prv03ProviderTaxonomyCode: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerSpecialty'].value),
      dtp03LastSeenDate: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['dateOrConsultation'].value),
      dtp03InitialTreatmentDate: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['dateInitial'].value),
      nm104SupervisingPhysicianFirst: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianFirstname'].value),
      nm103SupervisingPhysicianLastName: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianLastname'].value),
      nm105SupervisingPhysicianMiddle: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianMiddlename'].value),
      nm109SupervisingPhysicianIdentifier: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value),
      dtp03AccidentDate: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['accident'].value),
      subscriberkeyNavigation: undefined,
      serviceLineProfessional837s: [],
      claimListDetails: undefined,
      otherSubscriberInfoProfessional837s: [],
      n301AmbulancePickupAddress1: null,
      n302AmbulancePickupAddress2: null,
      n401AmbulancePickupCity: null,
      n402AmbulancePickupState: null,
      n403AmbulancePickupZip: null,
      n404AmbulancePickupCountryCode: null,
      n407AmbulancePickupCountrySubdivisionCode: null,
      n103AmbulanceDropOffLocation: null,
      n301AmbulanceDropOffAddress1: null,
      n302AmbulanceDropOffAddress2: null,
      n401AmbulanceDropOffCity: null,
      n402AmbulanceDropOffState: null,
      n403AmbulanceDropOffZip: null,
      n404AmbulanceDropOffCountryCode: null,

      n407AmbulanceDropOffCountrySubdivisionCode: null,
      otherInfoProfessionalOtherInfoProfessional: undefined,
      amt02PatientAmountPaid: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['paidAmount'].value),
      ref02MammographyCertificationNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['mammography'].value),
      clm07PlanParticipationCode: this.controlValueCheck(this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['clm08BenefitsAssignmentCertIndicator'].value)
    }

    return claimsProfessional837Model;
  }
  otherSubscriberInfoProfessional837s() {
    let otherInfoProfessionalOtherInfoProfessionalModel: OtherSubscriberInfoProfessional837 = {
      claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,
      amt02NonCoveredChargedAmount: null,
      amt02PayorAmountPaid: null,
      amt02RemainingPatientLiability: null,
      dtp03OtherPayerPaymentDate: null,
      moa01ReimbursementRate: null,
      moa02HcpcsPayableAmount: null,
      moa03ClaimPaymentRemarkCode: null,
      moa04ClaimPaymentRemarkCode2: null,
      moa05ClaimPaymentRemarkCode3: null,
      moa06ClaimPaymentRemarkCode4: null,
      moa07ClaimPaymentRemarkCode5: null,
      claimskeyNavigation: undefined,
      moa08EndStageRenalDiseasePaymntAmnt: null,
      moa09NonPayableProfessionComponentBill: null,
      n301OtherInsuredAddress: null,
      n301OtherPayerAddress1: null,
      n302OtherInsuredAddress2: null,
      n302OtherPayerAddress2: null,
      n401OtherInsuredCity: null,
      n401OtherPayerCity: null,
      n402OtherInsuredState: null,
      n402OtherPayerState: null,
      n403OtherInsuredZip: null,
      n403OtherPayerZip: null,
      n404OtherInsuredCountryCode: null,
      n404OtherPayerCountryCode: null,
      n407OtherInsuredCountrySubdivision: null,
      n407OtherPayerCountrySubdivision: null,
      nm101OtherBillingProviderEntityIdCode: null,
      nm101OtherProviderEntityIdCode: null,
      nm101OtherRenderingProviderEntityIdCode: null,
      nm101OtherServiceLocationEntityIdCode: null,
      nm101OtherSupervisorEntityIdCode: null,
      nm102OtherBillingProviderEntityTypeQlfr: null,
      nm102OtherInsuredEntityTypeQlfr: null,
      nm102OtherProviderEntityTypeQlfr: null,
      nm102OtherRenderingProviderEntityTypeQlfr: null,
      nm102OtherServiceLocationEntityTypeQlfr: null,
      nm102OtherSupervisorEntityTypeQlfr: null,
      nm103OtherPayerOrganizationName: null,
      nm107OtherInsuredSuffix: null,
      nm108OtherInsuredIdQlfr: null,
      nm108OtherPayerCodeQlfr: null,
      nm109OtherInsuredId: null,
      nm109OtherPayerPrimaryId: null,
      oi03BenefitsAssignmentCertIndicator: null,
      oi04PatientSignatureSourceCode: null,
      oi06ReleaseOfInformationCode: null,
      otherSubscriberInfokey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.otherSubscriberInfoProfessional837s[0] === undefined ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.otherSubscriberInfoProfessional837s[0].otherSubscriberInfokey,
      ref02OtherInsuredSecondaryId: null,
      ref02OtherPayerClaimAdjustmentIndicator: null,
      ref02OtherPayerClaimControlNo: null,
      ref02OtherPayerPriorAuthorizationNo: null,
      ref02OtherPayerReferralNo: null,
      sbr01PayerResponsibSeqNoCode: null,
      sbr02IndividualRelationshipCode: null,
      sbr05InsuranceTypeCode: null,
      sbr09ClaimFilingIndicatorCode: this.controlValueCheck(this.MediGroup.mediInfo.controls['insuranceType'].value),
      sbr03ReferenceIdentification: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredPolicy'].value),
      nm104OtherInsuredFirst: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredFirstName'].value),
      nm105OtherInsuredMiddle: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredMiddleInitial'].value),
      nm103OtherInsuredLastName: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredLastName'].value),
      sbr04OtherInsuredGroupName: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['insurePlanProgram'].value)
    }
    let other: OtherSubscriberInfoProfessional837[] = [];
    other.push(otherInfoProfessionalOtherInfoProfessionalModel)
    return other;
  }
  otherInforProfressionalModel() {
    //10, 8,33,
    let otherInfoProfessionalOtherInfoProfessionalModel: OtherInfoProfessionalOtherInfoProfessionalModel = {
      otherInfoProfessionalId: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.otherInfoProfessionalOtherInfoProfessional?.otherInfoProfessionalId,
      billingGroupIdQlfr: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['idQual'].value),
      billingGroupNumber: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerId'].value),
      dateOfCurrentIllnessQlfr: this.controlValueCheck(this.CurrentIllnessInfo.currentillnessInfo.controls['dateOfCurrentIllnessQlfr'].value),
      icdidentifier: this.icdIdentifier,
      insurancePlanName: null,
      insuredOtherHealthBenefitPlan: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['anotherHealthBenefitPlan'].value),
      insuredSignature: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['anotherHealthBenefitPlan'].value),
      outsideLab: this.controlValueCheck(this.OutsideLabInfo.otSideLabInfo.controls['outsideLab'].value),
      patientConditionRelatedToAutoAccident: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccident'].value),
      patientConditionRelatedToAutoAccidentState: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToAutoAccidentState'].value),
      patientConditionRelatedToEmp: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToEmp'].value),
      patientConditionRelatedToOtherAccident: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['patientConditionRelatedToOtherAccident'].value),
      patientOrAuthorizedSignature: this.controlValueCheck(this.PatientSignatureInfo.patientAuthorizedForm.controls['patientOrAuthorizedSignature'].value),
      patientOrAuthorizedSignatureDate: this.controlValueCheck(this.PatientSignatureInfo.patientAuthorizedForm.controls['patientOrAuthorizedSignatureDate'].value),
      patientOtherDate: this.controlValueCheck(this.OtherDateInfo.otherInfo.controls['otherDateQual'].value),
      renderingProviderPin: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['renderingProviderPin'].value),
      patientOtherDateQlfr: this.controlValueCheck(this.OtherDateInfo.otherInfo.controls['otherQual'].value),
      reservedNuccuse0801: this.controlValueCheck(this.PatientRelationshipInfo.patientRelationShip.controls['NUCC1'].value),
      reservedNuccuse0802: this.controlValueCheck(this.PatientRelationshipInfo.patientRelationShip.controls['NUCC2'].value),
      otherInsuredNuccuse01: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredNuccuse01'].value),
      otherInsuredNuccuse02: this.controlValueCheck(this.PatientOtherInsureInfo.patientOtherInsurence.controls['otherInsuredNuccuse02'].value),
      otherClaimIdNuccuse: null,
      additionalClaimInfoNucc: this.controlValueCheck(this.AdditionalClaimInfo.additionalClaim.controls['additionalClaimInformation'].value),
      patientConditionNuccuse: null,
      claimsCodes: this.controlValueCheck(this.PatientConditionInfo.patientConditionInfo.controls['claimsCodes'].value),
      federalTaxIdType: null,
      physicianQlfr: this.controlValueCheck(this.OtherProviderPayerInfo.otherPayerInfo.controls['physicianQual'].value),
      physicianDescription: this.controlValueCheck(this.OtherProviderPayerInfo.otherPayerInfo.controls['physicianDecription'].value),
      resubmissionCode: this.controlValueCheck(this.ResubmissionAuthorizedNumInfo.resubmissionInfo.controls['resubmissionCode'].value),
      reservedNuccuse30: this.controlValueCheck(this.ChargesInfo.chargesInfo.controls['reservedForNUCC'].value)
    }
    return otherInfoProfessionalOtherInfoProfessionalModel;
  }

  servicelineProfession() {
    let serviceLineProfessional837sModel: ServiceLineProfessional837sModel[] = []

    for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.controls.length - 1; j++) {
      let subscriberkeyNavigationModel: ServiceLineProfessional837sModel = {
        dOSFrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value),
        dOSTo: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value),
        n301ServiceFacilityAddress1: null,
        n302ServiceFacilityAddress2: null,
        n401ServiceFacilityCity: null,
        n402ServiceFacilityState: null,
        n403ServiceFacilityZip: null,
        nm103RenderingProviderNameLastOrg: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm103BillingProviderMiddle'].value),
        nm109RenderingProviderId: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('jRenderingProviderId').value),
        nm109ServiceFacilityId: null,
        sv10101ProductServiceIdQualifier: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('proceduceC').value),
        sv10102ProcedureCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('cpt').value),
        sv10103ProcedureModifier1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m1').value),
        sv10104ProcedureModifier2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m2').value),
        sv10105ProcedureModifier3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m3').value),
        sv10106ProcedureModifier4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m4').value),
        sv102LineItemChargeAmount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('unitCharges').value.toString()),
        sv104ServiceUnitCount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dayUnitChanges').value).toString(),
        sv105PlaceOfServiceCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('locationOfService').value),
        sv10701DiagnosisCodePointer1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer1').value),
        sv10702DiagnosisCodePointer2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer2').value),
        sv10703DiagnosisCodePointer3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer3').value),
        sv10704DiagnosisCodePointer4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer4').value),
        sv109EmergencyIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('emg').value),
        sv111EpsdtIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ePSDT').value),
        nm103OrderingProviderLastName: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingLastname'].value),
        nmSupervisingProviderId2: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingId'].value),
        claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,
        nm104OrderingProviderFirst: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingFirstname'].value),
        nm105OrderingProviderMiddle: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingMiddlename'].value),
        nm109OrderingProviderId: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['orderingNpi'].value),
        nmOrderingProviderId2: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['orderingId'].value),
        ref02ReferringCliaNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['cLIA'].value),
        svServiceFacilityId2: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['FacilityId'].value),
        ref02MammographyCertificationNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['mammography'].value),
        nm103SupervisingProviderLastName: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianLastname'].value) : null,
        nm105SupervisingProviderMiddle: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianMiddlename'].value) : null,
        nm104SupervisingProviderFirst: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianFirstname'].value) : null,
        nm109SupervisingProviderId: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value) : null,
        DOSFrom: null,
        DOSTo: null,
        serviceLinekey: 0,

      }
      subscriberkeyNavigationModel.dtp03ServiceDate = this.dateChangeToString(this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value), this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value));
      serviceLineProfessional837sModel.push(subscriberkeyNavigationModel)
    }
    return serviceLineProfessional837sModel;
  }
  subscriberkeyNavigationModel() {
    let subscriberkeyNavigationModel: SubscriberkeyNavigationModel = {
      dmg02SubscriberBirthDate: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['insuredDateOfBirth'].value),
      ref02PropertyCasualtyClaimNo: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['otherClaimID'].value),
      dmg03SubscriberGenderCode: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['genderCode'].value),
      n301PayerAddr1: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n301PayerAddr1'].value),
      n301SubscriberAddr1: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n301SubscriberAddr1'].value),
      n302PayerAddr2: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n302PayerAddr2'].value),
      n302SubscriberAddr2: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n302SubscriberAddr2'].value),
      n401PayerCity: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n401PayerCity'].value),
      n401SubscriberCity: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n401SubscriberCity'].value),
      n402PayerState: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n402PayerState'].value),
      n402SubscriberState: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n402SubscriberState'].value),
      n403PayerZip: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n403PayerZip'].value),
      n403SubscriberZip: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['n403SubscriberZip'].value),
      nm103PayerLastOrOrganizatioName: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value),
      nm103SubscriberLastOrOrganizationName: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value),
      nm104SubscriberFirst: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value),
      nm105SubscriberMiddle: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm105SubscriberMiddle'].value),
      nm109PayerIdCode: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm109PayerIdCode'].value),
      nm109SubscriberIdCode: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),
      per04SubscriberPhoneNo: this.controlValueCheck(this.InsureAddressInfo.patientAddress.controls['per04SubscriberPhoneNo'].value),
      sbr02IndividualRelationshipCode: null,
      sbr04SubscriberGroupName: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['insurancePlanName'].value),
      sbr09ClaimFilingIndicatorCode: this.controlValueCheck(this.MediGroup.mediInfo.controls['insuranceType'].value),
      subscriberkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkey,
      sbr03SubscriberGroupPolicyNo: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['fECANumber'].value),
      dependentProfessional837s: [],
      infoSourcekeyNavigation: this.infoSourcekeyNavigationModel(),
      crcConditionsIndicatorProfessional837s: []
    }
    let dependentProfessional837s: DependentProfessional837sModel = {
      dmg02PatientBirthDate: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg02PatientBirthDate'].value),
      dmg03PatientGenderCode: this.controlValueCheck(this.PatientDOBInfo.patientdobForm.controls['dmg03PatientGenderCode'].value),
      n301PatientAddr1: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['address1'].value),
      n302PatientAddr2: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['address2'].value),
      n401PatientCity: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['city'].value),
      n402PatientState: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['state'].value),
      n403PatientZip: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['zip'].value),
      nm103PatientLastOrOrganizationName: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm103PatientLastOrOrganizationName'].value),
      nm104PatientFirst: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm104PatientFirst'].value),
      nm105PatientMiddle: this.controlValueCheck(this.PatientInfo.patientForm.controls['nm105PatientMiddle'].value),
      pat01IndividualRelationshipCode: this.controlValueCheck(this.PatientRelationshipInfo.patientRelationShip.controls['sbr02IndividualRelationshipCode'].value),
      per04PatientPhoneNo: this.controlValueCheck(this.PatientAddressInfo.patientAddressInfo.controls['telephone'].value),
      dependentkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.dependentkey,

    }
    if (!this.claimFormData?.isAddClaim) {
      dependentProfessional837s.subscriberProfessional837id = this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.subscriberkey.toString()
    }

    subscriberkeyNavigationModel.dependentProfessional837s.push(dependentProfessional837s);
    subscriberkeyNavigationModel.crcConditionsIndicatorProfessional837s.push(this.crcConditionalIndicator())
    return subscriberkeyNavigationModel;
  }
  crcConditionalIndicator() {
    let subscriberkeyNavigationModel: CRCConditionasIndicatoeProfessional837s = {
      dependentProfessional837s: {
        dmg02PatientBirthDate: null,
        dmg03PatientGenderCode: null,
        n301PatientAddr1: null,
        n302PatientAddr2: null,
        n401PatientCity: null,
        n402PatientState: null,
        n403PatientZip: null,
        nm103PatientLastOrOrganizationName: null,
        nm104PatientFirst: null,
        nm105PatientMiddle: null,
        pat01IndividualRelationshipCode: null,
        per04PatientPhoneNo: null,

      }, dtP_DateTimePeriod_Professional_837: []
    };
    let dtpDateTimePeriodProfessional837Model: DTPDateTimePeriodProfessional837 = {
      serviceLinekeyNavigation: {
        nm103SupervisingProviderLastName: null,
        nm104SupervisingProviderFirst: null,
        nm105SupervisingProviderMiddle: null,
        nm108RenderingProviderIdqlfr: null,
        sv10101ProductServiceIdQualifier: null,
        ref02MammographyCertificationNumber: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['mammography'].value),
      },
      dependentkey: null
    }
    subscriberkeyNavigationModel.dtP_DateTimePeriod_Professional_837.push(dtpDateTimePeriodProfessional837Model);
    return subscriberkeyNavigationModel;
  }
  infoSourcekeyNavigationModel() {
    let subscriberkeyNavigationModel: InfoSourceProfessional837 = {
      infosourcekey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.infosourcekey,
      n301BillingProviderAddr1: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n301BillingProviderAddr1'].value),
      n302BillingProviderAddr2: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n302BillingProviderAddr2'].value),
      n401BillingProviderCity: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n401BillingProviderCity'].value),
      n402BillingProviderState: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n402BillingProviderState'].value),
      n403BillingProviderZip: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['n403BillingProviderZip'].value),
      nm103BillingProviderLastOrOrganizationName: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingProviderFullName'].value),
      nm104BillingProviderFirst: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm104BillingProviderFirst'].value),
      nm105BillingProviderMiddle: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['nm103BillingProviderMiddle'].value),
      nm109BillingProviderIdCode: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['groupNPI'].value),
      prv03BillingProviderIdCode: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingproviderSpecialty'].value),
      perAdministrativeCommunicationContactProfessional837s: [],
      ref02BillingProviderEmployerId: this.controlValueCheck(this.FederalTaxInfo.federalInfo.controls['federalTaxNumber'].value),

    }
    let perAdministrativeCommunicationContactProfessional837s: PerAdministrativeCommunicationContactProfessional837sModel = {
      per02ContactName: this.claimFormData?.provider?.billingProvider?.billingContactPersonName,
      per0xEmail: this.claimFormData?.provider?.billingProvider?.billingContactPersonEmailAddress,
      per0xFaxNo: this.claimFormData?.provider?.billingProvider?.billingContactPersonPhoneNumber,
      per0xPhoneNo: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingProviderTelephone'].value),
      per0xPhoneExtNo: null,
      perkey: this.claimFormData?.isAddClaim ? 0 : this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.infoSourcekeyNavigation?.perAdministrativeCommunicationContactProfessional837s[0]?.perkey
    }

    subscriberkeyNavigationModel.perAdministrativeCommunicationContactProfessional837s.push(perAdministrativeCommunicationContactProfessional837s);
    return subscriberkeyNavigationModel;
  }

  claimListDetailsModel() {
    let claimListDetailsModel: ClaimListDetailsModel = {
      iCDCodes: [],
      serviceLineDetails: []
    }
    for (let i = 1; i <= 12; i++) {
      let ICDCodesModel1: ICDCodesModel = {
        iCD: this.controlValueCheck(this.DiagnosisNature.icdInfo.controls['iCDInput' + i].value)
      }
      claimListDetailsModel.iCDCodes.push(ICDCodesModel1);
    }
    for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.controls.length - 1; j++) {
      let serviceLineDetailsModel: ServiceLineDetailsModel = {
        claimskey: this.claimFormData?.isAddClaim ? null : this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey,
        dOSFrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value),
        dOSTo: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value),
        DOSFrom: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSFrom) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value),
        DOSTo: this.claimFormData?.isAddClaim ? this.controlValueCheck(this.claimFormData.profileMember.dOSTo) : this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value),
        svServiceFacilityId2: this.controlValueCheck(this.ServiceFacilityForm.serviceInfo.controls['FacilityId'].value),
        n301ServiceFacilityAddress1: null,
        n302ServiceFacilityAddress2: null,
        n401ServiceFacilityCity: null,
        n402ServiceFacilityState: null,
        n403ServiceFacilityZip: null,
        nm103RenderingProviderNameLastOrg: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['billingProviderFullName'].value),
        nm109RenderingProviderId: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('jRenderingProviderId').value),
        nm109ServiceFacilityId: null,
        sv10102ProcedureCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('cpt').value),
        sv10103ProcedureModifier1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m1').value),
        sv10104ProcedureModifier2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m2').value),
        sv10105ProcedureModifier3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m3').value),
        sv10106ProcedureModifier4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('m4').value),
        sv102LineItemChargeAmount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('unitCharges').value).toString(),
        sv104ServiceUnitCount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dayUnitChanges').value),
        sv105PlaceOfServiceCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('locationOfService').value),
        sv10701DiagnosisCodePointer1: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer1').value),
        sv10702DiagnosisCodePointer2: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer2').value),
        sv10703DiagnosisCodePointer3: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer3').value),
        sv10704DiagnosisCodePointer4: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('diagnosispointer4').value),
        sv109EmergencyIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('emg').value),
        sv111EpsdtIndicator: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ePSDT').value),
        serviceLinekey: 0,
        sv10101ProductServiceIdQualifier: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('proceduceC').value),
        anesStart: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('anesStart').value),
        anesStop: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('anesStop1').value),
        lin03NationalDrugCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcCode').value),
        lin02NationalDrugCodeQlfr: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcQual').value),
        ctp04NationalDrugUnitCount: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcQty').value),
        ctp0501UnitMeasurementCode: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcQtyQual').value),
        nte02LineNoteText: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('lineNote').value),
        cpt03NationalDrugUnitPrice: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('ndcUnitPrice').value),
        lx01AssignedNumber: (this.ServiceLineClaimInfo.serviceLine.controls.length + 1).toString(),
        sv10107ServiceDescription: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('desc').value.value),
        nmSupervisingProviderId2: this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingId'].value),
        nm103SupervisingProviderLastName: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianLastname'].value) : null,
        nm105SupervisingProviderMiddle: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianMiddlename'].value) : null,
        nm104SupervisingProviderFirst: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['physicianFirstname'].value) : null,
        nm109SupervisingProviderId: j === 0 ? this.controlValueCheck(this.ProviderSignInfo.providerSignInfo.controls['supervisingNpi'].value) : null,
      }
      serviceLineDetailsModel.dtp03ServiceDate = this.dateChangeToString(this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceFrom').value), this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(j).get('dateServiceTo').value));
      claimListDetailsModel.serviceLineDetails.push(serviceLineDetailsModel);
    }
    return claimListDetailsModel;
  }
  controlValueCheck(data) {
    if (data === undefined)
      return null;
    if (data === null)
      return null;
    if (data === '')
      return null;
    if (moment.isMoment(data)) {
      return moment(data).format('YYYY-MM-DD')
    }
    return data;
  }
  dateChangeToString(fromDate: string, toDate: string) {

    if (fromDate === undefined && toDate === undefined)
      return null;
    if (fromDate === null && toDate === null)
      return null;
    if (fromDate === '' && toDate === null)
      return null;
    if (typeof (fromDate) == 'string') {
      return fromDate.substring(0, 4) + fromDate.substring(5, 7) + fromDate.substring(8, 10) + "-" + toDate.substring(0, 4) + toDate.substring(5, 7) + toDate.substring(8, 10);
    }
    else {
      return moment(fromDate).format('YYYYMMDD') + "-" + moment(toDate).format('YYYYMMDD')
    }

  }
  changeStatusText(status: string) {
    let statusText = '';
    switch (!!status && status.toLocaleUpperCase()) {
      case 'OH':
        statusText = 'ON HOLD';
        break;
      case 'ON':
        statusText = 'OPEN';
        break;
      case "AC":
        statusText = 'ACCEPTED';
        break;
      case "NTR":
        statusText = 'RESUBMISSION';
        break;
      case "DP":
        statusText = 'DISPATCHED';
        break;

      case "UACH":
        statusText = 'UNACKNOWLEDGED BY CLEARING HOUSE';
        break;
      case "RCH":
        statusText = 'REJECTED BY CLEARING HOUSE';
        break;
      case "ABCH":
        statusText = 'ACCEPTED BY CLEARING HOUSE';
        break;
      case "ANCH":
        statusText = 'ACKNOWLEDGED BY CLEARING HOUSE';
        break;
      case "ABP":
        statusText = 'ACCEPTED BY PAYER';
        break;
      case "UAP":
        statusText = 'UNACKNOWLEDGED BY PAYER';
        break;
      case "AP":
        statusText = 'ACKNOWLEDGED BY PAYER';
        break;
      case "RP":
        statusText = 'REJECTED BY PAYER';
        break;

      case "PEND":
        statusText = 'PENDING';
        break;
      case "EOB":
        statusText = 'EOB RECIEVED';
        break;
      case "DBP":
        statusText = 'DENIED BY PAYER';
        break;
    }
    return statusText;
  }
  open(status: string) {
    if (this.showNotes) {
      this.showNotes = false;
    }
    if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {
      this.claimModel = [];
      this.smartystreet().subscribe((success: boolean) => {
        if (success) {
          this.claimModelConvertion();
          this.updateModelMapping();
          this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
            if (res) {
              this.changeClaimStatus(status);
            }
          });
        } else {
          this.claimModelConvertion();
          this.updateModelMapping();
          this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
            if (res) {
              this.changeClaimStatus(status);
            }
          });
        }
      });
    }
    else if (this.validateAllForms()) {
      this.claimModelConvertion();
      this.updateModelMapping();
      this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
        if (res) {
          this.changeClaimStatus(status);
        }
      });
    }
  }
  accept(status: string) {
    if (this.showNotes) {
      this.showNotes = false;
    }
    if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {
      if (this.claimFormData?.claimViewModel?.source?.toLocaleLowerCase() != "gateway") {
        this.validateAddress(status);
      } else {
        this.smartystreet().subscribe((success: boolean) => {
          if (success) {
            this.claimModelConvertion();
            this.updateModelMapping();
            this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
              if (res) {
                this.changeClaimStatus(status);
              }
            });
          }
          else{
            this.claimModelConvertion();
            this.updateModelMapping();
            this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
              if (res) {
                this.changeClaimStatus(status);
              }
            });
          }
        });
      }
    }
    // else if (this.validateAllForms()) {
    //   this.claimModelConvertion();
    //   this.updateModelMapping();
    //   this.claimService.createClaim(this.claimModel).subscribe((res: any) => {
    //     if (res) {
    //       this.changeClaimStatus(status);
    //     }
    //   });
    // }


  }
  handleResponses(response: any): Observable<boolean> {
    const dialogRef = this.dialog.open(AddressMismatchComponent, {
      width: '800px',
      data: [
        { entity: 'Patient & Insured', address: response.content }
      ]
    });

    return dialogRef.afterClosed().pipe(
      map((confirmed: boolean) => {
        if (confirmed) {
          this.InsureAddressInfo.f.n301SubscriberAddr1.setValue(response.content.addressLineOne);
          this.InsureAddressInfo.f.n401SubscriberCity.setValue(response.content.city);
          this.InsureAddressInfo.f.n402SubscriberState.setValue(response.content.state);
          this.InsureAddressInfo.f.n403SubscriberZip.setValue(response.content.zipcode);
          this.PatientAddressInfo.f.address1.setValue(response.content.addressLineOne);
          this.PatientAddressInfo.f.city.setValue(response.content.city);
          this.PatientAddressInfo.f.state.setValue(response.content.state);
          this.PatientAddressInfo.f.zip.setValue(response.content.zipcode);
          this.PatientAddressInfo.patientAddressInfo.updateValueAndValidity();
          this.InsureAddressInfo.patientAddress.updateValueAndValidity();
          return true; // Address mapping successful
        }
        return false; // Address mapping not confirmed
      })
    );
  }

  smartystreet(): Observable<boolean> {
    let requestBody = {
      addressLineOne: this.InsureAddressInfo.f.n301SubscriberAddr1.value,
      city: this.InsureAddressInfo.f.n401SubscriberCity.value,
      state: this.InsureAddressInfo.f.n402SubscriberState.value,
      givenZipcode: this.InsureAddressInfo.f.n403SubscriberZip.value,
    };
    let patientrequestbody = {
      addressLineOne: this.PatientAddressInfo.f.address1.value,
      city: this.PatientAddressInfo.f.city.value,
      state: this.PatientAddressInfo.f.state.value,
      givenZipcode: this.PatientAddressInfo.f.zip.value,
    };
    let initialRequest = this.providerManagementService.GetCorrectAddress(requestBody);
    let alternativeRequest = this.providerManagementService.GetCorrectAddress(patientrequestbody);

    return forkJoin([initialRequest, alternativeRequest]).pipe(
      switchMap(responses => {
        let initialResponse = responses[0];
        let alternativeResponse = responses[1];

        if (this.isValidResponse(initialResponse)) {
          return this.handleResponses(initialResponse);
        } else if (this.isValidResponse(alternativeResponse)) {
          return this.handleResponses(alternativeResponse);
        } else {
          return of(false); // Neither response is valid
        }
      })
    );
  }


  initLoadSmartyStreetValidate() {
    let requestBody = {
      addressLineOne: this.claimFormData?.memberResult?.member.addressLine1,
      city: this.claimFormData?.memberResult?.member.city,
      state: this.claimFormData?.memberResult?.member.stateSubdivisionCode,
      givenZipcode: this.claimFormData?.memberResult?.member.zipCode,
    };
    this.providerManagementService.GetCorrectAddress(requestBody).subscribe((response: any) => {
      if (!!response && !!response.content && response.content.isSmartAddressFound) {
        this.patchValidSmartyAddress(response);
      }
    });
  }

  initLoadForEditClaimSmaryStreetValidate() {
    let requestBody = {
      addressLineOne: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n301PatientAddr1,
      city: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n401PatientCity,
      state: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n402PatientState,
      givenZipcode: this.claimFormData?.claimViewModel?.claimsProfessional837?.subscriberkeyNavigation?.dependentProfessional837s[0]?.n403PatientZip,
    }

    this.providerManagementService.GetCorrectAddress(requestBody).subscribe((response: any) => {
      if (!!response && !!response.content && response.content.isSmartAddressFound) {
        this.patchValidSmartyAddress(response);
      }
    });
  }

  patchValidSmartyAddress(response: any) {
    // patient address
    this.PatientAddressInfo.patientAddressInfo.patchValue({
      address1: response.content.addressLineOne,
      city: response.content.city,
      state: response.content.state,
      zip: response.content.zipcode == null ? response.content.givenZipcode : response.content.zipcode
    })

    // insured address.
    this.InsureAddressInfo.patientAddress.patchValue({
      n301SubscriberAddr1: response.content.addressLineOne,
      n401SubscriberCity: response.content.city,
      n402SubscriberState: response.content.state,
      n403SubscriberZip: response.content.zipcode == null ? response.content.givenZipcode : response.content.zipcode
    })
  }

  validateAddress(status: string) {
    let validateAddress: MemberValidateClaimMoveFromOpen = {
      claimDOSFrom: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(0).get('dateServiceFrom').value),
      claimDOSTo: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(0).get('dateServiceTo').value),
      dosFrom: this.controlValueCheck(this.ServiceLineClaimInfo.serviceLine.at(0).get('dateServiceFrom').value),
      claimId: this.claimFormData?.claimViewModel?.claimsProfessional837?.claimskey.toString(),
      firstName: this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value,
      insuranceCompanyName: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value),
      lastName: this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value,
      memberDOB: this.controlValueCheck(this.InsurePolicyGroupInfo.insurencePolicyInfo.controls['insuredDateOfBirth'].value),
      memberFirstName: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm104SubscriberFirst'].value),
      memberid: this.controlValueCheck(this.SubscriberInfo.subscriberInfo.controls['subscriberID'].value),
      memberLastName: this.controlValueCheck(this.InsureInfo.insureInfo.controls['nm103SubscriberLastOrOrganizationName'].value),
      npi: this.controlValueCheck(this.BillingInfoInfo.billingInfo.controls['providerNPI'].value),
      payToAddressLine1: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n301PayerAddr1'].value),
      payToCity: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n401PayerCity'].value),
      payToName: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['nm103PayerLastOrOrganizatioName'].value),
      payToState: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n402PayerState'].value),
      payToZipCode: this.controlValueCheck(this.PayerInfoForm.payerInfo.controls['n403PayerZip'].value),
      taxIdOrSSN: this.controlValueCheck(this.FederalTaxInfo.federalInfo.controls['federalTaxNumber'].value),
      email: JSON.parse(localStorage.getItem('email')),
      ipacode: this.claimFormData?.claimViewModel?.ipacode,
      uuid: JSON.parse(localStorage.getItem('uuid')),
      mdmServiceBaseUrl: JSON.parse(localStorage.getItem('MDMService')),
      providerVillaServiceBaseUrl: JSON.parse(localStorage.getItem('ProviderService')),
      memberHouseServiceBaseUrl: JSON.parse(localStorage.getItem('MemberService')),

    }
    this.claimService.validateAddress(validateAddress).subscribe((res: ValidateAddressResponse) => {
      const dialogRef = this.dialog.open(ValidateAddressComponent, {
        height: '530px',
        width: '1100px',
        panelClass: 'custom-dialog-containers',
        data: res
      });
      dialogRef.afterClosed().subscribe(res => {
        if (res) {
          this.changeClaimStatus(status);
        }

      });
    })
  }
  deactivate(status: string) {
    this.changeClaimStatus(status);
  }
  onHold(status: string) {
    if (this.showNotes) {
      this.showNotes = false;
    }
    const dialogRef = this.dialog.open(ReasonToHoldComponent, {
      minWidth: '40vw', minHeight: '35vh', autoFocus: false,
      restoreFocus: false,
    });
    dialogRef.afterClosed().subscribe((res) => {
      if (!!res && res.mode == 'changeStatus') {
        if (this.validateAllForms() && this.patientOrInsuredAddressIsDirtyCheck()) {
          this.claimModel = [];
          this.smartystreet().subscribe((success: boolean) => {
            this.onHoldUpdate(res,status)
          });

        }
        else if (this.validateAllForms()) {
          this.onHoldUpdate(res,status)
        }
      }
    });
  }
  onHoldUpdate(res,status) {
    this.claimModelConvertion();
    this.updateModelMapping();
    let data = [];
    data = this.claimModel;
    data[0].isActive = true;
    data[0].status = status;
    data[0].onHoldCategory = res.data?.categoryDesc;
    data[0].claimFormStatusCode = CLAIM_TYPE.onHold;
    data[0].lastModifiedByFirstName = JSON.parse(localStorage.getItem('userFirstName'));
    data[0].lastModifiedByLastName = JSON.parse(localStorage.getItem('userLastName'));
    data[0].userFirstName = this.claimFormData?.claimViewModel?.userFirstName;
    data[0].userLastName = this.claimFormData?.claimViewModel?.userLastName;
    data[0].reasons = [];
    data[0].reasons.push({ categoryFkId: res.data.id, description: res.data.description })
    this.claimService.createClaim(data).subscribe((res: any) => {
      this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} has been moved to ON HOLD bucket successfully.`, ValidationMsgs.success, 4000);
      this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
      this.subjectService.setIsClaimMovedOtherBucket();
    })
  }

  changeClaimStatus(status: string) {
    let changeClaimStatus: ClaimStatusModel = {
      claimsStatus: [
        {
          claimId: this.claimFormData?.claimViewModel?.claimForm837Pid,
          patientControlNumber: this.claimFormData?.claimViewModel?.clm01PatientControlNo,
        }
      ],
      status: status,
      lastModifiedFirstName: JSON.parse(localStorage.getItem('userFirstName')),
      lastModifiedLastName: JSON.parse(localStorage.getItem('userLastName')),
      lastModifiedMiddleName: JSON.parse(localStorage.getItem('userMiddleName')),
    }
    let statusText = this.changeStatusText(status);
    this.claimService.claimStatusChange(changeClaimStatus).subscribe((res: any) => {
      if (res) {
        this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} has been moved to ` + statusText + ' bucket successfully', ValidationMsgs.success , 4000);
        this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
        this.subjectService.setCloseTabRefresh('View Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
        this.subjectService.setIsClaimMovedOtherBucket();
        // this.openDialog({
        //   isClaimCreated: false,
        //   message: res
        //   , title: 'Claim Status',
        //   claimIds: null
        // })
      }
    });

  }
  validateAccidentDate() {
    let otherDate = this.OtherDateInfo.otherInfo.controls.otherDateQual.value;
    let otherQual = this.OtherDateInfo.otherInfo.controls.otherQual.value;
    let accidentDate = this.ProviderSignInfo.providerSignInfo.controls.accident.value;
    if (!!otherQual && otherQual == '439') {
      if (!accidentDate) {
        this.ProviderSignInfo.providerSignInfo.controls.accident.setErrors({ require: true })
      } else {
        if (this.ProviderSignInfo.providerSignInfo.controls.accident.hasError('require')) {
          delete this.ProviderSignInfo.providerSignInfo.controls.accident.errors['require'];
          this.ProviderSignInfo.providerSignInfo.controls.accident.updateValueAndValidity();
        }
        if (!!otherDate && new Date(otherDate).getDate() + '' + new Date(otherDate).getMonth() + '' + new Date(otherDate).getFullYear() != new Date(accidentDate).getDate() + '' + new Date(accidentDate).getMonth() + '' + new Date(accidentDate).getFullYear()) {
          this.ProviderSignInfo.providerSignInfo.controls.accident.setErrors({ isInvalid: true })
        } else {
          if (this.ProviderSignInfo.providerSignInfo.controls.accident.hasError('isInvalid')) {
            delete this.ProviderSignInfo.providerSignInfo.controls.accident.errors['isInvalid'];
            this.ProviderSignInfo.providerSignInfo.controls.accident.updateValueAndValidity();
          }
        }
      }
    } else {
      this.ProviderSignInfo.providerSignInfo.controls.accident.setErrors(null)
    }
  }

  validateDosFrom() {
    let currentIllnessDate = new Date(this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value);
    if (!!this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value && !!currentIllnessDate) {
      for (let i = 0; i <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; i++) {
        if (!!this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value && !!new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value)) {
          if (new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value).getTime() < currentIllnessDate.getTime()) {
            this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').setErrors({ currentIllnessError: true });
          } else {
            if (this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').hasError('currentIllnessError')) {
              delete this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').errors['currentIllnessError'];
              this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').updateValueAndValidity();
            }
          }
        } else {
          if (this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').hasError('currentIllnessError')) {
            delete this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').errors['currentIllnessError'];
            this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').updateValueAndValidity();
          }
        }
      }
      if (this.ServiceLineClaimInfo.serviceLineInfo.invalid) {
        submitValidateAllFields.validateAllFields(this.ServiceLineClaimInfo.serviceLineInfo);
        return;
      }
    }
  }

  validateCurrentIllnessDate() {
    let currentIllnessDate = new Date(this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value);
    if (!!this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.value && !!currentIllnessDate) {
      for (let i = 0; i <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; i++) {
        if (!!this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceTo').value && !!new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceTo').value)
          && !!this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value && !!new Date(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').value)
        ) {
          let dateServiceTo = moment(moment(this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceTo').value).format('MM/DD/YYYY'));
          let currentIllnessDateTemp = moment(moment(currentIllnessDate).format('MM/DD/YYYY'));
          if (moment(currentIllnessDateTemp).isAfter(dateServiceTo)
          ) {

            this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.setErrors({ currentIllnessError: true });
          } else {
            if (this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.hasError('currentIllnessError')) {
              delete this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').errors['currentIllnessError'];
              this.ServiceLineClaimInfo.serviceLine.at(i).get('dateServiceFrom').updateValueAndValidity();
            }
          }
        } else {
          if (this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.hasError('currentIllnessError')) {
            delete this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.errors['currentIllnessError'];
            this.CurrentIllnessInfo.currentillnessInfo.controls.dtp03OnsetofCurrentIllnessInjuryDate.updateValueAndValidity();
          }
        }
      }
      if (this.CurrentIllnessInfo.currentillnessInfo.invalid) {
        submitValidateAllFields.validateAllFields(this.CurrentIllnessInfo.currentillnessInfo);
        return;
      }
    }
  }

  calculateTotal(e) {
    let totalAmount:  number = 0.0;
    for (let j = 0; j <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; j++) {
     //const unitCharges = this.ServiceLineClaimInfo.serviceLine.at(j).get('unitCharges').value;
     //const dayUnitChanges = Number(this.ServiceLineClaimInfo.serviceLine.at(j).get('dayUnitChanges').value) 
     const total = parseFloat(this.ServiceLineClaimInfo.serviceLine.at(j).get('total').value) || 0;
     totalAmount += total;
    }
    this.ChargesInfo.chargesInfo.patchValue({
      totalCharges: (totalAmount).toFixed(2)
    })
  }

  reasonDesc(reson) {
    if (!!reson &&reson.length > 0)
      return reson[reson.length - 1]?.description;
    return '';
  }
  reasonHoldingDesc(reson) {
    if (!!reson && reson.length > 0)
      return reson[reson.length - 1].categoryFk?.categoryDesc;
    return '';
  }
  anotherHealthInsurenceValidate(e) {
    this.PatientOtherInsureInfo.validateRequired(e);
    this.PatientOtherInsureInfo.validateControls();
  }
  parentClaimNavigation() {
    
    let nav = new NavTabFromDetails();
    nav.name = Tabs.viewClaim;
    nav.data.claimControlNumber = this.claimFormData?.claimViewModel?.parentPatientCtrlNo;
    nav.data.claimId = this.claimFormData?.claimViewModel?.parentClaimForm837Pid;
    this.subjectService.setViewClaim(this.claimFormData?.claimViewModel?.parentClaimForm837Pid);
    localStorage.setItem('claimTapName', 'View Claim - #' + this.claimFormData?.claimViewModel?.parentPatientCtrlNo);
    this.subjectService.passValue(nav);
  }

  activateClaim() {
    this.claimModel = [];
    this.claimModelConvertion();
    this.updateModelMapping();
    let data = [];
    data = this.claimModel;

    data[0].isActive = true;
    data[0].status = "Active";
    this.claimService.createClaim(data).subscribe((res: any) => {
      this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} activated  Successfully`, ValidationMsgs.success, 4000);

      this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
    })
  }

  deactivateClaim() {
    this.claimModel = [];
    this.claimModelConvertion();
    this.updateModelMapping();
    let data = [];
    data = this.claimModel;
    data[0].isActive = false;
    data[0].status = "InActive";
    if (this.showNotes) {
      this.showNotes = false;
    }
    // return;
    this.claimService.createClaim(data).subscribe((res: any) => {
      this.notificationService.showSuccess(`Claim ${this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value} de-activated  Successfully`, ValidationMsgs.success, 4000);
      this.subjectService.setCloseTabRefresh('Edit Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
      this.subjectService.setIsClaimMovedOtherBucket();
    })
  }
  updateModelMapping() {
    let data = this.claimModel;
    data[0].claimFormStatusCode = this.claimFormData?.claimViewModel?.claimFormStatusCode;
    data[0].parentClaimForm837Pid = this.claimFormData?.claimViewModel?.parentClaimForm837Pid;
    data[0].parentPatientCtrlNo = this.claimFormData?.claimViewModel?.parentPatientCtrlNo;
    data[0].formCreatedDate = this.claimFormData?.claimViewModel?.formCreatedDate;
    data[0].onHoldCategory = this.claimFormData?.claimViewModel?.onHoldCategory;
    data[0].notes = this.claimFormData?.claimViewModel?.notes;
    data[0].formCreatedDate = this.claimFormData?.claimViewModel?.formCreatedDate;
    data[0].source = this.claimFormData?.claimViewModel?.source;
    data[0].userFirstName = this.claimFormData?.claimViewModel?.userFirstName;
    data[0].userLastName = this.claimFormData?.claimViewModel?.userLastName;
    data[0].lastModifiedByFirstName = JSON.parse(localStorage.getItem('userFirstName'));
    data[0].lastModifiedByLastName = JSON.parse(localStorage.getItem('userLastName'));
  }

  resubmission() {
    if (this.showNotes) {
      this.showNotes = false;
    }
    const dialogRef = this.dialog.open(ResubmitClaimComponent, {
      //height: '680px',
      //width: '1400px',
      minHeight: '240px',
      maxHeight: '680px',
      minWidth: '1200px',
      maxWidth: '1400px',
      panelClass: 'custom-dialog-containers',
      data: {
        claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value,
        claimId: this.claimFormData?.claimViewModel?.parentClaimForm837Pid ? this.claimFormData?.claimViewModel?.parentClaimForm837Pid : this.claimFormData?.claimViewModel?.claimForm837Pid
      }
    });
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.subjectService.setCloseTabRefresh('View Claim - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);
      }
    })
  }
  showResubmit() {
    const privielagesDetails = this.globalService.getPrivilegesByRole();
    if (privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_Resubmission).length > 0) {
      return (
        this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.dispatched && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acknolwedgedByCH && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acceptedByCH && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.unAckByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acceptedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.acknolwedgedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.rejectedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.pending.toUpperCase() && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.rejectedByCH && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.eobReceived && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null
        || this.claimFormData.claimViewModel?.claimFormStatusCode.toUpperCase() === CLAIM_TYPE.deniedByPayer && this.claimFormData.claimViewModel?.parentPatientCtrlNo == null

      ) ? true : false;
    }
    return false;

  }

  rejectedReason() {
    this.dialog.open(RejectedInComponent, {
      minHeight: '40vh',
      minWidth: '70vw',
      maxHeight: '72vh',
      height: '-webkit-fill-available',
      panelClass: 'custom-dialog-containers',
      data: {
        claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value,
        claimId: this.claimFormData?.claimViewModel?.parentClaimForm837Pid
      }
    });
  }
  pendingDetails() {
    this.dialog.open(PendingDetailsComponent, {
      minHeight: '40vh',
      minWidth: '70vw',
      maxHeight: '72vh',
      height: '-webkit-fill-available',
      panelClass: 'custom-dialog-containers',
      data: {
        claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value,
      }
    });
  }

  viewClaimClose() {
    let tabName = Tabs.editClaim;

    if (this.claimFormData?.isViewClaim)
      tabName = Tabs.viewClaim

    this.subjectService.setCloseTabRefresh(tabName + ' - #' + this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value);

  }
  notesLength() {
    return this.claimFormData?.claimViewModel?.notes?.filter(t => !t.isRemoved).length;
  }
  openLinkedClaims() {
    this.dialog.open(LinkedClaimsComponent, {
      minHeight: '40vh',
      minWidth: '70vw',
      maxHeight: '65vh',
      height: '-webkit-fill-available',
      panelClass: 'custom-dialog-containers',
      data: {
        claimControlNumber: this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value,
        linkedClaims: this.claimFormData?.claimViewModel?.lstLinkedClaimDetails
      }
    });
  }
  viewLinkedClaim(data) {
    let nav = new NavTabFromDetails();
    nav.name = Tabs.viewClaim;
    nav.data.claimControlNumber = data.patientCtrlNo;
    this.subjectService.setViewClaim(data.claimPid);
    localStorage.setItem('claimTapName', 'View Claim - #' + data.patientCtrlNo);
    this.subjectService.passValue(nav);
  }
  downloadLatestResponseFile() {
    let b64Data;
    let fileId: any;
  if (this.claimFormData.claimViewModel?.claimFormStatusCode === 'AP') {
    fileId = this.claimFormData.claimViewModel?.incomingFileId;
  } else {
    fileId = this.claimFormData.claimViewModel?.responseFileClaimMapings?.[0]?.incomeFileLoggerId;
  }
    this.fileService.downloadLatestResponseFile(fileId).subscribe((data) => {
      if (data.statusCode == 404) {
        this.notificationService.showError('', 'File Not Found.', 4000);
      }
      else if (data.statusCode == 500) {
        this.notificationService.showError('Error occured, Please try again later.', 'Error!', 4000);
      }
      else {
        if (!!data.content && !!data.content.ediFileContent) {
          b64Data = data.content.ediFileContent;
          this.notificationService.showSuccess('File Downloaded.', ValidationMsgs.success, 4000);
          const blob1 = new Blob([atob(b64Data)], { type: 'text/plain' });
          const a: any = document.createElement('a');
          a.style = 'display: none';
          document.body.appendChild(a);
          const url = window.URL.createObjectURL(blob1);
          a.href = url;
          a.download = !!data.content.fileName ? data.content.fileName : 'Latest Response';
          a.click();
          window.URL.revokeObjectURL(url);
        }
      }
    })
  }
  exportClaim() {
    this.claimModel = [];
    this.claimModelConvertion();

    this.updateModelMapping();
    this.claimModel[0].reasons = this.claimFormData?.claimViewModel?.reasons;
    this.claimModel[0].lastModifiedByFirstName = this.claimFormData?.claimViewModel?.lastModifiedByFirstName;
    this.claimModel[0].lastModifiedByLastName = this.claimFormData?.claimViewModel?.lastModifiedByLastName;
    this.claimModel[0].lastModifiedDate = this.claimFormData?.claimViewModel?.lastModifiedDate;
    this.claimModel[0].showNDC = this.ServiceLineClaimInfo.serviceLineInfo.controls["showNDC"].value === "No" ? false : true;
    this.claimModel[0]._277processedOnPayer = this.claimFormData?.claimViewModel._277processedOnPayer;
    this.claimModel[0]._999processedOnPayer = this.claimFormData?.claimViewModel._999processedOnPayer;
    this.claimModel[0]._999processedOnCh = this.claimFormData?.claimViewModel._999processedOnCh;
    this.claimModel[0]._277processedOnCh = this.claimFormData?.claimViewModel._277processedOnCh;
    this.claimModel[0].ignoreReason = this.claimFormData?.claimViewModel.ignoreReason;
    this.claimModel[0].patientDob = this.claimFormData?.claimViewModel?.patientDob;
    this.claimModel[0].isActive = this.claimFormData?.claimViewModel.isActive;
    if (this.showNotes) {
      this.showNotes = false;
    }
    this.fileService.exportCMSForm(this.claimModel[0]).subscribe((data) => {
      if (data.statusCode == 404) {
        this.notificationService.showError('', 'File Not Found.', 4000);
      }
      else if (data.statusCode == 500) {
        this.notificationService.showError('Error occured, Please try again later.', 'Error!', 4000);
      }
      else {
        if (!!data.content) {
          this.notificationService.showSuccess('File Downloaded.', ValidationMsgs.success, 4000);
          const byteString = window.atob(data.content);
          const arrayBuffer = new ArrayBuffer(byteString.length);
          const int8Array = new Uint8Array(arrayBuffer);
          for (let i = 0; i < byteString.length; i++) {
            int8Array[i] = byteString.charCodeAt(i);
          }

          let fileName = this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value + "_" + this.formatDate(new Date());
          const blob = new Blob([int8Array], { type: 'application/pdf' });
          const file = blob ? new File([blob], this.PatientAccountAcceptAssignmentInfo.patientAccountInfo.controls['patientAccountNo'].value + '.pdf', { type: 'application/pdf' }) : null;
          const url2 = file ? URL.createObjectURL(file) : null;
          const a: any = document.createElement('a');
          a.style = 'display: none';
          document.body.appendChild(a);
          a.href = url2;
          a.download = fileName + '.pdf';
          a.click();
          window.URL.revokeObjectURL(url2);
        }
      }
    })
  }

  formatDate(dateObj) {

    var curr_date = dateObj.getDate();
    var curr_month = dateObj.getMonth();
    curr_month = curr_month + 1;
    var curr_year = dateObj.getFullYear();
    var curr_min = dateObj.getMinutes();
    var curr_hr = dateObj.getHours();
    var curr_sc = dateObj.getSeconds();
    if (curr_month.toString().length == 1)
      curr_month = '0' + curr_month;
    if (curr_date.toString().length == 1)
      curr_date = '0' + curr_date;
    if (curr_hr.toString().length == 1)
      curr_hr = '0' + curr_hr;
    if (curr_min.toString().length == 1)
      curr_min = '0' + curr_min;

    return curr_month + curr_date + curr_year + curr_hr + curr_min + curr_sc;

  }
  validateDiagnosisPointerData(e: any) {
    for (let i = 0; i <= this.ServiceLineClaimInfo.serviceLine.value.length - 1; i++) {
      for (let j = 1; j <= 4; j++) {
        if ((Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value) === e.index || Number.parseInt(this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).value) === 0) && e.validationRequired) {
          this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors({ numberMax: true });
          this.ServiceLineClaimInfo.serviceLine.updateValueAndValidity();

        } else {
          this.ServiceLineClaimInfo.serviceLine.at(i).get('diagnosispointer' + j).setErrors(null);
          this.ServiceLineClaimInfo.serviceLine.updateValueAndValidity();
        }
      }
    }
    this.ServiceLineClaimInfo.setValidator();

  }


  updatePayerItem(newPayerItem: any | null): void {
    this.claimFormData.claimViewModel.payerId =newPayerItem.payerId;
    this.claimFormData.claimViewModel.insuranceCompany = newPayerItem.payerName
  }
  getPrivilegesByRole() {
    const privielagesDetails = this.globalService.getPrivilegesByRole();
    this.isDeactivateClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_DeactivateaClaim).length > 0 ? true : false;
    this.isAcceptedClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_AcceptAClaim).length > 0 ? true : false;
    this.isActivateClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_ActivateaClaim).length > 0 ? true : false;
    this.isOnHoldClainBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_OnHoldAClaim).length > 0 ? true : false;
    this.isUpdateClaimBtnShow = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_UpdateaClaim).length > 0 ? true : false;
  }
}



