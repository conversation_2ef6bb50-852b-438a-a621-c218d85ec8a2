import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor(private toastr: ToastrService) { }

  showSuccess(message, title, timeOut) {
    this.toastr.success(message, title, {
      timeOut: timeOut,
      closeButton: true,
      tapToDismiss: false,
      progressBar: true
    })
  }

  showError(message, title, timeOut) {
    this.toastr.error(message, title, {
      timeOut: timeOut,
      closeButton: true,
      tapToDismiss: false,
      progressBar: true
    })
  }

  showInfo(message, title, timeOut) {
    this.toastr.info(message, title, {
      timeOut: timeOut,
      closeButton: true,
      tapToDismiss: false,
      progressBar: true
    })
  }

  showWarning(message, title, timeOut) {
    this.toastr.warning(message, title, {
      timeOut: timeOut,
      closeButton: false,
      tapToDismiss: false,
      progressBar: true
    })
  }

}
