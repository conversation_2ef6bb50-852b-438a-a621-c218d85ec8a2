{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/Practice Management/Web/Mirra.Web.PracticeManagement/Mirra.Web.PracticeManagement/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ChangeDetectorRef, EventEmitter } from '@angular/core';\nimport { FormControl, Validators } from '@angular/forms';\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\nimport { distinctUntilChanged, first } from 'rxjs';\nimport * as moment from 'moment';\nimport Swal from 'sweetalert2';\nimport * as JSLZString from 'lz-string';\nimport { CptViewHistoryComponent } from '../../../../../modals/cpt-view-history/cpt-view-history.component';\nimport { numberWith3DecimalValidator, modifierValidator } from 'src/app/common/form.validators';\nimport { LocalStorageKey, PriceCost } from 'src/app/shared/constant/constatnt';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/ClaimForm/all-place-of-services.service\";\nimport * as i3 from \"src/app/shared/services/subject.service\";\nimport * as i4 from \"src/app/services/ClaimForm/get-all-cptcode.service\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"src/app/services/cache-service/cache.service\";\nimport * as i7 from \"ngx-spinner\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"../../../../../shared/directives/numbers-only.directive\";\nimport * as i12 from \"../../../../../shared/directives/number-only-withdecimal\";\nimport * as i13 from \"../../../../../shared/directives/number-only-twodecimal\";\nimport * as i14 from \"@angular/material/datepicker\";\n\nfunction ServiceLineClaimComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"th\", 17);\n  }\n}\n\nfunction ServiceLineClaimComponent_a_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_a_52_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cptViewHistory());\n    });\n    i0.ɵɵelement(1, \"span\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 79);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r82 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r82.ndcValidation(i_r7).controls[\"proceduceC\"].value && ctx_r82.ndcValidation(i_r7).controls[\"proceduceC\"].value.length > 0 ? ctx_r82.ndcValidation(i_r7).controls[\"proceduceC\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 81);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r84 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r84.ndcValidation(i_r7).controls[\"proceduceCount\"].value && ctx_r84.ndcValidation(i_r7).controls[\"proceduceCount\"].value.length > 0 ? ctx_r84.ndcValidation(i_r7).controls[\"proceduceCount\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 82);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r86 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r86.ndcValidation(i_r7).controls[\"lineNote\"].value && ctx_r86.ndcValidation(i_r7).controls[\"lineNote\"].value.length > 0 ? ctx_r86.ndcValidation(i_r7).controls[\"lineNote\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 83);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r88 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r88.ndcValidation(i_r7).controls[\"anesStart\"].value && ctx_r88.ndcValidation(i_r7).controls[\"anesStart\"].value.length > 0 ? ctx_r88.ndcValidation(i_r7).controls[\"anesStart\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 84);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r90 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r90.ndcValidation(i_r7).controls[\"anesStop1\"].value && ctx_r90.ndcValidation(i_r7).controls[\"anesStop1\"].value.length > 0 ? ctx_r90.ndcValidation(i_r7).controls[\"anesStop1\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_27_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r114 = ctx.item;\n    i0.ɵɵtextInterpolate2(\" \", item_r114.qualifier, \"-\", item_r114.description, \" \");\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r117 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 85, 86);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_ng_select_27_Template_ng_select_change_0_listener() {\n      i0.ɵɵrestoreView(_r117);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r115 = i0.ɵɵnextContext();\n      ctx_r115.changeValuesToMarkValidation(i_r7);\n      return i0.ɵɵresetView(ctx_r115.ndcQualChange(i_r7));\n    });\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_ng_select_27_ng_template_2_Template, 1, 2, \"ng-template\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r91 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", ctx_r91.nDcQualifier)(\"searchFn\", ctx_r91.onSearchQualifer)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r91.ndcValidation(i_r7).controls[\"ndcQual\"].invalid && ctx_r91.ndcValidation(i_r7).controls[\"ndcQual\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Qual is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_28_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r92 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r92.ndcValidation(i_r7).controls[\"ndcQual\"] == null ? null : ctx_r92.ndcValidation(i_r7).controls[\"ndcQual\"].errors[\"required\"]);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r93 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r93.ndcValidation(i_r7).controls[\"ndcQual\"].value && ctx_r93.ndcValidation(i_r7).controls[\"ndcQual\"].value.length > 0 ? ctx_r93.ndcValidation(i_r7).controls[\"ndcQual\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r124 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 89);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_input_33_Template_input_change_0_listener() {\n      i0.ɵɵrestoreView(_r124);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r122 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r122.changeValuesToMarkValidation(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r94 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r94.ndcValidation(i_r7).controls[\"ndcCode\"].invalid && ctx_r94.ndcValidation(i_r7).controls[\"ndcCode\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_34_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Invalid NDC code, it should be a 11 digit code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_34_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_div_34_div_2_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r95 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"] == null ? null : ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"] == null ? null : ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"].errors[\"required\"]) && (ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"] == null ? null : ctx_r95.ndcValidation(i_r7).controls[\"ndcCode\"].errors[\"isInvalid\"]));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r96 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r96.ndcValidation(i_r7).controls[\"ndcCode\"].value && ctx_r96.ndcValidation(i_r7).controls[\"ndcCode\"].value.length > 0 ? ctx_r96.ndcValidation(i_r7).controls[\"ndcCode\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r132 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 90);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_input_40_Template_input_change_0_listener() {\n      i0.ɵɵrestoreView(_r132);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r130 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r130.changeValuesToMarkValidation(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r97 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r97.ndcValidation(i_r7).controls[\"ndcQty\"].invalid && ctx_r97.ndcValidation(i_r7).controls[\"ndcQty\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Quantity is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter valid NDC Quantity. Only numbers and three decimals accepted. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" The field NDC Quantity must be between 0 and 9999999.999. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_41_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_div_41_div_2_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(3, ServiceLineClaimComponent_span_56_tr_1_div_41_div_3_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r137 = i0.ɵɵnextContext(2);\n    const i_r7 = ctx_r137.index;\n    const items_r6 = ctx_r137.$implicit;\n    const ctx_r98 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r98.ndcValidation(i_r7).controls[\"ndcQty\"] == null ? null : ctx_r98.ndcValidation(i_r7).controls[\"ndcQty\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = items_r6.get(\"ndcQty\")) == null ? null : tmp_1_0.invalid) && items_r6.get(\"ndcQty\").errors.isInvalid && !items_r6.get(\"ndcQty\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = items_r6.get(\"ndcQty\")) == null ? null : tmp_2_0.invalid) && !items_r6.get(\"ndcQty\").errors.isInvalid && !items_r6.get(\"ndcQty\").errors.required && (items_r6.get(\"ndcQty\").errors.max || items_r6.get(\"ndcQty\").errors.min));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r99 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r99.ndcValidation(i_r7).controls[\"ndcQty\"].value && ctx_r99.ndcValidation(i_r7).controls[\"ndcQty\"].value.length > 0 ? ctx_r99.ndcValidation(i_r7).controls[\"ndcQty\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_46_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const item_r141 = ctx.item;\n    i0.ɵɵtextInterpolate2(\" \", item_r141.qualifier, \"-\", item_r141.description, \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r144 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 91, 86);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r144);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r142 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r142.changeQual($event, i_r7));\n    })(\"clear\", function ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template_ng_select_clear_0_listener() {\n      i0.ɵɵrestoreView(_r144);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r145 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r145.clear(i_r7));\n    });\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_ng_select_46_ng_template_2_Template, 1, 2, \"ng-template\", 87);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r100 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", ctx_r100.nDcQualifierQty)(\"searchFn\", ctx_r100.onSearchQualifer)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r100.ndcValidation(i_r7).controls[\"ndcQtyQual\"].invalid && ctx_r100.ndcValidation(i_r7).controls[\"ndcQtyQual\"].errors));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" NDC Qty Qual is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_div_47_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r101 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r101.ndcValidation(i_r7).controls[\"ndcQtyQual\"] == null ? null : ctx_r101.ndcValidation(i_r7).controls[\"ndcQtyQual\"].errors[\"required\"]);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r102 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r102.ndcValidation(i_r7).controls[\"ndcQtyQual\"].value && ctx_r102.ndcValidation(i_r7).controls[\"ndcQtyQual\"].value.length > 0 ? ctx_r102.ndcValidation(i_r7).controls[\"ndcQtyQual\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 92);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r104 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r104.ndcValidation(i_r7).controls[\"anesStop2\"].value && ctx_r104.ndcValidation(i_r7).controls[\"anesStop2\"].value.length > 0 ? ctx_r104.ndcValidation(i_r7).controls[\"anesStop2\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_input_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 93);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_1_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r106 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r106.ndcValidation(i_r7).controls[\"anesStop3\"].value && ctx_r106.ndcValidation(i_r7).controls[\"anesStop3\"].value.length > 0 ? ctx_r106.ndcValidation(i_r7).controls[\"anesStop3\"].value : \"-\");\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"service-line-view-label\": a0\n  };\n};\n\nfunction ServiceLineClaimComponent_span_56_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 61);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_1_input_2_Template, 1, 0, \"input\", 62);\n    i0.ɵɵtemplate(3, ServiceLineClaimComponent_span_56_tr_1_span_3_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 61);\n    i0.ɵɵtemplate(5, ServiceLineClaimComponent_span_56_tr_1_input_5_Template, 1, 0, \"input\", 63);\n    i0.ɵɵtemplate(6, ServiceLineClaimComponent_span_56_tr_1_span_6_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 64)(8, \"div\", 65)(9, \"div\", 66);\n    i0.ɵɵtext(10, \" Line Note: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 67);\n    i0.ɵɵtemplate(12, ServiceLineClaimComponent_span_56_tr_1_input_12_Template, 1, 0, \"input\", 68);\n    i0.ɵɵtemplate(13, ServiceLineClaimComponent_span_56_tr_1_span_13_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 66);\n    i0.ɵɵtext(15, \" Anes Start: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 67);\n    i0.ɵɵtemplate(17, ServiceLineClaimComponent_span_56_tr_1_input_17_Template, 1, 0, \"input\", 69);\n    i0.ɵɵtemplate(18, ServiceLineClaimComponent_span_56_tr_1_span_18_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 66);\n    i0.ɵɵtext(20, \" Anes Stop: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 67);\n    i0.ɵɵtemplate(22, ServiceLineClaimComponent_span_56_tr_1_input_22_Template, 1, 0, \"input\", 70);\n    i0.ɵɵtemplate(23, ServiceLineClaimComponent_span_56_tr_1_span_23_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 66);\n    i0.ɵɵtext(25, \" NDC Qual: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 71);\n    i0.ɵɵtemplate(27, ServiceLineClaimComponent_span_56_tr_1_ng_select_27_Template, 3, 5, \"ng-select\", 72);\n    i0.ɵɵtemplate(28, ServiceLineClaimComponent_span_56_tr_1_div_28_Template, 2, 1, \"div\", 33);\n    i0.ɵɵtemplate(29, ServiceLineClaimComponent_span_56_tr_1_span_29_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 66);\n    i0.ɵɵtext(31, \" NDC Code: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 73);\n    i0.ɵɵtemplate(33, ServiceLineClaimComponent_span_56_tr_1_input_33_Template, 1, 3, \"input\", 74);\n    i0.ɵɵtemplate(34, ServiceLineClaimComponent_span_56_tr_1_div_34_Template, 3, 2, \"div\", 33);\n    i0.ɵɵtemplate(35, ServiceLineClaimComponent_span_56_tr_1_span_35_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 65)(37, \"div\", 66);\n    i0.ɵɵtext(38, \" NDC Qty: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 67);\n    i0.ɵɵtemplate(40, ServiceLineClaimComponent_span_56_tr_1_input_40_Template, 1, 3, \"input\", 75);\n    i0.ɵɵtemplate(41, ServiceLineClaimComponent_span_56_tr_1_div_41_Template, 4, 3, \"div\", 33);\n    i0.ɵɵtemplate(42, ServiceLineClaimComponent_span_56_tr_1_span_42_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 66);\n    i0.ɵɵtext(44, \" NDC Qty Qual: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 67);\n    i0.ɵɵtemplate(46, ServiceLineClaimComponent_span_56_tr_1_ng_select_46_Template, 3, 5, \"ng-select\", 76);\n    i0.ɵɵtemplate(47, ServiceLineClaimComponent_span_56_tr_1_div_47_Template, 2, 1, \"div\", 33);\n    i0.ɵɵtemplate(48, ServiceLineClaimComponent_span_56_tr_1_span_48_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 73);\n    i0.ɵɵtemplate(50, ServiceLineClaimComponent_span_56_tr_1_input_50_Template, 1, 0, \"input\", 77);\n    i0.ɵɵtemplate(51, ServiceLineClaimComponent_span_56_tr_1_span_51_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 67);\n    i0.ɵɵtemplate(53, ServiceLineClaimComponent_span_56_tr_1_input_53_Template, 1, 0, \"input\", 78);\n    i0.ɵɵtemplate(54, ServiceLineClaimComponent_span_56_tr_1_span_54_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(55, \"div\", 73)(56, \"div\", 73)(57, \"div\", 73)(58, \"div\", 73);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext().index;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(35, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(37, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(39, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcQual\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcCode\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(43, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcQty\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c1, ctx_r8.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.ndcValidation(i_r7).controls[\"ndcQtyQual\"].errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.claimFormData.isViewClaim);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_input_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 97);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext(2).$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_0_0.invalid) && (items_r6.get(\"ndcUnitPrice\").dirty || items_r6.get(\"ndcUnitPrice\").touched) && ((tmp_0_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid NDC Unit Price. Only numbers and three decimals accepted. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The field NDC Unit Price must be between 0 and 9999999.999. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r7 = i0.ɵɵnextContext(2).index;\n    const ctx_r157 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!ctx_r157.ndcValidation(i_r7).controls[\"ndcUnitPrice\"].value && ctx_r157.ndcValidation(i_r7).controls[\"ndcUnitPrice\"].value.length > 0 ? ctx_r157.ndcValidation(i_r7).controls[\"ndcUnitPrice\"].value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_tr_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 94);\n    i0.ɵɵtext(2, \"NDC Unit Price:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 95);\n    i0.ɵɵtemplate(4, ServiceLineClaimComponent_span_56_tr_2_input_4_Template, 1, 3, \"input\", 96);\n    i0.ɵɵtemplate(5, ServiceLineClaimComponent_span_56_tr_2_div_5_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(6, ServiceLineClaimComponent_span_56_tr_2_div_6_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(7, ServiceLineClaimComponent_span_56_tr_2_span_7_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r9.claimFormData.isViewClaim));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_2_0.invalid) && items_r6.get(\"ndcUnitPrice\").errors.isInvalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = items_r6.get(\"ndcUnitPrice\")) == null ? null : tmp_3_0.invalid) && !items_r6.get(\"ndcUnitPrice\").errors.isInvalid && (items_r6.get(\"ndcUnitPrice\").errors.max || items_r6.get(\"ndcUnitPrice\").errors.min));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.claimFormData.isViewClaim);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r162 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 98);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_span_56_input_7_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r162);\n      i0.ɵɵnextContext();\n\n      const _r11 = i0.ɵɵreference(9);\n\n      return i0.ɵɵresetView(_r11.open());\n    })(\"blur\", function ServiceLineClaimComponent_span_56_input_7_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r162);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r163 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r163.dosFromChangedEvent(i_r7, \"dateServiceFrom\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n\n    const _r11 = i0.ɵɵreference(9);\n\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_3_0;\n    i0.ɵɵproperty(\"min\", ctx_r10.minDate)(\"max\", ctx_r10.todayDate)(\"matDatepicker\", _r11)(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ((tmp_3_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_3_0.invalid) && (items_r6.get(\"dateServiceFrom\").dirty || items_r6.get(\"dateServiceFrom\").touched) && ((tmp_3_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_3_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please select date of service from. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" From date Should be less than To Date. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" DOS From should be greater than or equal to Date of Current Illness. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Select a date greater than or equal to 10-01-2015. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", !!items_r6.get(\"dateServiceFrom\").value && items_r6.get(\"dateServiceFrom\").value.length > 0 ? i0.ɵɵpipeBind2(2, 1, items_r6.get(\"dateServiceFrom\").value, \"MM/dd/yyyy\") : \"-\", \"\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r168 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 99);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_span_56_input_18_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r168);\n      i0.ɵɵnextContext();\n\n      const _r18 = i0.ɵɵreference(20);\n\n      return i0.ɵɵresetView(_r18.open());\n    })(\"blur\", function ServiceLineClaimComponent_span_56_input_18_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r168);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r169 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r169.checkDateValidations(i_r7, \"dateServiceTo\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n\n    const _r18 = i0.ɵɵreference(20);\n\n    const ctx_r17 = i0.ɵɵnextContext();\n    let tmp_3_0;\n    i0.ɵɵproperty(\"min\", ctx_r17.minDate)(\"max\", ctx_r17.todayDate)(\"matDatepicker\", _r18)(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ((tmp_3_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_3_0.invalid) && (items_r6.get(\"dateServiceTo\").dirty || items_r6.get(\"dateServiceTo\").touched) && ((tmp_3_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_3_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please select date of service to. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" To date Should be greater than From Date. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Select a date greater than or equal to 10-01-2015. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", !!items_r6.get(\"dateServiceTo\").value && items_r6.get(\"dateServiceTo\").value.length > 0 ? i0.ɵɵpipeBind2(2, 1, items_r6.get(\"dateServiceTo\").value, \"MM/dd/yyyy\") : \"-\", \"\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_28_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r174 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r174);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, item_r174.text), \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r176 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 100);\n    i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_span_56_ng_select_28_Template_ng_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r176);\n      const ctx_r175 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r175.placeOfServiceChanges($event));\n    });\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_28_ng_option_1_Template, 3, 4, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r23 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_0_0 = items_r6.get(\"locationOfService\")) == null ? null : tmp_0_0.invalid) && (items_r6.get(\"locationOfService\").dirty || items_r6.get(\"locationOfService\").touched) && ((tmp_0_0 = items_r6.get(\"locationOfService\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r23.allPlaceOfServices);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Place of Service is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"locationOfService\").value && items_r6.get(\"locationOfService\").value.length > 0 ? items_r6.get(\"locationOfService\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_34_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r180 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r180);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, item_r180), \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 103);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_34_ng_option_1_Template, 3, 4, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r26.cmg);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"emg\").value && items_r6.get(\"emg\").value.length > 0 ? items_r6.get(\"emg\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_39_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r183 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r183);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", item_r183.cpt, \" - \", item_r183.shortDescription, \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r186 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-select\", 104);\n    i0.ɵɵlistener(\"ngModelChange\", function ServiceLineClaimComponent_span_56_ng_select_39_Template_ng_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r186);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r184 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r184.OnCPTCodeChange($event, i_r7));\n    });\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_39_ng_option_1_Template, 2, 3, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵproperty(\"virtualScroll\", true)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.invalid) && (items_r6.get(\"cpt\").dirty || items_r6.get(\"cpt\").touched) && (((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.required) || ((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.invalidCptlength) || ((tmp_1_0 = items_r6.get(\"cpt\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.invalidCpt))));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r28.cPTCodes);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Code is Required \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Invalid CPT code entered. Please provide a valid CPT code for accurate processing. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Give Valid CPT Code \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"cpt\").value && items_r6.get(\"cpt\").value.length > 0 ? items_r6.get(\"cpt\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 105);\n  }\n\n  if (rf & 2) {\n    const ctx_r189 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r189.index;\n    const items_r6 = ctx_r189.$implicit;\n    const ctx_r33 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier1\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r33.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m1\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m1\").dirty || items_r6.get(\"m1\").touched) && ((tmp_2_0 = items_r6.get(\"m1\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier1 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m1\").value && items_r6.get(\"m1\").value.length > 0 ? items_r6.get(\"m1\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 106);\n  }\n\n  if (rf & 2) {\n    const ctx_r191 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r191.index;\n    const items_r6 = ctx_r191.$implicit;\n    const ctx_r36 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier2\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r36.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m2\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m2\").dirty || items_r6.get(\"m2\").touched) && ((tmp_2_0 = items_r6.get(\"m2\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier2 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m2\").value && items_r6.get(\"m2\").value.length > 0 ? items_r6.get(\"m2\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 107);\n  }\n\n  if (rf & 2) {\n    const ctx_r193 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r193.index;\n    const items_r6 = ctx_r193.$implicit;\n    const ctx_r39 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier3\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r39.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m3\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m3\").dirty || items_r6.get(\"m3\").touched) && ((tmp_2_0 = items_r6.get(\"m3\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier3 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m3\").value && items_r6.get(\"m3\").value.length > 0 ? items_r6.get(\"m3\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 108);\n  }\n\n  if (rf & 2) {\n    const ctx_r195 = i0.ɵɵnextContext();\n    const i_r7 = ctx_r195.index;\n    const items_r6 = ctx_r195.$implicit;\n    const ctx_r42 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Modifier4\", i_r7, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r42.isControl)(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ((tmp_2_0 = items_r6.get(\"m4\")) == null ? null : tmp_2_0.invalid) && (items_r6.get(\"m4\").dirty || items_r6.get(\"m4\").touched) && ((tmp_2_0 = items_r6.get(\"m4\")) == null ? null : tmp_2_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Modifier4 must be of 2 characters in length \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"m4\").value && items_r6.get(\"m4\").value.length > 0 ? items_r6.get(\"m4\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 109);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r45 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵproperty(\"disabled\", ctx_r45.isControl)(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ((tmp_1_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_1_0.invalid) && (((tmp_1_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_1_0.dirty) || ((tmp_1_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_1_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer1\").value && items_r6.get(\"diagnosispointer1\").value.length > 0 ? items_r6.get(\"diagnosispointer1\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 110);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_0_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer2\").value && items_r6.get(\"diagnosispointer2\").value.length > 0 ? items_r6.get(\"diagnosispointer2\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 111);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_0_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer3\").value && items_r6.get(\"diagnosispointer3\").value.length > 0 ? items_r6.get(\"diagnosispointer3\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 112);\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_0_0.touched))));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please enter valid Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Dupication Of Diagnosis Pointers is not allowed \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" Please Maintain Continuity while entering Diagnosis Pointer \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"diagnosispointer4\").value && items_r6.get(\"diagnosispointer4\").value.length > 0 ? items_r6.get(\"diagnosispointer4\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r207 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 113);\n    i0.ɵɵlistener(\"blur\", function ServiceLineClaimComponent_span_56_input_92_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r207);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r205 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r205.calculateTotalAmount($event, i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.touched)) && ((tmp_0_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The unit charges field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"unitCharges\").value && items_r6.get(\"unitCharges\").value.length > 0 ? items_r6.get(\"unitCharges\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r212 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 114);\n    i0.ɵɵlistener(\"blur\", function ServiceLineClaimComponent_span_56_input_98_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r212);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r210 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r210.calculateTotalAmount($event, i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.touched)) && ((tmp_0_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The days and units field is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtext(1, \" The field days and units must be between 1 and 99999999. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"dayUnitChanges\").value && items_r6.get(\"dayUnitChanges\").value.length > 0 ? items_r6.get(\"dayUnitChanges\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 115);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"total\").value && items_r6.get(\"total\").value.length > 0 ? items_r6.get(\"total\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_110_ng_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-option\", 102);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r217 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r217);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, item_r217), \" \");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_ng_select_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng-select\", 116);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_ng_select_110_ng_option_1_Template, 3, 4, \"ng-option\", 101);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r75.epsdt);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"ePSDT\").value && items_r6.get(\"ePSDT\").value.length > 0 ? items_r6.get(\"ePSDT\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_input_117_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r220 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 117);\n    i0.ɵɵlistener(\"input\", function ServiceLineClaimComponent_span_56_input_117_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r220);\n      const ctx_r219 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r219.renderingProvider($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ((tmp_0_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_0_0.errors)));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_118_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter Rendering Provider NPI \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_118_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Rendering Provider NPI must be of 10 characters in length. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_div_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_div_118_div_1_Template, 2, 0, \"div\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_div_118_div_2_Template, 2, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_0_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_1_0.hasError(\"maxlength\")) || ((tmp_1_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_1_0.hasError(\"minlength\")));\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_span_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const items_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(!!items_r6.get(\"jRenderingProviderId\").value && items_r6.get(\"jRenderingProviderId\").value.length > 0 ? items_r6.get(\"jRenderingProviderId\").value : \"-\");\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_td_120_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r229 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_span_56_td_120_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r229);\n      const i_r7 = i0.ɵɵnextContext(2).index;\n      const ctx_r227 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r227.removeService(i_r7));\n    });\n    i0.ɵɵtext(1, \"X\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_td_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"div\", 118);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_td_120_button_2_Template, 2, 0, \"button\", 119);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r80 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r80.serviceLine.length > 1);\n  }\n}\n\nfunction ServiceLineClaimComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtemplate(1, ServiceLineClaimComponent_span_56_tr_1_Template, 59, 47, \"tr\", 29);\n    i0.ɵɵtemplate(2, ServiceLineClaimComponent_span_56_tr_2_Template, 8, 7, \"tr\", 29);\n    i0.ɵɵelementStart(3, \"tr\")(4, \"td\")(5, \"div\", 11)(6, \"div\", 30);\n    i0.ɵɵtemplate(7, ServiceLineClaimComponent_span_56_input_7_Template, 1, 6, \"input\", 31);\n    i0.ɵɵelement(8, \"mat-datepicker\", null, 32);\n    i0.ɵɵtemplate(10, ServiceLineClaimComponent_span_56_div_10_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(11, ServiceLineClaimComponent_span_56_div_11_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(12, ServiceLineClaimComponent_span_56_div_12_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(13, ServiceLineClaimComponent_span_56_div_13_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(14, ServiceLineClaimComponent_span_56_span_14_Template, 3, 4, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"div\", 11)(17, \"div\", 30);\n    i0.ɵɵtemplate(18, ServiceLineClaimComponent_span_56_input_18_Template, 1, 6, \"input\", 35);\n    i0.ɵɵelement(19, \"mat-datepicker\", null, 36);\n    i0.ɵɵtemplate(21, ServiceLineClaimComponent_span_56_div_21_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(22, ServiceLineClaimComponent_span_56_div_22_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(23, ServiceLineClaimComponent_span_56_div_23_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(24, ServiceLineClaimComponent_span_56_span_24_Template, 3, 4, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"td\", 37)(26, \"div\", 11)(27, \"div\", 30);\n    i0.ɵɵtemplate(28, ServiceLineClaimComponent_span_56_ng_select_28_Template, 2, 4, \"ng-select\", 38);\n    i0.ɵɵtemplate(29, ServiceLineClaimComponent_span_56_div_29_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(30, ServiceLineClaimComponent_span_56_span_30_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"td\")(32, \"div\", 11)(33, \"div\", 30);\n    i0.ɵɵtemplate(34, ServiceLineClaimComponent_span_56_ng_select_34_Template, 2, 1, \"ng-select\", 39);\n    i0.ɵɵtemplate(35, ServiceLineClaimComponent_span_56_span_35_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"td\", 37)(37, \"div\", 11)(38, \"div\", 30);\n    i0.ɵɵtemplate(39, ServiceLineClaimComponent_span_56_ng_select_39_Template, 2, 5, \"ng-select\", 40);\n    i0.ɵɵtemplate(40, ServiceLineClaimComponent_span_56_div_40_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(41, ServiceLineClaimComponent_span_56_div_41_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(42, ServiceLineClaimComponent_span_56_div_42_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(43, ServiceLineClaimComponent_span_56_span_43_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"td\", 41)(45, \"div\", 42)(46, \"div\", 30);\n    i0.ɵɵtemplate(47, ServiceLineClaimComponent_span_56_input_47_Template, 1, 5, \"input\", 43);\n    i0.ɵɵtemplate(48, ServiceLineClaimComponent_span_56_div_48_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(49, ServiceLineClaimComponent_span_56_span_49_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 30);\n    i0.ɵɵtemplate(51, ServiceLineClaimComponent_span_56_input_51_Template, 1, 5, \"input\", 44);\n    i0.ɵɵtemplate(52, ServiceLineClaimComponent_span_56_div_52_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(53, ServiceLineClaimComponent_span_56_span_53_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 30);\n    i0.ɵɵtemplate(55, ServiceLineClaimComponent_span_56_input_55_Template, 1, 5, \"input\", 45);\n    i0.ɵɵtemplate(56, ServiceLineClaimComponent_span_56_div_56_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(57, ServiceLineClaimComponent_span_56_span_57_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 30);\n    i0.ɵɵtemplate(59, ServiceLineClaimComponent_span_56_input_59_Template, 1, 5, \"input\", 46);\n    i0.ɵɵtemplate(60, ServiceLineClaimComponent_span_56_div_60_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(61, ServiceLineClaimComponent_span_56_span_61_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"td\", 41)(63, \"div\", 47)(64, \"div\", 30);\n    i0.ɵɵtemplate(65, ServiceLineClaimComponent_span_56_input_65_Template, 1, 4, \"input\", 48);\n    i0.ɵɵtemplate(66, ServiceLineClaimComponent_span_56_div_66_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(67, ServiceLineClaimComponent_span_56_div_67_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(68, ServiceLineClaimComponent_span_56_div_68_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(69, ServiceLineClaimComponent_span_56_div_69_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(70, ServiceLineClaimComponent_span_56_span_70_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"div\", 30);\n    i0.ɵɵtemplate(72, ServiceLineClaimComponent_span_56_input_72_Template, 1, 3, \"input\", 49);\n    i0.ɵɵtemplate(73, ServiceLineClaimComponent_span_56_div_73_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(74, ServiceLineClaimComponent_span_56_div_74_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(75, ServiceLineClaimComponent_span_56_div_75_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(76, ServiceLineClaimComponent_span_56_span_76_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 30);\n    i0.ɵɵtemplate(78, ServiceLineClaimComponent_span_56_input_78_Template, 1, 3, \"input\", 50);\n    i0.ɵɵtemplate(79, ServiceLineClaimComponent_span_56_div_79_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(80, ServiceLineClaimComponent_span_56_div_80_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(81, ServiceLineClaimComponent_span_56_div_81_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(82, ServiceLineClaimComponent_span_56_span_82_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"div\", 30);\n    i0.ɵɵtemplate(84, ServiceLineClaimComponent_span_56_input_84_Template, 1, 3, \"input\", 51);\n    i0.ɵɵtemplate(85, ServiceLineClaimComponent_span_56_div_85_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(86, ServiceLineClaimComponent_span_56_div_86_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(87, ServiceLineClaimComponent_span_56_div_87_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(88, ServiceLineClaimComponent_span_56_span_88_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(89, \"td\", 52)(90, \"div\", 11)(91, \"div\", 30);\n    i0.ɵɵtemplate(92, ServiceLineClaimComponent_span_56_input_92_Template, 1, 3, \"input\", 53);\n    i0.ɵɵtemplate(93, ServiceLineClaimComponent_span_56_div_93_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(94, ServiceLineClaimComponent_span_56_span_94_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"td\", 52)(96, \"div\", 11)(97, \"div\", 30);\n    i0.ɵɵtemplate(98, ServiceLineClaimComponent_span_56_input_98_Template, 1, 3, \"input\", 54);\n    i0.ɵɵtemplate(99, ServiceLineClaimComponent_span_56_div_99_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(100, ServiceLineClaimComponent_span_56_div_100_Template, 2, 0, \"div\", 33);\n    i0.ɵɵtemplate(101, ServiceLineClaimComponent_span_56_span_101_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(102, \"td\", 52)(103, \"div\", 11)(104, \"div\", 30);\n    i0.ɵɵtemplate(105, ServiceLineClaimComponent_span_56_input_105_Template, 1, 0, \"input\", 55);\n    i0.ɵɵtemplate(106, ServiceLineClaimComponent_span_56_span_106_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(107, \"td\")(108, \"div\", 11)(109, \"div\", 30);\n    i0.ɵɵtemplate(110, ServiceLineClaimComponent_span_56_ng_select_110_Template, 2, 1, \"ng-select\", 56);\n    i0.ɵɵtemplate(111, ServiceLineClaimComponent_span_56_span_111_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(112, \"td\")(113, \"div\", 57);\n    i0.ɵɵtext(114, \" NPI \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(115, \"td\", 58)(116, \"div\", 59);\n    i0.ɵɵtemplate(117, ServiceLineClaimComponent_span_56_input_117_Template, 1, 3, \"input\", 60);\n    i0.ɵɵtemplate(118, ServiceLineClaimComponent_span_56_div_118_Template, 3, 2, \"div\", 33);\n    i0.ɵɵtemplate(119, ServiceLineClaimComponent_span_56_span_119_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(120, ServiceLineClaimComponent_span_56_td_120_Template, 3, 1, \"td\", 29);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const items_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    let tmp_15_0;\n    let tmp_20_0;\n    let tmp_21_0;\n    let tmp_22_0;\n    let tmp_25_0;\n    let tmp_28_0;\n    let tmp_31_0;\n    let tmp_34_0;\n    let tmp_37_0;\n    let tmp_38_0;\n    let tmp_39_0;\n    let tmp_40_0;\n    let tmp_43_0;\n    let tmp_44_0;\n    let tmp_45_0;\n    let tmp_48_0;\n    let tmp_49_0;\n    let tmp_50_0;\n    let tmp_53_0;\n    let tmp_54_0;\n    let tmp_55_0;\n    let tmp_58_0;\n    let tmp_61_0;\n    let tmp_62_0;\n    let tmp_70_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r7);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f.showNDC.value === \"Yes\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f.showNDC.value === \"Yes\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_4_0.invalid) && items_r6.get(\"dateServiceFrom\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_5_0.invalid) && items_r6.get(\"dateServiceFrom\").errors.invalidDate && !items_r6.get(\"dateServiceFrom\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_6_0.invalid) && items_r6.get(\"dateServiceFrom\").errors.currentIllnessError && !items_r6.get(\"dateServiceFrom\").errors.invalidDate && !items_r6.get(\"dateServiceFrom\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = items_r6.get(\"dateServiceFrom\")) == null ? null : tmp_7_0.invalid) && !items_r6.get(\"dateServiceFrom\").errors.currentIllnessError && !items_r6.get(\"dateServiceFrom\").errors.invalidDate && !items_r6.get(\"dateServiceFrom\").errors.required && items_r6.get(\"dateServiceFrom\").errors.invalidFromDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_10_0.invalid) && items_r6.get(\"dateServiceTo\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_11_0.invalid) && items_r6.get(\"dateServiceTo\").errors.invalidDate && !items_r6.get(\"dateServiceTo\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = items_r6.get(\"dateServiceTo\")) == null ? null : tmp_12_0.invalid) && !items_r6.get(\"dateServiceTo\").errors.invalidDate && !items_r6.get(\"dateServiceTo\").errors.required && items_r6.get(\"dateServiceTo\").errors.invalidFromDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isEditClaim || ctx_r2.claimFormData.isAddClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = items_r6.get(\"locationOfService\")) == null ? null : tmp_15_0.invalid) && items_r6.get(\"locationOfService\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isEditClaim || ctx_r2.claimFormData.isAddClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = items_r6.get(\"cpt\")) == null ? null : tmp_20_0.invalid) && items_r6.get(\"cpt\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = items_r6.get(\"cpt\")) == null ? null : tmp_21_0.invalid) && items_r6.get(\"cpt\").errors.invalidCptlength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = items_r6.get(\"cpt\")) == null ? null : tmp_22_0.invalid) && items_r6.get(\"cpt\").errors.invalidCpt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_25_0 = items_r6.get(\"m1\")) == null ? null : tmp_25_0.invalid) && items_r6.get(\"m1\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_28_0 = items_r6.get(\"m2\")) == null ? null : tmp_28_0.invalid) && items_r6.get(\"m2\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_31_0 = items_r6.get(\"m3\")) == null ? null : tmp_31_0.invalid) && items_r6.get(\"m3\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_34_0 = items_r6.get(\"m4\")) == null ? null : tmp_34_0.invalid) && items_r6.get(\"m4\").errors);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_37_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_37_0.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_38_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_38_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_39_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_39_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_40_0 = items_r6.get(\"diagnosispointer1\")) == null ? null : tmp_40_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_43_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_43_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_44_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_44_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_45_0 = items_r6.get(\"diagnosispointer2\")) == null ? null : tmp_45_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_48_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_48_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_49_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_49_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_50_0 = items_r6.get(\"diagnosispointer3\")) == null ? null : tmp_50_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_53_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_53_0.hasError(\"numberMax\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_54_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_54_0.hasError(\"dublicate\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_55_0 = items_r6.get(\"diagnosispointer4\")) == null ? null : tmp_55_0.hasError(\"continue\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_58_0 = items_r6.get(\"unitCharges\")) == null ? null : tmp_58_0.invalid) && items_r6.get(\"unitCharges\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_61_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_61_0.invalid) && items_r6.get(\"dayUnitChanges\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_62_0 = items_r6.get(\"dayUnitChanges\")) == null ? null : tmp_62_0.invalid) && (items_r6.get(\"dayUnitChanges\").errors.max || items_r6.get(\"dayUnitChanges\").errors.min));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(73, _c1, ctx_r2.claimFormData.isViewClaim));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_70_0 = items_r6.get(\"jRenderingProviderId\")) == null ? null : tmp_70_0.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isViewClaim);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.claimFormData.isEditClaim || ctx_r2.claimFormData.isAddClaim);\n  }\n}\n\nfunction ServiceLineClaimComponent_button_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r231 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function ServiceLineClaimComponent_button_57_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r231);\n      const ctx_r230 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r230.addnewService());\n    });\n    i0.ɵɵelementStart(1, \"i\", 122);\n    i0.ɵɵtext(2, \"playlist_add\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \"Add Service\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"width\": a0\n  };\n};\n\nexport class ServiceLineClaimComponent {\n  constructor(serviceLineform, placeOfService, subjectService, cptservice, dialog, cacheService, cdRef, spinner) {\n    this.serviceLineform = serviceLineform;\n    this.placeOfService = placeOfService;\n    this.subjectService = subjectService;\n    this.cptservice = cptservice;\n    this.dialog = dialog;\n    this.cacheService = cacheService;\n    this.cdRef = cdRef;\n    this.spinner = spinner;\n    this.todayDate = new Date();\n    this.minDate = new Date('2015-10-01');\n    this.diagnosisPointerBlur = new EventEmitter();\n    this.dosFromChanged = new EventEmitter();\n    this.calculateTotal = new EventEmitter();\n    this.allPlaceOfServices = [];\n    this.allICDCode = [{\n      text: \"abcdef\",\n      value: \"abcdef\",\n      addDate: '',\n      termDate: ''\n    }];\n    this.isControl = true;\n    this.nDcQualifierQty = [];\n    this.nDcQualifier = [];\n    this.allCPTCode = [];\n    this.allCPTCharges = [];\n    this.viewHistoryCPTList = [];\n    this.cmg = [\"Yes\", \"No\"];\n    this.epsdt = [\"Yes\", \"No\"];\n    this.isSubmited = false;\n    this.invalidCptCode = [];\n    this.minimumcharacterTohitAPI = 3;\n    this.cPTCodes = [];\n    this.addServiceShow = false;\n    this.dateServiceFrom = [];\n    this.dateServiceTo = [];\n    this.oldServiceDateFrom = '';\n    this.oldServiceDateTo = '';\n    this.subjectService.getBillingProviderNPIChange().subscribe(res => {\n      if (res || res == '') {\n        this.changeRenderProviderNo(res);\n        this.subjectService.resetServicelineProviderNPIChange();\n      }\n    });\n  }\n\n  ngOnInit() {\n    this.spinner.show('child');\n    this.cdRef.detectChanges();\n    setTimeout(() => {\n      this.patchValue(); // Heavy synchronous form patch\n\n      this.nDcQualifier = this.claimFormData.ndcQualifierDataByType;\n      this.nDcQualifierQty = this.claimFormData.ndcQtyQualifierDataByType;\n\n      if (this.claimFormData.isAddClaim) {\n        this.serviceLine.controls.forEach(control => {\n          control.get('proceduceC')?.setValue('HC', {\n            emitEvent: false\n          });\n        });\n      }\n\n      this.spinner.hide('child');\n    }, 1000);\n  }\n\n  createForm() {\n    this.serviceLineInfo = this.serviceLineform.group({\n      showNDC: new FormControl('No'),\n      serviceLines: this.serviceLineform.array([])\n    });\n    return this.serviceLineInfo;\n  }\n\n  get serviceLine() {\n    return this.serviceLineInfo.get('serviceLines');\n  }\n\n  get f() {\n    return this.serviceLineInfo.controls;\n  } // allPlaceOfServices: AllPlaceOfServices[] = [];\n\n\n  placeOfServiceData() {\n    if (!!this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices)) {\n      this.allPlaceOfServices = this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices);\n    } else {\n      this.placeOfService.fetchchAllPlaceOfServices().subscribe(res => {\n        this.allPlaceOfServices = res;\n      });\n    }\n  }\n\n  patchValue() {\n    if (this.claimFormData.isAddClaim) {\n      const renderingNPI = this.claimFormData?.provider?.renderingProvider?.providerNPI;\n      const posCode = this.claimFormData?.placeOfServiceCode;\n      const dOSFrom = new Date(this.memberProfile?.dOSFrom);\n      const dOSTo = this.memberProfile?.dOSTo;\n      const groups = this.serviceLineInfos?.serviceLines.map(ele => {\n        const charges = Number(ele.charges) || 0;\n        const units = Number(ele.daysunit) || 0;\n        const total = (units * charges).toFixed(2);\n        return this.serviceLineform.group({\n          dateServiceFrom: [{\n            value: dOSFrom,\n            disabled: true\n          }],\n          dateServiceTo: [{\n            value: dOSTo,\n            disabled: true\n          }],\n          locationOfService: [{\n            value: posCode,\n            disabled: false\n          }],\n          emg: [{\n            value: ele.eMG,\n            disabled: false\n          }],\n          cpt: [{\n            value: ele.cptCode,\n            disabled: false\n          }, Validators.required],\n          desc: [{\n            value: ele.desc\n          }],\n          m1: [{\n            value: ele.m1,\n            disabled: false\n          }],\n          m2: [{\n            value: ele.m2,\n            disabled: false\n          }],\n          m3: [{\n            value: ele.m3,\n            disabled: false\n          }],\n          m4: [{\n            value: ele.m4,\n            disabled: false\n          }],\n          diagnosispointer1: [{\n            value: Number(ele.d1).toString(),\n            disabled: false\n          }],\n          diagnosispointer2: [{\n            value: ele.d2 ? Number(ele.d2).toString() : '',\n            disabled: false\n          }],\n          diagnosispointer3: [{\n            value: ele.d3 ? Number(ele.d3).toString() : '',\n            disabled: false\n          }],\n          diagnosispointer4: [{\n            value: ele.d4 ? Number(ele.d4).toString() : '',\n            disabled: false\n          }],\n          unitCharges: [{\n            value: ele.charges,\n            disabled: false\n          }, [Validators.required]],\n          dayUnitChanges: [{\n            value: ele.daysunit,\n            disabled: false\n          }, [Validators.required, Validators.max(99999999), Validators.min(1)]],\n          total: [{\n            value: total,\n            disabled: true\n          }],\n          ePSDT: [{\n            value: ele.ePSDT,\n            disabled: false\n          }],\n          jRenderingProviderId: [{\n            value: renderingNPI,\n            disabled: true\n          }],\n          proceduceC: '',\n          proceduceCount: '',\n          ndcUnitPrice: '',\n          lineNote: '',\n          ndcQtyQual: null,\n          anesStart: '',\n          ndcQty: '',\n          anesStop1: '',\n          anesStop2: '',\n          anesStop3: '',\n          ndcQual: null,\n          ndcCode: ''\n        });\n      });\n      groups.forEach(group => this.serviceLine.push(group));\n    }\n\n    if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim) {\n      this.fetchCPTData();\n      this.fetchchAllCPTCharges();\n      this.placeOfServiceData();\n    } // View Claim Show NDC radio button enabled \n\n\n    if (this.claimFormData.isViewClaim) {\n      this.patchServiceLineData();\n      this.serviceLineInfo.disable();\n      this.serviceLineInfo.controls[\"showNDC\"].enable();\n    }\n\n    this.dateValidators();\n  }\n\n  showNDC() {\n    if (this.claimFormData.isAddClaim) {\n      for (let index = 0; index < this.serviceLineInfos?.serviceLines.length; index++) {\n        this.serviceLine.at(index).get('proceduceC').patchValue('HC');\n        this.serviceLine.at(index).get('proceduceCount').patchValue(index + 1);\n      }\n    }\n  }\n\n  hideNDC() {}\n\n  setValidator() {\n    this.isSubmited;\n\n    if (this.serviceLineInfo.invalid) {\n      submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n      return false;\n    }\n\n    return true;\n  }\n\n  OnCPTCodeChange(codeValue, index) {\n    let dayUnitChanges = this.serviceLine.at(index).get('dayUnitChanges').value ? this.serviceLine.at(index).get('dayUnitChanges').value : '1';\n    let unitCharges = this.serviceLine.at(index).get('unitCharges').value;\n\n    if (codeValue) {\n      let cptCharge = this.allCPTCharges.filter(item => item.code == codeValue)[0]?.charge;\n\n      if (!!cptCharge) {\n        unitCharges = cptCharge;\n      } else {\n        unitCharges = this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n      }\n\n      let cptDescription = this.cptCodes.find(item => item.cpt == codeValue).description;\n      this.serviceLine.at(index).patchValue({\n        dayUnitChanges: dayUnitChanges,\n        total: (dayUnitChanges * unitCharges).toFixed(2),\n        desc: {\n          value: cptDescription\n        },\n        proceduceC: 'HC',\n        unitCharges: Number.parseFloat(unitCharges).toFixed(2)\n      });\n      this.invalidCptCode.forEach(element => {\n        if (element === codeValue) {\n          this.serviceLine.at(index).get('cpt').setErrors({\n            invalidCpt: true\n          });\n        }\n      });\n    } else {\n      this.serviceLine.at(index).patchValue({\n        dayUnitChanges: null,\n        total: (0 * unitCharges).toFixed(2)\n      });\n    }\n\n    this.calculateTotal.emit();\n    this.validateCPTCode();\n  }\n\n  fetchchAllCPTCharges() {\n    if (this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges)) {\n      this.allCPTCharges = this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges);\n      return;\n    }\n\n    this.cptservice.fetchchAllCPTCharges().pipe(first()).subscribe(response => {\n      this.allCPTCharges = response.content;\n    });\n  }\n\n  validateForm() {\n    for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\n      if (this.serviceLine.at(index).get('jRenderingProviderId').value.length != 10) {\n        this.serviceLine.at(this.serviceLineInfo.controls['serviceLines'].value.length - 1).get('jRenderingProviderId').setErrors({\n          maxlength: true\n        });\n      }\n\n      this.checkDateValidations(index, 'dateServiceFrom');\n      this.checkDateFromValidation(index, 'dateServiceTo');\n      this.ndcQualChange(index);\n      this.changeValuesToMarkValidation(index);\n      this.validateCPTCode();\n    }\n\n    return this.setValidator();\n  }\n\n  changeQual(e, index) {\n    if (e) {\n      if (!this.serviceLine.at(index).get('ndcCode').value) this.serviceLine.at(index).get('ndcCode').setErrors({\n        required: true\n      });\n      if (!this.serviceLine.at(index).get('ndcQty').value) this.serviceLine.at(index).get('ndcQty').setErrors({\n        required: true\n      });\n      if (!this.serviceLine.at(index).get('ndcQual').value) this.serviceLine.at(index).get('ndcQual').setErrors({\n        required: true\n      });\n    } else {\n      this.serviceLine.at(index).get('ndcCode').setErrors(null);\n      this.serviceLine.at(index).get('ndcQty').setErrors(null);\n      this.serviceLine.at(index).get('ndcQual').setErrors(null);\n    }\n\n    this.changeValuesToMarkValidation(index);\n  }\n\n  changeValuesToMarkValidation(index) {\n    let fields = ['ndcCode', 'ndcQty', 'ndcQual', 'ndcQtyQual'];\n    let validatorToBeApplied = false;\n\n    for (const field of fields) {\n      validatorToBeApplied = validatorToBeApplied || !!this.serviceLine.at(index).get(field).value;\n    }\n\n    if (validatorToBeApplied) {\n      for (const field of fields) {\n        if (!!!this.serviceLine.at(index).get(field).value) {\n          this.serviceLine.at(index).get(field).setErrors({\n            required: true\n          });\n          this.serviceLine.at(index).get(field).setValidators(Validators.required);\n        }\n      }\n    } else {\n      for (const field of fields) {\n        this.serviceLine.at(index).get(field).removeValidators(Validators.required);\n\n        if (this.serviceLine.at(index).get(field).hasError('required')) {\n          delete this.serviceLine.at(index).get(field).errors['required'];\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\n        }\n      }\n    } // fields = fields.filter(field => field != fieldName);\n    // if (!!this.serviceLine.at(index).get(fieldName).value) {\n    //   for (const field of fields) {\n    //     if (!(!!this.serviceLine.at(index).get(field).value)) {\n    //       this.serviceLine.at(index).get(field).setErrors({required: true})\n    //     } else {\n    //       if (this.serviceLine.at(index).get(field).hasError('required')) {\n    //         delete this.serviceLine.at(index).get(field).errors['required'];\n    //         this.serviceLine.at(index).get(field).updateValueAndValidity();\n    //       }\n    //     }\n    //   }\n    // }\n\n  }\n\n  clear(index) {\n    this.serviceLine.at(index).get('ndcCode').setErrors(null);\n    this.serviceLine.at(index).get('ndcQty').setErrors(null);\n    this.serviceLine.at(index).get('ndcQual').setErrors(null);\n  }\n\n  ndcValidation(index) {\n    let formArr = this.serviceLineInfo.controls['serviceLines'];\n    const formGroup = formArr.controls[index];\n    return formGroup;\n  }\n\n  calculateTotalAmount(e, i) {\n    let unitCharges = this.serviceLine.at(i).get('unitCharges').value;\n\n    if (unitCharges == null || unitCharges == undefined || unitCharges.length == 0) {\n      unitCharges = this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n      this.serviceLine.at(i).get('unitCharges').setValue(unitCharges);\n    }\n\n    let dayUnitChanges = this.serviceLine.at(i).get('dayUnitChanges').value;\n    this.serviceLine.at(i).patchValue({\n      total: Number.parseFloat((dayUnitChanges * unitCharges).toString()).toFixed(2)\n    });\n    this.calculateTotal.emit(e);\n    return false;\n  }\n\n  addnewService() {\n    const dOSFrom = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);\n    const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);\n    let locationOfService = '11';\n\n    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n      locationOfService = this.serviceLine.controls[0].value.locationOfService;\n    }\n\n    const empGroup = this.serviceLineform.group({\n      dateServiceFrom: new FormControl({\n        value: dOSFrom,\n        disabled: true\n      }, [Validators.required]),\n      dateServiceTo: new FormControl({\n        value: dOSTo,\n        disabled: true\n      }, [Validators.required]),\n      locationOfService: new FormControl(locationOfService, Validators.required),\n      emg: new FormControl(null),\n      desc: [{\n        value: null\n      }],\n      cpt: new FormControl(null, [Validators.required]),\n      m1: [{\n        value: '',\n        disabled: this.claimFormData.isViewClaim\n      }, modifierValidator],\n      m2: [{\n        value: '',\n        disabled: this.claimFormData.isViewClaim\n      }, modifierValidator],\n      m3: [{\n        value: \"\",\n        disabled: this.claimFormData.isViewClaim\n      }, modifierValidator],\n      m4: [{\n        value: '',\n        disabled: this.claimFormData.isViewClaim\n      }, modifierValidator],\n      diagnosispointer1: new FormControl('1', [Validators.required]),\n      diagnosispointer2: [{\n        value: '',\n        disabled: this.claimFormData.isViewClaim\n      }],\n      diagnosispointer3: [{\n        value: '',\n        disabled: this.claimFormData.isViewClaim\n      }],\n      diagnosispointer4: [{\n        value: '',\n        disabled: this.claimFormData.isViewClaim\n      }],\n      unitCharges: new FormControl(this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice, Validators.required),\n      dayUnitChanges: new FormControl({\n        value: '1',\n        disabled: this.claimFormData.isViewClaim\n      }, [Validators.required, Validators.max(99999999), Validators.min(1)]),\n      total: [{\n        value: this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice,\n        disabled: true\n      }],\n      ePSDT: [{\n        value: null,\n        disabled: this.claimFormData.isViewClaim\n      }],\n      jRenderingProviderId: [{\n        value: this.serviceLine.at(0)?.get('jRenderingProviderId')?.value,\n        disabled: true\n      }, [Validators.maxLength(10), Validators.minLength(10)]],\n      //new FormControl(this.serviceLine.at(0)?.get('jRenderingProviderId')?.value, [Validators.maxLength(10), Validators.minLength(10)]).disable(),\n      proceduceC: '',\n      proceduceCount: this.serviceLine.length + 1,\n      ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n      lineNote: '',\n      ndcQtyQual: '',\n      anesStart: '',\n      ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n      anesStop1: '',\n      anesStop2: '',\n      anesStop3: '',\n      ndcQual: '',\n      ndcCode: ''\n    });\n    this.serviceLine.push(empGroup); // this.dateValidators();\n    // if (this.serviceLine.invalid) {\n    //   submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n    //   return;\n    // }\n  }\n\n  removeService(i) {\n    this.serviceLine.removeAt(i);\n    this.calculateTotal.emit();\n    this.validateForm();\n  }\n\n  renderingProvider(e) {\n    this.changeRenderProviderNo(e.target.value);\n    this.subjectService.setServicelineProviderNPIChange(e.target.value);\n  }\n\n  changeRenderProviderNo(data) {\n    if (!!this.serviceLineInfo.controls) {\n      for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\n        this.serviceLine.at(index).get('jRenderingProviderId').patchValue(data);\n      }\n    }\n  }\n\n  dateValidators() {\n    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n      for (let item of this.serviceLine.controls) {\n        item.get('dateServiceFrom').valueChanges.pipe(distinctUntilChanged()).subscribe(dateServiceFrom => {\n          if (!!dateServiceFrom) {\n            if (!!item.get('dateServiceTo').value) {\n              if (new Date(dateServiceFrom).getTime() > new Date(item.get('dateServiceTo').value).getTime()) {\n                item.get('dateServiceFrom').setErrors({\n                  invalidDate: true\n                });\n              } else if (new Date(dateServiceFrom).getTime() < new Date(item.get('dateServiceTo').value).getTime()) {\n                item.get('dateServiceFrom').updateValueAndValidity();\n                item.get('dateServiceTo').updateValueAndValidity();\n              } else {\n                if (item.get('dateServiceFrom').hasError('invalidDate')) {\n                  delete item.get('dateServiceFrom').errors['invalidDate'];\n                  item.get('dateServiceFrom').updateValueAndValidity();\n                }\n\n                if (item.get('dateServiceFrom').hasError('required')) {\n                  delete item.get('dateServiceFrom').errors['invalidDate'];\n                  item.get('dateServiceFrom').updateValueAndValidity();\n                }\n              }\n            } else {\n              if (item.get('dateServiceFrom').hasError('invalidDate')) {\n                delete item.get('dateServiceFrom').errors['invalidDate'];\n                item.get('dateServiceFrom').updateValueAndValidity();\n              }\n\n              if (item.get('dateServiceFrom').hasError('required')) {\n                delete item.get('dateServiceFrom').errors['invalidDate'];\n                item.get('dateServiceFrom').updateValueAndValidity();\n              }\n            }\n          } else {\n            item.get('dateServiceFrom').setErrors({\n              required: true\n            });\n          }\n        });\n        item.get('dateServiceTo').valueChanges.pipe(distinctUntilChanged()).subscribe(dateServiceTo => {\n          if (!!dateServiceTo) {\n            if (!!item.get('dateServiceFrom').value) {\n              if (new Date(dateServiceTo).getTime() < new Date(item.get('dateServiceFrom').value).getTime()) {\n                item.get('dateServiceTo').setErrors({\n                  invalidDate: true\n                });\n              } else {\n                item.get('dateServiceTo').setErrors(null);\n                item.get('dateServiceFrom').updateValueAndValidity();\n              }\n            } else {\n              item.get('dateServiceTo').setErrors(null);\n            }\n          } else {\n            item.get('dateServiceTo').setErrors({\n              required: true\n            });\n          }\n        });\n      }\n    }\n  }\n\n  placeOfServiceChanges(e) {\n    if (!!this.serviceLineInfo.controls) {\n      for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\n        this.serviceLine.at(index).get('locationOfService').patchValue(!!e?.value ? e.value : null);\n      }\n    }\n  }\n\n  fetchCPT() {\n    let result = [];\n    let dosFrom;\n\n    if (this.claimFormData.isAddClaim) {\n      dosFrom = new Date(this.claimFormData?.profileMember?.dOSFrom).getTime();\n    } else if (!!this.claimFormData?.claimViewModel) {\n      dosFrom = new Date(this.claimFormData?.claimViewModel?.claimDosfrom).getTime();\n    }\n\n    for (const item of this.cptCodes) {\n      let add_date = new Date(item.add_date).getTime();\n      let term_date = new Date(item.term_date).getTime();\n\n      if ((add_date < dosFrom || add_date == dosFrom) && (term_date > dosFrom || term_date == dosFrom) && !!!result.find(e => e.mdmCode == item.mdmCode)) {\n        result.push(item);\n      }\n    }\n\n    this.cPTCodes = result;\n\n    if (this.claimFormData.isEditClaim) {\n      this.validateInvalidCPT();\n    }\n  }\n\n  fetchCPTData() {\n    let cpt = localStorage.getItem(LocalStorageKey.allCPT);\n\n    if (!!cpt) {\n      this.cptCodes = JSON.parse(JSLZString.decompress(cpt));\n      this.fetchCPT();\n    } else {\n      this.cptservice.fetchAllCpt().subscribe(res => {\n        if (!!res) {\n          this.cptCodes = res;\n          this.fetchCPT();\n        }\n      });\n    }\n  }\n\n  setYesOrNo(data) {\n    if (data === '' || data === 'N' || data === 'No') {\n      return 'No';\n    }\n\n    return 'Yes';\n  }\n\n  patchServiceLineData() {\n    let count = 1;\n    this.claimFormData?.claimViewModel?.claimsProfessional837?.serviceLineProfessional837s.forEach(ele => {\n      let date = ele.dtp03ServiceDate;\n      let fromDate = date.substring(0, 4) + \"-\" + date.substring(4, 6) + \"-\" + date.substring(6, 8);\n      let toDate = fromDate;\n      if (date.substring(9, 13) && date.substring(13, 15) && date.substring(15, 17)) toDate = date.substring(9, 13) + \"-\" + date.substring(13, 15) + \"-\" + date.substring(15, 17);\n      const empGroup = this.serviceLineform.group({\n        dateServiceFrom: [{\n          value: fromDate,\n          disabled: true\n        }],\n        dateServiceTo: [{\n          value: toDate,\n          disabled: true\n        }],\n        locationOfService: [{\n          value: ele.sv105PlaceOfServiceCode,\n          disabled: this.claimFormData.isViewClaim\n        }, Validators.required],\n        emg: [{\n          value: this.setYesOrNo(ele.sv109EmergencyIndicator),\n          disabled: this.claimFormData.isViewClaim\n        }],\n        desc: [{\n          value: ele.sv10107ServiceDescription\n        }],\n        cpt: new FormControl(ele.sv10102ProcedureCode, [Validators.required]),\n        m1: new FormControl(ele.sv10103ProcedureModifier1, modifierValidator),\n        m2: new FormControl(ele.sv10104ProcedureModifier2, modifierValidator),\n        m3: new FormControl(ele.sv10105ProcedureModifier3, modifierValidator),\n        m4: new FormControl(ele.sv10106ProcedureModifier4, modifierValidator),\n        diagnosispointer1: new FormControl(Number(ele.sv10701DiagnosisCodePointer1).toString(), [Validators.required]),\n        diagnosispointer2: new FormControl(ele.sv10702DiagnosisCodePointer2 ? Number(ele.sv10702DiagnosisCodePointer2).toString() : null),\n        diagnosispointer3: new FormControl(ele.sv10703DiagnosisCodePointer3 ? Number(ele.sv10703DiagnosisCodePointer3).toString() : null),\n        diagnosispointer4: new FormControl(ele.sv10704DiagnosisCodePointer4 ? Number(ele.sv10704DiagnosisCodePointer4).toString() : null),\n        unitCharges: [{\n          value: Number.parseFloat(ele.sv102LineItemChargeAmount).toFixed(2),\n          disabled: this.claimFormData.isViewClaim\n        }, Validators.required],\n        dayUnitChanges: [{\n          value: ele.sv104ServiceUnitCount,\n          disabled: this.claimFormData.isViewClaim\n        }, [Validators.required]],\n        total: [{\n          value: (Number.parseFloat(ele.sv104ServiceUnitCount) * Number.parseFloat(ele.sv102LineItemChargeAmount)).toFixed(2),\n          disabled: true\n        }],\n        ePSDT: [{\n          value: this.setYesOrNo(ele.sv111EpsdtIndicator),\n          disabled: this.claimFormData.isViewClaim\n        }],\n        jRenderingProviderId: [{\n          value: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm109RenderingProviderIdentifier,\n          disabled: true\n        }, Validators.maxLength(10), Validators.minLength(10)],\n        proceduceC: ele.sv10101ProductServiceIdQualifier,\n        proceduceCount: count,\n        ndcUnitPrice: new FormControl(ele.cpt03NationalDrugUnitPrice, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n        lineNote: ele.nte02LineNoteText,\n        ndcQtyQual: ele.ctp0501UnitMeasurementCode,\n        anesStart: ele.anesStart,\n        ndcQty: new FormControl(ele.ctp04NationalDrugUnitCount, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n        anesStop1: ele.anesStop,\n        anesStop2: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }],\n        anesStop3: [{\n          value: '',\n          disabled: this.claimFormData.isViewClaim\n        }],\n        ndcQual: ele.lin02NationalDrugCodeQlfr,\n        ndcCode: ele.lin03NationalDrugCode\n      });\n      this.serviceLine.push(empGroup);\n      count = count + 1;\n    });\n\n    if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim) {\n      for (let i = 0; i < this.serviceLine.controls.length; i++) {\n        this.ndcQualChange(i);\n        this.changeValuesToMarkValidation(i);\n      }\n\n      this.validateCPTCodeDOS();\n    }\n  }\n\n  dosFromChangedEvent(index, field) {\n    this.dosFromChanged.emit();\n    this.checkDateValidations(index, field);\n    this.twoYearValidationForDate(index);\n  }\n\n  checkDateFromValidation(index, field) {\n    let fromDate = this.serviceLine.at(index).get(field).value;\n\n    if (!!fromDate && !!new Date(fromDate)) {\n      if (new Date(fromDate).getTime() < this.minDate.getTime()) {\n        this.serviceLine.at(index).get(field).setErrors({\n          'invalidFromDate': true\n        });\n      } else {\n        if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {\n          delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\n        }\n      }\n    } else {\n      if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {\n        delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];\n        this.serviceLine.at(index).get(field).updateValueAndValidity();\n      }\n    }\n  }\n\n  twoYearValidationForDate(index) {\n    var _this = this;\n\n    if (!!this.serviceLine.at(index).get('dateServiceFrom').value) {\n      let fromDate = new Date(this.serviceLine.at(index).get('dateServiceFrom').value);\n      const days = moment(this.todayDate).diff(moment(fromDate), 'days');\n\n      if (days > 730) {\n        Swal.fire({\n          title: 'Alert',\n          text: \"The Date of Service is beyond 730 days (2 years). Do you want to continue?\",\n          icon: 'warning',\n          showCancelButton: false,\n          confirmButtonText: 'Yes',\n          showDenyButton: true,\n          denyButtonText: 'No',\n          reverseButtons: true\n        }).then( /*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (result) {\n            if (result.value == false) {\n              _this.serviceLine.at(index).get('dateServiceFrom').setValue(null);\n            }\n          });\n\n          return function (_x) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n      }\n    }\n  }\n\n  checkDateValidations(index, field) {\n    let fromDate = this.serviceLine.at(index).get('dateServiceFrom').value;\n    let toDate = this.serviceLine.at(index).get('dateServiceTo').value;\n\n    if (!!fromDate && !!toDate && !!new Date(fromDate) && !!new Date(toDate)) {\n      if (new Date(fromDate).getTime() > new Date(toDate).getTime()) {\n        this.serviceLine.at(index).get(field).setErrors({\n          'invalidDate': true\n        });\n      } else {\n        if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {\n          delete this.serviceLine.at(index).get(field).errors['invalidDate'];\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\n        }\n\n        if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {\n          delete this.serviceLine.at(index).get(field).errors['invalidDate'];\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\n        }\n\n        if (this.serviceLine.at(index).get(field).hasError('required')) {\n          delete this.serviceLine.at(index).get(field).errors['required'];\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\n        }\n      }\n    } else {\n      if (this.serviceLine.at(index).get(field).hasError('currentIllnessError')) {\n        if (this.serviceLine.at(index).get(field).hasError('invalidDate')) {\n          delete this.serviceLine.at(index).get(field).errors['invalidDate'];\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\n        }\n      } else {\n        this.serviceLine.at(index).get(field).setErrors(null);\n      }\n\n      if (!!!fromDate) {\n        this.serviceLine.at(index).get('dateServiceFrom').setErrors({\n          'required': true\n        });\n\n        if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {\n          delete this.serviceLine.at(index).get('dateServiceTo').errors['invalidDate'];\n          this.serviceLine.at(index).get('dateServiceTo').updateValueAndValidity();\n        }\n      }\n\n      if (!!!toDate) {\n        this.serviceLine.at(index).get('dateServiceTo').setErrors({\n          'required': true\n        });\n\n        if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {\n          delete this.serviceLine.at(index).get('dateServiceFrom').errors['invalidDate'];\n          this.serviceLine.at(index).get('dateServiceFrom').updateValueAndValidity();\n        }\n      }\n    }\n\n    this.checkDateFromValidation(index, field);\n    this.dateShow();\n  }\n\n  ndcCodeValidator(control) {\n    let isValid = !!control.value && control.value.trim().length == 11 || control.value == null || control.value == '';\n\n    if (!!control.value) {\n      isValid = isValid && String(control.value).match(/[0-9]/g).length == control.value.length;\n    }\n\n    return isValid ? null : {\n      'isInvalid': true\n    };\n  }\n\n  ndcQualChange(index) {\n    if (!!this.ndcValidation(index).controls['ndcQual'].value && this.ndcValidation(index).controls['ndcQual'].value.toLowerCase() == 'n4') {\n      this.ndcValidation(index).controls['ndcCode'].setValidators(this.ndcCodeValidator);\n\n      if (!this.isNdcCodeValid(this.ndcValidation(index).controls['ndcCode'].value)) {\n        this.ndcValidation(index).controls['ndcCode'].setErrors({\n          'isInvalid': true\n        });\n      }\n    } else {\n      this.ndcValidation(index).controls['ndcCode'].removeValidators(this.ndcCodeValidator);\n\n      if (this.serviceLine.at(index).get('ndcCode').hasError('isInvalid')) {\n        delete this.serviceLine.at(index).get('ndcCode').errors['isInvalid'];\n      }\n    }\n\n    this.serviceLine.at(index).get('ndcCode').updateValueAndValidity();\n  }\n\n  isNdcCodeValid(value) {\n    let isValid = !!value && value.trim().length == 11 || value == null || value == '';\n\n    if (!!value) {\n      isValid = isValid && String(value).match(/[0-9]/g).length == value.length;\n    }\n\n    return isValid;\n  }\n\n  servicelineEdit(e) {\n    if (e.target.checked) {\n      this.addServiceShow = true;\n\n      for (let item of this.serviceLine.controls) {\n        this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());\n        this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString()); // item.get('dateServiceFrom').enable({ onlySelf: true });\n        // item.get('dateServiceTo').enable({ onlySelf: true });\n      } //submitValidateAllFields.validateDisableControl(this.serviceLineInfo, [\"dateServiceFrom\"]);\n\n    } else {\n      this.addServiceShow = false;\n\n      for (let item of this.serviceLine.controls) {// item.get('dateServiceFrom').disable({ onlySelf: true });\n        // item.get('dateServiceTo').disable({ onlySelf: true });\n      }\n    }\n  }\n\n  dateShow() {\n    this.dateServiceFrom = [];\n    this.dateServiceTo = [];\n\n    for (let item of this.serviceLine.controls) {\n      this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());\n      this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString());\n    }\n\n    let min = this.dateServiceFrom[0];\n    let max = this.dateServiceFrom[0];\n    this.dateServiceFrom.forEach(function (v) {\n      max = new Date(v) > new Date(max) ? v : max;\n      min = new Date(v) < new Date(min) ? v : min;\n    });\n    this.oldServiceDateFrom = new Date(min).toString();\n    this.oldServiceDateTo = new Date(max).toString();\n  }\n\n  onSearchQualifer(term, item) {\n    term = term.toLocaleLowerCase();\n    return item['qualifier'].toLocaleLowerCase().indexOf(term) > -1 || item['description'].toLocaleLowerCase().indexOf(term) > -1;\n  }\n\n  validateCPTCode() {\n    for (let i = 0; i < this.serviceLine.length; i++) {\n      for (let k = 1; k <= 4; k++) {\n        if (this.serviceLine.at(i).get('diagnosispointer' + k).errors?.dublicate) {\n          this.serviceLine.at(i).get('diagnosispointer' + k).setErrors(null);\n        }\n      }\n    }\n\n    for (let i = 0; i < this.serviceLine.length; i++) {\n      for (let h = 1; h <= this.serviceLine.length - 1; h++) {\n        if (this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {\n          for (let j = 1; j <= 4; j++) {\n            for (let k = 1; k <= 4; k++) {\n              if (i != h && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {\n                this.serviceLine.at(i).get('diagnosispointer' + j).setErrors({\n                  dublicate: true\n                });\n                this.serviceLine.at(h).get('diagnosispointer' + k).setErrors({\n                  dublicate: true\n                });\n                this.serviceLine.at(i).get('diagnosispointer' + j).markAsTouched();\n                this.serviceLine.at(h).get('diagnosispointer' + k).markAsTouched();\n              }\n            }\n          }\n        }\n      }\n    }\n\n    for (let i = 0; i < this.serviceLine.length; i++) {\n      for (let j = 1; j <= 4; j++) {\n        for (let k = 1; k <= 4; k++) {\n          if (j != k && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(i).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(i).get('diagnosispointer' + k).value) {\n            if (!this.serviceLine.at(i).get('diagnosispointer' + k).errors) {\n              this.serviceLine.at(i).get('diagnosispointer' + k).setErrors({\n                dublicate: true\n              });\n              this.serviceLine.at(i).get('diagnosispointer' + i).markAsTouched();\n            }\n          }\n        }\n      }\n    }\n  }\n\n  validateInvalidCPT() {\n    let countMatched = 0;\n\n    for (let index = 0; index < this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length; index++) {\n      if (this.cptCodes.filter(t => t.cpt == this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode).length > 0) {\n        countMatched = countMatched + 1;\n      } else {\n        this.cptservice.fetchchCPTCodeSeach(this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode, this.claimFormData.claimViewModel.claimDosfrom, '').subscribe(res => {\n          countMatched = countMatched + 1;\n          res.forEach(element => {\n            this.cPTCodes.push(element);\n          });\n          this.patchValueMatched(countMatched);\n        });\n      }\n    }\n\n    this.patchValueMatched(countMatched);\n  }\n\n  patchValueMatched(countMatched) {\n    let totalCount = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length;\n\n    if (totalCount === countMatched) {\n      this.patchServiceLineData();\n    }\n  }\n\n  validateCPTCodeDOS() {\n    let cptData = [];\n\n    for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {\n      let cptDosData = {\n        cpt: '',\n        dos: ''\n      };\n      let date = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].dtp03ServiceDate;\n      let fromDate = date.substring(6, 8) + \"/\" + date.substring(4, 6) + \"/\" + date.substring(0, 4);\n      cptDosData.cpt = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].sv10102ProcedureCode;\n      cptDosData.dos = fromDate;\n      cptData.push(cptDosData);\n    }\n\n    this.cptservice.CPTValidorNot(cptData).subscribe(res => {\n      for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {\n        if (res.filter(t => t.cpt === this.serviceLine.at(i).get('cpt').value).length === 0) {\n          this.serviceLine.at(i).get('cpt').setErrors({\n            invalidCpt: true\n          });\n          res[i].cpt = this.serviceLine.at(i).get('cpt').value;\n          this.cPTCodes.push(res[i]);\n          this.invalidCptCode.push(this.serviceLine.at(i).get('cpt').value);\n        }\n      }\n\n      if (this.serviceLine.invalid) {\n        submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n        return;\n      }\n    });\n  }\n\n  cptViewHistory() {\n    let request;\n\n    if (this.claimFormData.isEditClaim) {\n      if (this.serviceLine.controls.length > 0) {\n        if (!!this.serviceLine.controls[0].value.dateServiceFrom?._d) {\n          request = {\n            subscribeID: this.claimFormData.claimViewModel.subscribeId,\n            dos: this.serviceLine.controls[0].value.dateServiceFrom._d\n          };\n        } else {\n          request = {\n            subscribeID: this.claimFormData.claimViewModel.subscribeId,\n            dos: this.serviceLine.controls[0].value.dateServiceFrom\n          };\n        }\n      }\n    } else if (this.claimFormData.isAddClaim) {\n      request = {\n        subscribeID: this.claimFormData.profileMember.subscriberID,\n        dos: this.claimFormData.profileMember.dOSFrom\n      };\n    }\n\n    this.cptservice.getCPTViewHistory(request).subscribe(resp => {\n      let cptList = [];\n\n      if (resp.statusCode == 200 && (resp.content || []).length > 0) {\n        cptList = resp.content;\n        cptList = cptList.map(cpt => ({ ...cpt,\n          isSelected: false\n        }));\n      }\n\n      const existingServiceLines = this.serviceLine.value;\n      existingServiceLines?.forEach(serviceLine => {\n        cptList.filter(cptItem => cptItem.cpt === serviceLine.cpt).forEach(cpt => {\n          cpt.isSelected = true;\n        });\n      });\n      this.viewHistoryCPTList = cptList;\n      let dialogRef = this.dialog.open(CptViewHistoryComponent, {\n        height: '650px',\n        width: '1100px',\n        autoFocus: false,\n        restoreFocus: false,\n        maxHeight: '90vh',\n        panelClass: 'custom-dialog-containers',\n        data: {\n          cptList: cptList\n        }\n      });\n      dialogRef.afterClosed().subscribe(data => {\n        if (!!data && (data.selectedCPTCodes || []).length > 0) {\n          this.mapServiceLinesSelecteCPCodes(data.selectedCPTCodes);\n        } // if selected cpts is zero then index 0 in existing service line cpt should be empty and remove remaining all.\n        else if (!!data && (data.selectedCPTCodes || []).length == 0) {\n          if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n            const servicelineMatchedFromViewHistoryCPTCodes = this.serviceLine.value.filter(serviceLineItem => this.viewHistoryCPTList.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt));\n\n            if (!!servicelineMatchedFromViewHistoryCPTCodes && servicelineMatchedFromViewHistoryCPTCodes.length > 0) {\n              servicelineMatchedFromViewHistoryCPTCodes.forEach(item => {\n                const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt);\n\n                if (index == 0) {\n                  let charges = this.getUnitChargesByPayerId();\n                  let cptCharge = this.allCPTCharges.filter(item => item.code == item.cpt)[0]?.charge;\n\n                  if (!!cptCharge) {\n                    charges = cptCharge;\n                  }\n\n                  this.serviceLine.at(index).patchValue({\n                    cpt: null,\n                    unitCharges: Number.parseFloat(charges).toFixed(2)\n                  });\n                } else {\n                  this.serviceLine.removeAt(index);\n                }\n              });\n            } // this.serviceLine.controls[0].patchValue({\n            //   cpt: null,\n            //   dayUnitChanges: '0.00'\n            // });\n            // this.isShowDelete = false;\n            // while (this.serviceLine.controls.length > 1) {\n            //   this.serviceLine.removeAt(this.serviceLine.controls.length - 1);  // Remove last control\n            // }\n\n          }\n        }\n      });\n    });\n  }\n\n  mapServiceLinesSelecteCPCodes(selectedCPTCodes) {\n    let getExistingFirstServiceLine;\n\n    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\n      getExistingFirstServiceLine = this.serviceLine.controls[0]; // copy this values to auto fill to new adding service\n      /// filter  Unused  servicelines based on selected CPT and remove it from servicelines controls.\n\n      if (selectedCPTCodes.length > 0 && this.serviceLine.controls.length > 0) {\n        const servicelineNoMatchedFromSelectedCPTCodes = this.serviceLine.value.filter(serviceLineItem => !selectedCPTCodes.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt)); // Remove unMatched values from serviceLines Controls.      \n\n        servicelineNoMatchedFromSelectedCPTCodes.forEach(item => {\n          if (!!item.cpt) {\n            const isViewHistoryItem = this.viewHistoryCPTList.find(cpt => cpt.cpt == item.cpt); // Find the index of the control where the 'id' matches\n\n            const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt); // Remove the control if found\n\n            if (index !== -1 && !!isViewHistoryItem) {\n              let charges = this.getUnitChargesByPayerId();\n              let cptCharge = this.allCPTCharges.filter(item => item.code == item.cptCode)[0]?.charge;\n\n              if (!!cptCharge) {\n                charges = cptCharge;\n              }\n\n              if (index == 0) {\n                this.serviceLine.at(index).patchValue({\n                  cpt: null,\n                  unitCharges: Number.parseFloat(charges).toFixed(2)\n                });\n              } else {\n                this.serviceLine.removeAt(index);\n              }\n            }\n          }\n        });\n      }\n\n      let existingServiceLines = this.serviceLine.value; // get selected cptCodes except from existingservice lines.\n\n      selectedCPTCodes = selectedCPTCodes.filter(selectedItem => !existingServiceLines.some(existingItem => selectedItem.cpt === existingItem.cpt)); // if existing service line is one with dates , selected cpt is one if both are different       \n\n      if (selectedCPTCodes.length == 1 && existingServiceLines.length == 1) {\n        // if existing cpt is null update existing row of cpt.\n        if (existingServiceLines.length == 1 && !existingServiceLines[0].cpt) {\n          this.serviceLine.controls[0].patchValue({\n            cpt: selectedCPTCodes[0].cpt,\n            desc: selectedCPTCodes[0].shortDescription\n          });\n          this.OnCPTCodeChange(selectedCPTCodes[0].cpt, 0);\n          selectedCPTCodes.shift(); // 0 index remove\n        }\n      }\n    } // for new selected cpts addding to service lines.\n\n\n    selectedCPTCodes.forEach(cptCodeDetails => {\n      this.patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine);\n    });\n  }\n\n  patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine) {\n    let locationOfService;\n    let topSelectedServiceLine;\n\n    if (!!getExistingFirstServiceLine) {\n      locationOfService = getExistingFirstServiceLine.value.locationOfService;\n      topSelectedServiceLine = getExistingFirstServiceLine.getRawValue();\n    }\n\n    if (!!cptCodeDetails.cpt) {\n      // existing service line with empty cptCode patch values\n      const index = this.serviceLine.controls.findIndex(control => control.value.cpt === null);\n\n      if (index !== -1) {\n        this.serviceLine.at(index).patchValue({\n          cpt: cptCodeDetails.cpt,\n          desc: cptCodeDetails.shortDescription\n        });\n        this.OnCPTCodeChange(cptCodeDetails.cpt, index);\n      } else {\n        let unitCharges = this.getUnitChargesByPayerId();\n        let total = unitCharges;\n        let cptCharge = this.allCPTCharges.filter(item => item.code == cptCodeDetails.cpt)[0]?.charge;\n\n        if (!!cptCharge) {\n          unitCharges = cptCharge;\n          total = (cptCharge * 1).toFixed(2);\n        }\n\n        const dOSFrom = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);\n        const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);\n        const empGroup = this.serviceLineform.group({\n          dateServiceFrom: new FormControl({\n            value: dOSFrom,\n            disabled: true\n          }, [Validators.required]),\n          dateServiceTo: new FormControl({\n            value: dOSTo,\n            disabled: true\n          }, [Validators.required]),\n          locationOfService: new FormControl(!!locationOfService ? locationOfService : null, Validators.required),\n          emg: new FormControl(null),\n          desc: new FormControl(cptCodeDetails.shortDescription),\n          cpt: new FormControl(cptCodeDetails.cpt, [Validators.required]),\n          m1: [{\n            value: '',\n            disabled: false\n          }, modifierValidator],\n          m2: [{\n            value: '',\n            disabled: false\n          }, modifierValidator],\n          m3: [{\n            value: '',\n            disabled: false\n          }, modifierValidator],\n          m4: [{\n            value: '',\n            disabled: false\n          }, modifierValidator],\n          diagnosispointer1: new FormControl('1', [Validators.required]),\n          diagnosispointer2: [{\n            value: '',\n            disabled: false\n          }],\n          diagnosispointer3: [{\n            value: '',\n            disabled: false\n          }],\n          diagnosispointer4: [{\n            value: '',\n            disabled: false\n          }],\n          charges: new FormControl(Number.parseFloat(unitCharges).toFixed(2), Validators.required),\n          dayUnitChanges: new FormControl('1', Validators.required),\n          unitCharges: new FormControl({\n            value: Number.parseFloat(unitCharges).toFixed(2),\n            disabled: false\n          }, [Validators.required]),\n          total: [{\n            value: total,\n            disabled: true\n          }],\n          ePSDT: [{\n            value: null,\n            disabled: false\n          }],\n          jRenderingProviderId: [{\n            value: topSelectedServiceLine.jRenderingProviderId,\n            disabled: true\n          }, [Validators.maxLength(10), Validators.minLength(10)]],\n          proceduceC: '',\n          proceduceCount: this.serviceLine.length + 1,\n          ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n          lineNote: '',\n          ndcQtyQual: '',\n          anesStart: '',\n          ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\n          anesStop1: '',\n          anesStop2: '',\n          anesStop3: '',\n          ndcQual: '',\n          ndcCode: ''\n        });\n        this.serviceLine.push(empGroup);\n        this.calculateTotal.emit();\n        this.dateValidators();\n\n        if (this.serviceLine.invalid) {\n          submitValidateAllFields.validateAllFields(this.serviceLineInfo);\n          return;\n        }\n      }\n    }\n  }\n\n  getUnitChargesByPayerId() {\n    let charges = PriceCost.zeroPrice;\n\n    if (!!this.claimFormData.claimViewModel) {\n      charges = this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n    } else if (this.claimFormData.isAddClaim) {\n      charges = this.claimFormData.payerItem.payerId === \"41212\" || this.claimFormData.payerItem?.payerId === \"20133\" || this.claimFormData.payerItem?.payerId === \"59354\" ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\n    }\n\n    return charges;\n  }\n\n}\n\nServiceLineClaimComponent.ɵfac = function ServiceLineClaimComponent_Factory(t) {\n  return new (t || ServiceLineClaimComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AllPlaceOfServicesService), i0.ɵɵdirectiveInject(i3.SubjectService), i0.ɵɵdirectiveInject(i4.GetAllCPTCodeService), i0.ɵɵdirectiveInject(i5.MatDialog), i0.ɵɵdirectiveInject(i6.CacheService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.NgxSpinnerService));\n};\n\nServiceLineClaimComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ServiceLineClaimComponent,\n  selectors: [[\"app-service-line-claim\"]],\n  inputs: {\n    serviceLineInfos: \"serviceLineInfos\",\n    memberProfile: \"memberProfile\",\n    claimFormData: \"claimFormData\",\n    allPlaceOfServices: \"allPlaceOfServices\"\n  },\n  outputs: {\n    diagnosisPointerBlur: \"diagnosisPointerBlur\",\n    dosFromChanged: \"dosFromChanged\",\n    calculateTotal: \"calculateTotal\"\n  },\n  decls: 58,\n  vars: 8,\n  consts: [[1, \"service-line-dashboard\", 3, \"formGroup\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"form-title\"], [1, \"col-md-2\", \"radio-flex\"], [1, \"col-md-4\", \"radio-flex\"], [1, \"form-check\", \"form-check-inline\", \"radio-flex\"], [\"type\", \"radio\", \"formControlName\", \"showNDC\", \"value\", \"No\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"OutsideLabYes\", 1, \"create-claim-radio-labels\"], [\"type\", \"radio\", \"formControlName\", \"showNDC\", \"value\", \"Yes\", 1, \"form-check-input\", 3, \"change\"], [\"for\", \"OutsideLabNo\", 1, \"create-claim-radio-labels\"], [1, \"row\", \"mt-2\"], [1, \"col-md\"], [1, \"24\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\", 3, \"ngStyle\"], [\"colspan\", \"2\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\", \"rowspan\", \"2\", 2, \"font-size\", \"12px\"], [\"scope\", \"col\", \"rowspan\", \"2\", 2, \"font-size\", \"12px\", \"max-width\", \"6rem !important\"], [\"scope\", \"col\", \"rowspan\", \"2\", 2, \"font-size\", \"12px\", \"max-width\", \"9rem !important\"], [\"style\", \"font-size:12px;\", \"scope\", \"col\", \"rowspan\", \"2\", 4, \"ngIf\"], [\"scope\", \"col\", 2, \"font-size\", \"12px\"], [\"href\", \"javascript:void(0);\", \"id\", \"viewCptCodesHistory\", \"class\", \"view-history\", \"title\", \"View CPT History\", 3, \"click\", 4, \"ngIf\"], [\"formArrayName\", \"serviceLines\", 2, \"border\", \"inherit !important\"], [\"style\", \"display: contents;\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn-primary primary-btn btn-height\", 3, \"click\", 4, \"ngIf\"], [\"href\", \"javascript:void(0);\", \"id\", \"viewCptCodesHistory\", \"title\", \"View CPT History\", 1, \"view-history\", 3, \"click\"], [1, \"fa\", \"fa-history\"], [2, \"display\", \"contents\", 3, \"formGroupName\"], [4, \"ngIf\"], [1, \"col\"], [\"matInput\", \"\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"dateServiceFrom\", \"placeholder\", \"MM/DD/YYYY\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\", 4, \"ngIf\"], [\"datepickerFrom\", \"\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"class\", \"form-control\", 4, \"ngIf\"], [\"matInput\", \"\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"dateServiceTo\", \"placeholder\", \"MM/DD/YYYY\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\", 4, \"ngIf\"], [\"datepickerTo\", \"\"], [2, \"vertical-align\", \"top\"], [\"class\", \"form-control form-control-sm wdt-100\", \"placeholder\", \"Location Of Service\", \"appendTo\", \"body\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"locationOfService\", 3, \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"EMG\", \"appendTo\", \"body\", \"formControlName\", \"emg\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm wdt-100\", \"placeholder\", \"CPT  Type 3 letter\", \"appendTo\", \"body\", \"bindLabel\", \"shortDescription\", \"bindValue\", \"cpt\", \"formControlName\", \"cpt\", 3, \"virtualScroll\", \"ngClass\", \"ngModelChange\", 4, \"ngIf\"], [2, \"max-width\", \"20rem !important\"], [1, \"table-flex\", \"m-2\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m1\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M1\", \"maxlength\", \"2\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m2\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M2\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m3\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M3\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"m4\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M4\", 3, \"disabled\", \"id\", \"ngClass\", 4, \"ngIf\"], [1, \"table-flex\", \"mt-2\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer1\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D1\", 3, \"disabled\", \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer2\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D2\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer3\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D3\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"diagnosispointer4\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D4\", 3, \"ngClass\", 4, \"ngIf\"], [2, \"max-width\", \"6rem !important\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersTwoDecimalOnly\", \"\", \"formControlName\", \"unitCharges\", 3, \"ngClass\", \"blur\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"numbersOnly\", \"\", \"formControlName\", \"dayUnitChanges\", 3, \"ngClass\", \"blur\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"placeholder\", \"\", \"formControlName\", \"total\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"EPSDT\", \"appendTo\", \"body\", \"formControlName\", \"ePSDT\", 4, \"ngIf\"], [1, \"d-flex\", \"mt-2\", 2, \"font-size\", \"12px\", 3, \"ngClass\"], [2, \"max-width\", \"9rem !important\"], [1, \"mt-2\"], [\"class\", \"form-control form-control-sm\", \"type\", \"text\", \"placeholder\", \"\", \"maxlength\", \"10\", \"required\", \"\", \"formControlName\", \"jRenderingProviderId\", 3, \"ngClass\", \"input\", 4, \"ngIf\"], [2, \"font-size\", \"12px\", \"vertical-align\", \"middle\"], [\"class\", \"form-control form-control-sm\", \"placeholder\", \"Procedure Code Type\", \"formControlName\", \"proceduceC\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"proceduceCount\", 4, \"ngIf\"], [\"rowspan\", \"2\", \"colspan\", \"11\", 2, \"font-size\", \"12px\"], [1, \"row\", 2, \"margin\", \"0\", \"margin-top\", \"0.5rem !important\"], [1, \"col-1\", 3, \"ngClass\"], [1, \"col-1\", \"no-padding\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"lineNote\", \"placeholder\", \"LINE NOTE\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStart\", \"placeholder\", \"Anes Start\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStop1\", \"placeholder\", \"ANES STOP\", 4, \"ngIf\"], [1, \"col-3\", \"no-padding\"], [\"class\", \"form-control form-control-sm\", \"appendTo\", \"body\", \"placeholder\", \"NDC Qualifer\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQual\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\", 4, \"ngIf\"], [1, \"col-1\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"ndcCode\", \"placeholder\", \"NDC Code\", 3, \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"ndcQty\", \"numbersThreeDecimalOnly\", \"\", \"placeholder\", \"NDC Qty\", 3, \"ngClass\", \"change\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"appendTo\", \"body\", \"placeholder\", \"NDC Qty Qual\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQtyQual\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\", \"clear\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStop2\", 4, \"ngIf\"], [\"class\", \"form-control form-control-sm\", \"formControlName\", \"anesStop3\", 4, \"ngIf\"], [\"placeholder\", \"Procedure Code Type\", \"formControlName\", \"proceduceC\", 1, \"form-control\", \"form-control-sm\"], [1, \"form-control\"], [\"formControlName\", \"proceduceCount\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"lineNote\", \"placeholder\", \"LINE NOTE\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"anesStart\", \"placeholder\", \"Anes Start\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"anesStop1\", \"placeholder\", \"ANES STOP\", 1, \"form-control\", \"form-control-sm\"], [\"appendTo\", \"body\", \"placeholder\", \"NDC Qualifer\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQual\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\"], [\"assignto\", \"\"], [\"ng-option-tmp\", \"\", \"ng-label-tmp\", \"\"], [1, \"invalid-feedback\"], [\"formControlName\", \"ndcCode\", \"placeholder\", \"NDC Code\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"formControlName\", \"ndcQty\", \"numbersThreeDecimalOnly\", \"\", \"placeholder\", \"NDC Qty\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"appendTo\", \"body\", \"placeholder\", \"NDC Qty Qual\", \"bindLabel\", \"description\", \"bindValue\", \"qualifier\", \"formControlName\", \"ndcQtyQual\", 1, \"form-control\", \"form-control-sm\", 3, \"items\", \"searchFn\", \"ngClass\", \"change\", \"clear\"], [\"formControlName\", \"anesStop2\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"anesStop3\", 1, \"form-control\", \"form-control-sm\"], [2, \"font-size\", \"12px\", 3, \"ngClass\"], [2, \"font-size\", \"12px\"], [\"class\", \"form-control form-control-sm\", \"numbersThreeDecimalOnly\", \"\", \"formControlName\", \"ndcUnitPrice\", \"placeholder\", \"NDC Unit Price \", 3, \"ngClass\", 4, \"ngIf\"], [\"numbersThreeDecimalOnly\", \"\", \"formControlName\", \"ndcUnitPrice\", \"placeholder\", \"NDC Unit Price \", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"matInput\", \"\", \"formControlName\", \"dateServiceFrom\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\"], [\"matInput\", \"\", \"formControlName\", \"dateServiceTo\", \"placeholder\", \"MM/DD/YYYY\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"max\", \"matDatepicker\", \"ngClass\", \"click\", \"blur\"], [\"placeholder\", \"Location Of Service\", \"appendTo\", \"body\", \"bindLabel\", \"text\", \"bindValue\", \"value\", \"formControlName\", \"locationOfService\", 1, \"form-control\", \"form-control-sm\", \"wdt-100\", 3, \"ngClass\", \"change\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [\"placeholder\", \"EMG\", \"appendTo\", \"body\", \"formControlName\", \"emg\", 1, \"form-control\", \"form-control-sm\"], [\"placeholder\", \"CPT  Type 3 letter\", \"appendTo\", \"body\", \"bindLabel\", \"shortDescription\", \"bindValue\", \"cpt\", \"formControlName\", \"cpt\", 1, \"form-control\", \"form-control-sm\", \"wdt-100\", 3, \"virtualScroll\", \"ngClass\", \"ngModelChange\"], [\"formControlName\", \"m1\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"placeholder\", \"M1\", \"maxlength\", \"2\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"m2\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M2\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"m3\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M3\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"m4\", \"numbersOnly\", \"\", \"type\", \"text\", \"name\", \"\", \"maxlength\", \"2\", \"placeholder\", \"M4\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"id\", \"ngClass\"], [\"formControlName\", \"diagnosispointer1\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D1\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"ngClass\"], [\"formControlName\", \"diagnosispointer2\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D2\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"formControlName\", \"diagnosispointer3\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D3\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"formControlName\", \"diagnosispointer4\", \"numbersOnly\", \"\", \"type\", \"text\", \"placeholder\", \"D4\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"type\", \"text\", \"numbersTwoDecimalOnly\", \"\", \"formControlName\", \"unitCharges\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"blur\"], [\"type\", \"text\", \"numbersOnly\", \"\", \"formControlName\", \"dayUnitChanges\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"blur\"], [\"type\", \"text\", \"placeholder\", \"\", \"formControlName\", \"total\", 1, \"form-control\", \"form-control-sm\"], [\"placeholder\", \"EPSDT\", \"appendTo\", \"body\", \"formControlName\", \"ePSDT\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"placeholder\", \"\", \"maxlength\", \"10\", \"required\", \"\", \"formControlName\", \"jRenderingProviderId\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"input\"], [1, \"d-flex\", \"mt-2\"], [\"type\", \"button\", \"matTooltip\", \"Remove\", \"matTooltipPosition\", \"above\", \"class\", \"remove-button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"matTooltip\", \"Remove\", \"matTooltipPosition\", \"above\", 1, \"remove-button\", 3, \"click\"], [\"type\", \"button\", 1, \"btn-primary\", \"primary-btn\", \"btn-height\", 3, \"click\"], [1, \"material-icons\", \"icon-margin\", \"icon-height\", \"icon-align\"]],\n  template: function ServiceLineClaimComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"p\", 3);\n      i0.ɵɵtext(4, \"24. Service Line\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelement(5, \"div\", 4);\n      i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"input\", 7);\n      i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_Template_input_change_8_listener() {\n        return ctx.hideNDC();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"label\", 8);\n      i0.ɵɵtext(10, \"Hide NDC\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(11, \"div\", 6)(12, \"input\", 9);\n      i0.ɵɵlistener(\"change\", function ServiceLineClaimComponent_Template_input_change_12_listener() {\n        return ctx.showNDC();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"label\", 10);\n      i0.ɵɵtext(14, \"Show NDC\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"section\", 13)(18, \"div\", 14)(19, \"table\", 15)(20, \"thead\")(21, \"tr\")(22, \"th\", 16);\n      i0.ɵɵtext(23, \"A. Date(S) Of Service\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"th\", 17);\n      i0.ɵɵtext(25, \"B. Place Of Service\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"th\", 17);\n      i0.ɵɵtext(27, \"C. EMG\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"th\", 16);\n      i0.ɵɵtext(29, \"D. Procedures,Services Or Supplies\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"th\", 17);\n      i0.ɵɵtext(31, \"E. Diagnosis Pointer\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"th\", 18);\n      i0.ɵɵtext(33, \"F. Unit Charges ($)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"th\", 18);\n      i0.ɵɵtext(35, \"G. Days And Unit\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(36, \"th\", 18);\n      i0.ɵɵtext(37, \"Charges ($)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"th\", 17);\n      i0.ɵɵtext(39, \"H. EPSDT\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"th\", 17);\n      i0.ɵɵtext(41, \"I. ID Qual\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"th\", 19);\n      i0.ɵɵtext(43, \"J. Rendering Provider ID\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(44, ServiceLineClaimComponent_th_44_Template, 1, 0, \"th\", 20);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"tr\")(46, \"th\", 21);\n      i0.ɵɵtext(47, \"From\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(48, \"th\", 21);\n      i0.ɵɵtext(49, \"To\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(50, \"th\", 21);\n      i0.ɵɵtext(51, \"CPT/HCPCS \");\n      i0.ɵɵtemplate(52, ServiceLineClaimComponent_a_52_Template, 2, 0, \"a\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(53, \"th\", 21);\n      i0.ɵɵtext(54, \"Modifier\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(55, \"tbody\", 23);\n      i0.ɵɵtemplate(56, ServiceLineClaimComponent_span_56_Template, 121, 75, \"span\", 24);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(57, ServiceLineClaimComponent_button_57_Template, 4, 0, \"button\", 25);\n      i0.ɵɵelementEnd()()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"formGroup\", ctx.serviceLineInfo);\n      i0.ɵɵadvance(19);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c2, !ctx.claimFormData.isViewClaim ? \"max-content\" : \"\"));\n      i0.ɵɵadvance(25);\n      i0.ɵɵproperty(\"ngIf\", ctx.serviceLine.length > 1 && ctx.claimFormData.isEditClaim);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isEditClaim || ctx.claimFormData.isAddClaim);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngForOf\", ctx.serviceLine.controls);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.claimFormData.isEditClaim || ctx.claimFormData.isAddClaim);\n    }\n  },\n  dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i8.NgStyle, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i9.NgSelectComponent, i9.NgOptionComponent, i9.NgOptionTemplateDirective, i9.NgLabelTemplateDirective, i10.MatTooltip, i11.OnlyNumberDirective, i12.OnlyWithDeciamlaThreeNumberDirective, i13.OnlyWithDeciamlaTwoNumberDirective, i14.MatDatepicker, i14.MatDatepickerInput, i8.UpperCasePipe, i8.DatePipe],\n  styles: [\".create-claims-labels[_ngcontent-%COMP%] {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 16px;\\n  color: #3B475A;\\n}\\n\\n.form-title[_ngcontent-%COMP%] {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: #3B475A;\\n}\\n\\n.create-claim-form[_ngcontent-%COMP%] {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 600;\\n  font-size: 24px;\\n  line-height: 31px;\\n}\\n\\n.form-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.form-border[_ngcontent-%COMP%] {\\n  border: 1px solid #DCE3EF;\\n  padding: 12px;\\n}\\n\\n.claim-label[_ngcontent-%COMP%] {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 16px;\\n}\\n\\n.claim-title[_ngcontent-%COMP%] {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 16px;\\n  line-height: 42px;\\n  color: #617798;\\n}\\n\\n.search-icon-alignment[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.search-icons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0%;\\n  top: 20%;\\n  cursor: pointer;\\n  z-index: 1000;\\n}\\n\\n.create-claim-title[_ngcontent-%COMP%] {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 600;\\n  font-size: 20px;\\n}\\n\\n.create-claim[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\ninput[_ngcontent-%COMP%], input[_ngcontent-%COMP%]::placeholder {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 12px;\\n}\\n\\n.create-claim-radio-labels[_ngcontent-%COMP%] {\\n  font-family: \\\"IBM Plex Sans\\\";\\n  font-style: normal;\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #617798;\\n}\\n\\n.table-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n\\n.radio-flex[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.form-select-sm[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n@media (min-width: 1300px) {\\n  .create-claims-labels[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .form-title[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .create-claim-radio-labels[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .claim-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n\\n@media (min-width: 1700px) {\\n  .create-claims-labels[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .form-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .create-claim-radio-labels[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .claim-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n@media (min-width: 1900px) {\\n  .create-claims-labels[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .form-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .create-claim-radio-labels[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .claim-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n.fieldError[_ngcontent-%COMP%] {\\n  border: 1px solid #dc3545;\\n  border-radius: 4px 6px;\\n}\\n\\n.sbt-btn[_ngcontent-%COMP%] {\\n  margin-right: -102px;\\n  float: right;\\n}\\n\\ntd[_ngcontent-%COMP%] {\\n  padding-right: 0.15rem;\\n  padding-left: 0.15rem;\\n  max-width: 12rem !important;\\n}\\n\\nth[_ngcontent-%COMP%] {\\n  max-width: 12rem !important;\\n}\\n\\n.no-padding[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n.service-line-dashboard[_ngcontent-%COMP%]     .ng-select-container {\\n  height: 100% !important;\\n}\\n\\n.service-line-dashboard[_ngcontent-%COMP%]     .mat-card {\\n  background: #fff;\\n  color: rgba(0, 0, 0, 0.87);\\n  padding: 11px !important;\\n  border-radius: 4px !important;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  height: 1.36rem !important;\\n}\\n\\ntr[_ngcontent-%COMP%] {\\n  border-color: #dee2e6 !important;\\n}\\n\\ntable[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #dee2e6 !important;\\n}\\n\\n  .ng-option {\\n  white-space: break-spaces !important;\\n}\\n\\n.remove-button[_ngcontent-%COMP%] {\\n  background-color: transparent !important;\\n  border: none !important;\\n}\\n\\n.service-line-view-label[_ngcontent-%COMP%] {\\n  color: #617798;\\n  font-weight: 600;\\n}\\n\\nspan.form-control[_ngcontent-%COMP%] {\\n  text-align: center !important;\\n}\\n\\n.view-history[_ngcontent-%COMP%] {\\n  padding: 2px 3px;\\n  background-color: #0d6efd;\\n  color: white;\\n}\\n\\n.view-history[_ngcontent-%COMP%]:hover {\\n  background-color: midnightblue !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,iBAAT,EAAuCC,YAAvC,QAAkF,eAAlF;AACA,SAAiCC,WAAjC,EAAyDC,UAAzD,QAA2E,gBAA3E;AAKA,SAASC,uBAAT,QAAwC,gCAAxC;AASA,SAASC,oBAAT,EAA+BC,KAA/B,QAA4C,MAA5C;AACA,OAAO,KAAKC,MAAZ,MAAwB,QAAxB;AACA,OAAOC,IAAP,MAAiB,aAAjB;AACA,OAAO,KAAKC,UAAZ,MAA4B,WAA5B;AAGA,SAASC,uBAAT,QAAwC,mEAAxC;AACA,SAASC,2BAAT,EAAqCC,iBAArC,QAA8D,gCAA9D;AAEA,SAASC,eAAT,EAA0BC,SAA1B,QAA2C,mCAA3C;;;;;;;;;;;;;;;;;;;IC0BgCC;;;;;;;;IAQIA;IAEyBA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAyB,CAAzB;IAAoDA;IACxCA;;;;;;IAUgBA;;;;;;IAGjDA;IACsCA;IAEmBA;;;;;;IAFnBA;IAAAA;;;;;;IAIWA;;;;;;IAGjDA;IACsCA;IAEuBA;;;;;;IAFvBA;IAAAA;;;;;;IAW9BA;;;;;;IAGAA;IACsCA;IAEiBA;;;;;;IAFjBA;IAAAA;;;;;;IAUtCA;;;;;;IAGAA;IACsCA;IAEkBA;;;;;;IAFlBA;IAAAA;;;;;;IAWtCA;;;;;;IAGAA;IACsCA;IAEkBA;;;;;;IAFlBA;IAAAA;;;;;;IAoB9BA;;;;;IAAAA;;;;;;;;;;;;;;IATRA;IAIIA;MAAAA;MAAA;MAAA;MAAUC;MAA+B,OAACD,4CAAD;IAAiB,CAA1D;IAIAA;IAGJA;;;;;;IATIA,6CAAsB,UAAtB,EAAsBE,wBAAtB,EAAsB,SAAtB,EAAsBF,6IAAtB;;;;;;IAcAA;IAEIA;IACJA;;;;;;IALJA;IAEIA;IAIJA;;;;;;IAHSA;IAAAA;;;;;;IAITA;IACsCA;IAEgBA;;;;;;IAFhBA;IAAAA;;;;;;;;IAStCA;IAIIA;MAAAA;MAAA;MAAA;MAAA,OAAUA,2DAAV;IAAyC,CAAzC;IAJJA;;;;;;IAEIA;;;;;;IAKAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IATJA;IAEIA;IAIAA;IAIJA;;;;;;IAPSA;IAAAA;IAIAA;IAAAA;;;;;;IAITA;IACsCA;IAEgBA;;;;;;IAFhBA;IAAAA;;;;;;;;IAYtCA;IAE4BA;MAAAA;MAAA;MAAA;MAAA,OAAUA,2DAAV;IAAyC,CAAzC;IAF5BA;;;;;;IAGIA;;;;;;IAIAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IAEJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IAdJA;IAEIA;IAIAA;IAKAA;IAIJA;;;;;;;;;;IAZSA;IAAAA;IAIAA;IAAAA;IAKAA;IAAAA;;;;;;IAITA;IACsCA;IAEeA;;;;;;IAFfA;IAAAA;;;;;;IAiB9BA;;;;;IAAAA;;;;;;;;IARRA;IAGIA;MAAAA;MAAA;MAAA;MAAA,OAAUA,iDAAV;IAA+B,CAA/B,EAAgC,OAAhC,EAAgC;MAAAA;MAAA;MAAA;MAAA,OACvBA,oCADuB;IACf,CADjB;IAIAA;IAGJA;;;;;;IARIA,iDAAyB,UAAzB,EAAyBG,yBAAzB,EAAyB,SAAzB,EAAyBH,qJAAzB;;;;;;IAWAA;IAEIA;IACJA;;;;;;IALJA;IAEIA;IAIJA;;;;;;IAHSA;IAAAA;;;;;;IAITA;IACsCA;IAEmBA;;;;;;IAFnBA;IAAAA;;;;;;IAMtCA;;;;;;IAEAA;IACsCA;IAEkBA;;;;;;IAFlBA;IAAAA;;;;;;IAKtCA;;;;;;IAEAA;IACsCA;IAEkBA;;;;;;IAFlBA;IAAAA;;;;;;;;;;;;IAnMtDA,2BAAoC,CAApC,EAAoC,IAApC,EAAoC,EAApC;IACyDA;IAGjDA;IAIJA;IACAA;IAAqDA;IAGjDA;IAIJA;IACAA,+BAAqD,CAArD,EAAqD,KAArD,EAAqD,EAArD,EAAqD,CAArD,EAAqD,KAArD,EAAqD,EAArD;IAIYA;IACJA;IACAA;IACIA;IAGAA;IAIJA;IAEAA;IAEIA;IACJA;IACAA;IACIA;IAGAA;IAIJA;IAEAA;IAEIA;IAEJA;IACAA;IACIA;IAGAA;IAIJA;IAEAA;IAEIA;IACJA;IACAA;IAEIA;IAcAA;IAOAA;IAIJA;IACAA;IAEIA;IACJA;IACAA;IACIA;IAKAA;IAWAA;IAIJA;IAGJA,iCAAmE,EAAnE,EAAmE,KAAnE,EAAmE,EAAnE;IAGQA;IACJA;IACAA;IACIA;IAKAA;IAgBAA;IAIJA;IACAA;IAEIA;IACJA;IACAA;IACIA;IAWAA;IAOAA;IAIJA;IAEAA;IACIA;IAEAA;IAIJA;IACAA;IACIA;IAEAA;IAIJA;IAEAA,4BAEM,EAFN,EAEM,KAFN,EAEM,EAFN,EAEM,EAFN,EAEM,KAFN,EAEM,EAFN,EAEM,EAFN,EAEM,KAFN,EAEM,EAFN;IAaJA;;;;;;IAnNKA;IAAAA;IAGAA;IAAAA;IAKAA;IAAAA;IAGAA;IAAAA;IAOGA;IAAAA;IAIQA;IAAAA;IAIHA;IAAAA;IAMLA;IAAAA;IAIQA;IAAAA;IAIHA;IAAAA;IAMLA;IAAAA;IAKQA;IAAAA;IAIHA;IAAAA;IAMLA;IAAAA;IAKYA;IAAAA;IAePA;IAAAA;IAOAA;IAAAA;IAKLA;IAAAA;IAIQA;IAAAA;IAMHA;IAAAA;IAWAA;IAAAA;IAQLA;IAAAA;IAIQA;IAAAA;IAMHA;IAAAA;IAgBAA;IAAAA;IAKLA;IAAAA;IAIYA;IAAAA;IAYPA;IAAAA;IAOAA;IAAAA;IAMGA;IAAAA;IAGHA;IAAAA;IAKGA;IAAAA;IAGHA;IAAAA;;;;;;IAyBYA;;;;;;IAGrBA;;;;;;IACJA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEqBA;;;;;;IAFrBA;IAAAA;;;;;;IAjB9CA,2BAAoC,CAApC,EAAoC,IAApC,EAAoC,EAApC;IAE4EA;IAC9DA;IACVA;IAA6BA;IAIzBA;IAIAA;IAIAA;IAIJA;;;;;;;;IAlBIA;IAAAA;IAEiCA;IAAAA;IAK5BA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;;;;;;;;IAUGA;IAGsCA;MAAAA;MAAAA;;MAAA;;MAAA,OAASA,2BAAT;IAA8B,CAA9B,EAA+B,MAA/B,EAA+B;MAAAA;MAAA;MAAA;MAAA,OAEzDA,kDAAuB,iBAAvB,EAFyD;IAEhB,CAFf;IAHtCA;;;;;;;;;;IACyCA,sCAAe,KAAf,EAAeI,iBAAf,EAAe,eAAf,EAAeC,IAAf,EAAe,SAAf,EAAeL,iRAAf;;;;;;IAgBzCA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IACIA;;IAGKA;;;;;IAHLA;IAAAA;;;;;;;;IAUJA;IAGoCA;MAAAA;MAAAA;;MAAA;;MAAA,OAASA,2BAAT;IAA4B,CAA5B,EAA6B,MAA7B,EAA6B;MAAAA;MAAA;MAAA;MAAA,OAErDA,mDAAwB,eAAxB,EAFqD;IAEb,CAFhB;IAHpCA;;;;;;;;;;IACyCA,sCAAe,KAAf,EAAeM,iBAAf,EAAe,eAAf,EAAeC,IAAf,EAAe,SAAf,EAAeP,yQAAf;;;;;;IAgBzCA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IACIA;;IAGKA;;;;;IAHLA;IAAAA;;;;;;IAgBAA;IACIA;;IACJA;;;;;IAFmDA;IAC/CA;IAAAA;;;;;;;;IAPRA;IACIA;MAAAA;MAAA;MAAA,OAAUA,sDAAV;IAAuC,CAAvC;IAKAA;IAGJA;;;;;;;IAJIA;IAC4BA;IAAAA;;;;;;IAIhCA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEUA;;;;;IAFVA;IAAAA;;;;;;IAWlCA;IACIA;;IACJA;;;;;IAFoCA;IAChCA;IAAAA;;;;;;IAJMA;IAGVA;IAGJA;;;;;IAHgCA;IAAAA;;;;;;IAIhCA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;IAiBlCA;IACIA;IACJA;;;;;IAFyCA;IACrCA;IAAAA;;;;;;;;IARRA;IAIIA;MAAAA;MAAA;MAAA;MAAA,OAAiBA,sDAAjB;IAA0C,CAA1C;IAGAA;IAGJA;;;;;;;IARiDA,qCAAsB,SAAtB,EAAsBA,kfAAtB;IAKjBA;IAAAA;;;;;;IAIhCA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IAEJA;;;;;;IAEAA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;IAStCA;;;;;;;;;IAGIA;IADwBA,6CAAsB,SAAtB,EAAsBA,6NAAtB;;;;;;IAG5BA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;IAKtCA;;;;;;;;;IAGIA;IADwBA,6CAAsB,SAAtB,EAAsBA,6NAAtB;;;;;;IAG5BA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;IAKtCA;;;;;;;;;IAGIA;IADwBA,6CAAsB,SAAtB,EAAsBA,6NAAtB;;;;;;IAG5BA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;IAKtCA;;;;;;;;;IAGIA;IADwBA,6CAAsB,SAAtB,EAAsBA,6NAAtB;;;;;;IAG5BA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;IAStCA;;;;;;;IAGIA,6CAAsB,SAAtB,EAAsBA,oRAAtB;;;;;;IAEJA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEUA;;;;;IAFVA;IAAAA;;;;;;IAKtCA;;;;;;IAGqBA;;;;;;IAIrBA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IAEAA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEUA;;;;;IAFVA;IAAAA;;;;;;IAMtCA;;;;;;IAGqBA;;;;;;IAOrBA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEUA;;;;;IAFVA;IAAAA;;;;;;IAMtCA;;;;;;IAGqBA;;;;;;IAIrBA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEUA;;;;;IAFVA;IAAAA;;;;;;;;IAUtCA;IAGIA;MAAAA;MAAA;MAAA;MAAA,OAAQA,2DAAR;IAAsC,CAAtC;IAHJA;;;;;;IAIIA;;;;;;IAEJA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEIA;;;;;IAFJA;IAAAA;;;;;;;;IAStCA;IAGIA;MAAAA;MAAA;MAAA;MAAA,OAAQA,2DAAR;IAAsC,CAAtC;IAHJA;;;;;;IAIIA;;;;;;IAEJA;IAEIA;IACJA;;;;;;IACAA;IAEIA;IACJA;;;;;;IACAA;IACsCA;IAEOA;;;;;IAFPA;IAAAA;;;;;;IAStCA;;;;;;IAGAA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;IAYlCA;IACIA;;IACJA;;;;;IAFsCA;IAClCA;IAAAA;;;;;;IAJRA;IAGIA;IAGJA;;;;;IAHgCA;IAAAA;;;;;;IAIhCA;IACsCA;IAE7BA;;;;;IAF6BA;IAAAA;;;;;;;;IAe1CA;IAIIA;MAAAA;MAAA;MAAA,OAASA,kDAAT;IAAkC,CAAlC;IAJJA;;;;;;IAGIA;;;;;;IAMAA;IACIA;IACJA;;;;;;IAEAA;IAEIA;IACJA;;;;;;IATJA;IAEIA;IAIAA;IAIJA;;;;;;;IARUA;IAAAA;IAKDA;IAAAA;;;;;;IAITA;IACsCA;IAEaA;;;;;IAFbA;IAAAA;;;;;;;;IAOtCA;IACIA;MAAAA;MAAA;MAAA;MAAA,OAASA,4CAAT;IAAyB,CAAzB;IACiDA;IAACA;;;;;;IAJ9DA,2BAAkE,CAAlE,EAAkE,KAAlE,EAAkE,GAAlE;IAEQA;IAGJA;;;;;IAHaA;IAAAA;;;;;;IAhrBzBA;IAEIA;IAwNAA;IAsBAA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,CAAJ,EAAI,KAAJ,EAAI,EAAJ,EAAI,CAAJ,EAAI,KAAJ,EAAI,EAAJ;IAKgBA;IAQAA;IASAA;IAIAA;IAIAA;IAIAA;IAIAA;IAKJA;IAGRA,4BAAI,EAAJ,EAAI,KAAJ,EAAI,EAAJ,EAAI,EAAJ,EAAI,KAAJ,EAAI,EAAJ;IAGYA;IAQAA;IASAA;IAIAA;IAIAA;IAIAA;IAKJA;IAGRA,gCAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC,EAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC;IAGYA;IAUAA;IAIAA;IAIJA;IAGRA,4BAAI,EAAJ,EAAI,KAAJ,EAAI,EAAJ,EAAI,EAAJ,EAAI,KAAJ,EAAI,EAAJ;IAE0BA;IAOdA;IAIJA;IAGRA,gCAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC,EAAiC,EAAjC,EAAiC,KAAjC,EAAiC,EAAjC;IAIYA;IAWAA;IAIAA;IAMAA;IAIAA;IAIJA;IAGRA,gCAAyC,EAAzC,EAAyC,KAAzC,EAAyC,EAAzC,EAAyC,EAAzC,EAAyC,KAAzC,EAAyC,EAAzC;IAGYA;IAKAA;IAIAA;IAIJA;IACAA;IACIA;IAKAA;IAIAA;IAIJA;IACAA;IACIA;IAKAA;IAIAA;IAIJA;IACAA;IACIA;IAKAA;IAIAA;IAIJA;IAGRA,gCAAyC,EAAzC,EAAyC,KAAzC,EAAyC,EAAzC,EAAyC,EAAzC,EAAyC,KAAzC,EAAyC,EAAzC;IAGYA;IAKAA;IAIAA;IAIAA;IAIAA;IAIAA;IAIJA;IACAA;IACIA;IAOAA;IAIAA;IAKAA;IAIAA;IAKJA;IACAA;IACIA;IAUAA;IAIAA;IAIAA;IAIAA;IAKJA;IACAA;IACIA;IAOAA;IAIAA;IAIAA;IAIAA;IAKJA;IAGRA,gCAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IAGYA;IAMAA;IAIAA;IAIJA;IAGRA,gCAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,EAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IAGYA;IAMAA;IAIAA;IAIAA;IAIJA;IAGRA,iCAAwC,GAAxC,EAAwC,KAAxC,EAAwC,EAAxC,EAAwC,GAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IAGYA;IAGAA;IAIJA;IAGRA,6BAAI,GAAJ,EAAI,KAAJ,EAAI,EAAJ,EAAI,GAAJ,EAAI,KAAJ,EAAI,EAAJ;IAGYA;IAOAA;IAIJA;IAGRA,6BAAI,GAAJ,EAAI,KAAJ,EAAI,EAAJ;IAGQA;IACJA;IAEJA,iCAAwC,GAAxC,EAAwC,KAAxC,EAAwC,EAAxC;IAGQA;IAOAA;IAWAA;IAIJA;IAEJA;IAOJA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAprBAA;IACKA;IAAAA;IAwNAA;IAAAA;IA2BmBA;IAAAA;IAkBHA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAGuBA;IAAAA;IAWpBA;IAAAA;IAkBHA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAGuBA;IAAAA;IAWhBA;IAAAA;IAWPA;IAAAA;IAIAA;IAAAA;IAQqBA;IAAAA;IAQrBA;IAAAA;IAWAA;IAAAA;IAWAA;IAAAA;IAIAA;IAAAA;IAMAA;IAAAA;IAIAA;IAAAA;IASGA;IAAAA;IAMHA;IAAAA;IAIAA;IAAAA;IAKGA;IAAAA;IAMHA;IAAAA;IAIAA;IAAAA;IAKGA;IAAAA;IAMHA;IAAAA;IAIAA;IAAAA;IAKGA;IAAAA;IAMHA;IAAAA;IAIAA;IAAAA;IASGA;IAAAA;IAMHA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAKGA;IAAAA;IAQHA;IAAAA;IAIAA;IAAAA;IAKAA;IAAAA;IAIAA;IAAAA;IAMGA;IAAAA;IAWHA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAMGA;IAAAA;IAQHA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IAUGA;IAAAA;IAOHA;IAAAA;IAIAA;IAAAA;IASGA;IAAAA;IAOHA;IAAAA;IAIAA;IAAAA;IAIAA;IAAAA;IASGA;IAAAA;IAIHA;IAAAA;IASOA;IAAAA;IAQPA;IAAAA;IAQTA;IAAAA;IAOQA;IAAAA;IAQHA;IAAAA;IAWAA;IAAAA;IAKRA;IAAAA;;;;;;;;IAWrBA;IAAiEA;MAAAA;MAAA;MAAA,OAASA,wCAAT;IAAwB,CAAxB;IACCA;IACAA;IAAYA;IAAIA;IACvEA;;;;;;;;;;ADjuB/B,OAAM,MAAOQ,yBAAP,CAAgC;EAsBpCC,YAAoBC,eAApB,EACUC,cADV,EAEUC,cAFV,EAGUC,UAHV,EAIUC,MAJV,EAKUC,YALV,EAMUC,KANV,EAOmBC,OAPnB,EAO4C;IAPxB;IACV;IACA;IACA;IACA;IACA;IACA;IACS;IA1BnB,iBAAkB,IAAIC,IAAJ,EAAlB;IACA,eAAU,IAAIA,IAAJ,CAAS,YAAT,CAAV;IAGU,4BAA0C,IAAIhC,YAAJ,EAA1C;IACA,sBAAoC,IAAIA,YAAJ,EAApC;IACA,sBAAoC,IAAIA,YAAJ,EAApC;IACD,0BAA2C,EAA3C;IAET,kBAA2B,CAAC;MAAEiC,IAAI,EAAE,QAAR;MAAkBC,KAAK,EAAE,QAAzB;MAAmCC,OAAO,EAAE,EAA5C;MAAgDC,QAAQ,EAAE;IAA1D,CAAD,CAA3B;IACA,iBAAqB,IAArB;IACA,uBAAyC,EAAzC;IACA,oBAAsC,EAAtC;IACA,kBAA2B,EAA3B;IACA,qBAAqB,EAArB;IACA,0BAAyB,EAAzB;IACA,WAAa,CAAC,KAAD,EAAQ,IAAR,CAAb;IACA,aAAe,CAAC,KAAD,EAAQ,IAAR,CAAf;IACA,kBAAsB,KAAtB;IAqJA,sBAA2B,EAA3B;IAuJA,gCAAmC,CAAnC;IACA,gBAAyB,EAAzB;IAuWA,sBAA0B,KAA1B;IACA,uBAAuB,EAAvB;IACA,qBAAqB,EAArB;IACA,0BAA6B,EAA7B;IACA,wBAA2B,EAA3B;IA/oBE,KAAKV,cAAL,CAAoBW,2BAApB,GAAkDC,SAAlD,CAA4DC,GAAG,IAAG;MAChE,IAAIA,GAAG,IAAIA,GAAG,IAAI,EAAlB,EAAsB;QACpB,KAAKC,sBAAL,CAA4BD,GAA5B;QACA,KAAKb,cAAL,CAAoBe,iCAApB;MACD;IACF,CALD;EAMD;;EAEDC,QAAQ;IACN,KAAKX,OAAL,CAAaY,IAAb,CAAkB,OAAlB;IACA,KAAKb,KAAL,CAAWc,aAAX;IAEAC,UAAU,CAAC,MAAK;MACd,KAAKC,UAAL,GADc,CACK;;MACrB,KAAKC,YAAL,GAAoB,KAAKC,aAAL,CAAmBC,sBAAvC;MACA,KAAKC,eAAL,GAAuB,KAAKF,aAAL,CAAmBG,yBAA1C;;MAEA,IAAI,KAAKH,aAAL,CAAmBI,UAAvB,EAAmC;QACjC,KAAKC,WAAL,CAAiBC,QAAjB,CAA0BC,OAA1B,CAAkCC,OAAO,IAAG;UAC1CA,OAAO,CAACC,GAAR,CAAY,YAAZ,GAA2BC,QAA3B,CAAoC,IAApC,EAA0C;YAAEC,SAAS,EAAE;UAAb,CAA1C;QACD,CAFD;MAGD;;MACD,KAAK5B,OAAL,CAAa6B,IAAb,CAAkB,OAAlB;IACC,CAXS,EAWP,IAXO,CAAV;EAaD;;EAEDC,UAAU;IACR,KAAKC,eAAL,GAAuB,KAAKtC,eAAL,CAAqBuC,KAArB,CAA2B;MAChDC,OAAO,EAAE,IAAI/D,WAAJ,CAAgB,IAAhB,CADuC;MAGhDgE,YAAY,EAAE,KAAKzC,eAAL,CAAqB0C,KAArB,CAA2B,EAA3B;IAHkC,CAA3B,CAAvB;IAKA,OAAO,KAAKJ,eAAZ;EACD;;EACc,IAAXT,WAAW;IACb,OAAO,KAAKS,eAAL,CAAqBL,GAArB,CAAyB,cAAzB,CAAP;EACD;;EACI,IAADU,CAAC;IACH,OAAO,KAAKL,eAAL,CAAqBR,QAA5B;EACD,CAtEmC,CAuEpC;;;EACAc,kBAAkB;IAChB,IAAI,CAAC,CAAC,KAAKvC,YAAL,CAAkBwC,mBAAlB,CAAsCzD,eAAe,CAAC0D,kBAAtD,CAAN,EAAiF;MAC3E,KAAKA,kBAAL,GAA0B,KAAKzC,YAAL,CAAkBwC,mBAAlB,CAAsCzD,eAAe,CAAC0D,kBAAtD,CAA1B;IACD,CAFL,MAGQ;MACF,KAAK7C,cAAL,CAAoB8C,yBAApB,GAAgDjC,SAAhD,CAA2DC,GAAD,IAA8B;QACtF,KAAK+B,kBAAL,GAA0B/B,GAA1B;MACD,CAFD;IAGD;EACN;;EACDO,UAAU;IACR,IAAI,KAAKE,aAAL,CAAmBI,UAAvB,EAAmC;MACjC,MAAMoB,YAAY,GAAG,KAAKxB,aAAL,EAAoByB,QAApB,EAA8BC,iBAA9B,EAAiDC,WAAtE;MACA,MAAMC,OAAO,GAAG,KAAK5B,aAAL,EAAoB6B,kBAApC;MACA,MAAMC,OAAO,GAAG,IAAI9C,IAAJ,CAAS,KAAK+C,aAAL,EAAoBD,OAA7B,CAAhB;MACA,MAAME,KAAK,GAAG,KAAKD,aAAL,EAAoBC,KAAlC;MAEA,MAAMC,MAAM,GAAG,KAAKC,gBAAL,EAAuBjB,YAAvB,CAAoCkB,GAApC,CAAyCC,GAAD,IAAmC;QACxF,MAAMC,OAAO,GAAGC,MAAM,CAACF,GAAG,CAACC,OAAL,CAAN,IAAuB,CAAvC;QACA,MAAME,KAAK,GAAGD,MAAM,CAACF,GAAG,CAACI,QAAL,CAAN,IAAwB,CAAtC;QACA,MAAMC,KAAK,GAAG,CAACF,KAAK,GAAGF,OAAT,EAAkBK,OAAlB,CAA0B,CAA1B,CAAd;QAEA,OAAO,KAAKlE,eAAL,CAAqBuC,KAArB,CAA2B;UAChC4B,eAAe,EAAE,CAAC;YAAEzD,KAAK,EAAE4C,OAAT;YAAkBc,QAAQ,EAAE;UAA5B,CAAD,CADe;UAEhCC,aAAa,EAAC,CAAC;YAAE3D,KAAK,EAAE8C,KAAT;YAAgBY,QAAQ,EAAE;UAA1B,CAAD,CAFkB;UAGhCE,iBAAiB,EAAE,CAAC;YAAE5D,KAAK,EAAE0C,OAAT;YAAkBgB,QAAQ,EAAE;UAA5B,CAAD,CAHa;UAIhCG,GAAG,EAAE,CAAC;YAAE7D,KAAK,EAAEkD,GAAG,CAACY,GAAb;YAAkBJ,QAAQ,EAAE;UAA5B,CAAD,CAJ2B;UAKhCK,GAAG,EAAE,CAAC;YAAE/D,KAAK,EAAEkD,GAAG,CAACc,OAAb;YAAsBN,QAAQ,EAAE;UAAhC,CAAD,EAA0C1F,UAAU,CAACiG,QAArD,CAL2B;UAMhCC,IAAI,EAAE,CAAC;YAAElE,KAAK,EAAEkD,GAAG,CAACgB;UAAb,CAAD,CAN0B;UAOhCC,EAAE,EAAE,CAAC;YAAEnE,KAAK,EAAEkD,GAAG,CAACiB,EAAb;YAAiBT,QAAQ,EAAE;UAA3B,CAAD,CAP4B;UAQhCU,EAAE,EAAE,CAAC;YAAEpE,KAAK,EAAEkD,GAAG,CAACkB,EAAb;YAAiBV,QAAQ,EAAE;UAA3B,CAAD,CAR4B;UAShCW,EAAE,EAAE,CAAC;YAAErE,KAAK,EAAEkD,GAAG,CAACmB,EAAb;YAAiBX,QAAQ,EAAE;UAA3B,CAAD,CAT4B;UAUhCY,EAAE,EAAE,CAAC;YAAEtE,KAAK,EAAEkD,GAAG,CAACoB,EAAb;YAAiBZ,QAAQ,EAAE;UAA3B,CAAD,CAV4B;UAWhCa,iBAAiB,EAAE,CAAC;YAAEvE,KAAK,EAAEoD,MAAM,CAACF,GAAG,CAACsB,EAAL,CAAN,CAAeC,QAAf,EAAT;YAAoCf,QAAQ,EAAE;UAA9C,CAAD,CAXa;UAYhCgB,iBAAiB,EAAE,CAAC;YAAE1E,KAAK,EAAEkD,GAAG,CAACyB,EAAJ,GAASvB,MAAM,CAACF,GAAG,CAACyB,EAAL,CAAN,CAAeF,QAAf,EAAT,GAAqC,EAA9C;YAAkDf,QAAQ,EAAE;UAA5D,CAAD,CAZa;UAahCkB,iBAAiB,EAAE,CAAC;YAAE5E,KAAK,EAAEkD,GAAG,CAAC2B,EAAJ,GAASzB,MAAM,CAACF,GAAG,CAAC2B,EAAL,CAAN,CAAeJ,QAAf,EAAT,GAAqC,EAA9C;YAAkDf,QAAQ,EAAE;UAA5D,CAAD,CAba;UAchCoB,iBAAiB,EAAE,CAAC;YAAE9E,KAAK,EAAEkD,GAAG,CAAC6B,EAAJ,GAAS3B,MAAM,CAACF,GAAG,CAAC6B,EAAL,CAAN,CAAeN,QAAf,EAAT,GAAqC,EAA9C;YAAkDf,QAAQ,EAAE;UAA5D,CAAD,CAda;UAehCsB,WAAW,EAAE,CAAC;YAAEhF,KAAK,EAAEkD,GAAG,CAACC,OAAb;YAAsBO,QAAQ,EAAE;UAAhC,CAAD,EAA0C,CAAC1F,UAAU,CAACiG,QAAZ,CAA1C,CAfmB;UAgBhCgB,cAAc,EAAE,CAAC;YAAEjF,KAAK,EAAEkD,GAAG,CAACI,QAAb;YAAuBI,QAAQ,EAAE;UAAjC,CAAD,EAA0C,CAAE1F,UAAU,CAACiG,QAAb,EAAuBjG,UAAU,CAACkH,GAAX,CAAe,QAAf,CAAvB,EAAiDlH,UAAU,CAACmH,GAAX,CAAe,CAAf,CAAjD,CAA1C,CAhBgB;UAiBhC5B,KAAK,EAAE,CAAC;YAAEvD,KAAK,EAAEuD,KAAT;YAAgBG,QAAQ,EAAE;UAA1B,CAAD,CAjByB;UAkBhC0B,KAAK,EAAE,CAAC;YAAEpF,KAAK,EAAEkD,GAAG,CAACkC,KAAb;YAAoB1B,QAAQ,EAAE;UAA9B,CAAD,CAlByB;UAmBhC2B,oBAAoB,EAAE,CAAC;YAAErF,KAAK,EAAEsC,YAAT;YAAuBoB,QAAQ,EAAE;UAAjC,CAAD,CAnBU;UAoBhC4B,UAAU,EAAE,EApBoB;UAqBhCC,cAAc,EAAE,EArBgB;UAsBhCC,YAAY,EAAE,EAtBkB;UAuBhCC,QAAQ,EAAE,EAvBsB;UAwBhCC,UAAU,EAAE,IAxBoB;UAyBhCC,SAAS,EAAE,EAzBqB;UA0BhCC,MAAM,EAAE,EA1BwB;UA2BhCC,SAAS,EAAE,EA3BqB;UA4BhCC,SAAS,EAAE,EA5BqB;UA6BhCC,SAAS,EAAE,EA7BqB;UA8BhCC,OAAO,EAAE,IA9BuB;UA+BhCC,OAAO,EAAE;QA/BuB,CAA3B,CAAP;MAiCD,CAtCc,CAAf;MAwCAlD,MAAM,CAAC1B,OAAP,CAAeQ,KAAK,IAAI,KAAKV,WAAL,CAAiB+E,IAAjB,CAAsBrE,KAAtB,CAAxB;IAED;;IAED,IAAI,KAAKf,aAAL,CAAmBqF,WAAnB,IAAkC,KAAKrF,aAAL,CAAmBI,UAAzD,EAAqE;MAEjE,KAAKkF,YAAL;MACA,KAAKC,oBAAL;MACA,KAAKnE,kBAAL;IACH,CAxDO,CA0DR;;;IACA,IAAI,KAAKpB,aAAL,CAAmBwF,WAAvB,EAAoC;MAClC,KAAKC,oBAAL;MACA,KAAK3E,eAAL,CAAqB4E,OAArB;MACA,KAAK5E,eAAL,CAAqBR,QAArB,CAA8B,SAA9B,EAAyCqF,MAAzC;IACD;;IACD,KAAKC,cAAL;EACD;;EAGD5E,OAAO;IACL,IAAI,KAAKhB,aAAL,CAAmBI,UAAvB,EAAmC;MACjC,KAAK,IAAIyF,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK3D,gBAAL,EAAuBjB,YAAvB,CAAoC6E,MAAhE,EAAwED,KAAK,EAA7E,EAAiF;QAC/E,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,YAA/B,EAA6CX,UAA7C,CAAwD,IAAxD;QACA,KAAKO,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,gBAA/B,EAAiDX,UAAjD,CAA4D+F,KAAK,GAAG,CAApE;MACD;IACF;EACF;;EACDG,OAAO,IAEN;;EAEDC,YAAY;IACV,KAAKC,UAAL;;IACA,IAAI,KAAKpF,eAAL,CAAqBqF,OAAzB,EAAkC;MAChChJ,uBAAuB,CAACiJ,iBAAxB,CAA0C,KAAKtF,eAA/C;MACA,OAAO,KAAP;IACD;;IACD,OAAO,IAAP;EACD;;EAGDuF,eAAe,CAACC,SAAD,EAAiBT,KAAjB,EAA2B;IAC1C,IAAI1B,cAAc,GAAG,KAAK9D,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,gBAA/B,EAAiDvB,KAAjD,GAAyD,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,gBAA/B,EAAiDvB,KAA1G,GAAgH,GAArI;IACE,IAAIgF,WAAW,GAAG,KAAK7D,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,aAA/B,EAA8CvB,KAAhE;;IACA,IAAIoH,SAAJ,EAAe;MACb,IAAIC,SAAS,GAAG,KAAKC,aAAL,CAAmBC,MAAnB,CAA0BC,IAAI,IAAIA,IAAI,CAACC,IAAL,IAAaL,SAA/C,EAA0D,CAA1D,GAA8DM,MAA9E;;MACC,IAAG,CAAC,CAACL,SAAL,EAAe;QACdrC,WAAW,GAAEqC,SAAb;MACA,CAFD,MAGI;QACHrC,WAAW,GAAI,KAAKlE,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAA/C,IAA0D,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAAzG,IAAoH,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAApK,GACdjJ,SAAS,CAACkJ,SADI,GACQlJ,SAAS,CAACmJ,qBADhC;MAEA;;MAEF,IAAIC,cAAc,GAAG,KAAKC,QAAL,CAAcC,IAAd,CAAmBT,IAAI,IAAIA,IAAI,CAACzD,GAAL,IAAYqD,SAAvC,EAAkDc,WAAvE;MACA,KAAK/G,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2B/F,UAA3B,CAAsC;QACpCqE,cAAc,EAAGA,cADmB;QAEpC1B,KAAK,EAAE,CAAC0B,cAAc,GAAGD,WAAlB,EAA+BxB,OAA/B,CAAuC,CAAvC,CAF6B;QAGpCU,IAAI,EAAE;UAAClE,KAAK,EAAG+H;QAAT,CAH8B;QAIpCzC,UAAU,EAAC,IAJyB;QAKpCN,WAAW,EAAE5B,MAAM,CAAC+E,UAAP,CAAkBnD,WAAlB,EAA+BxB,OAA/B,CAAuC,CAAvC;MALuB,CAAtC;MAQA,KAAK4E,cAAL,CAAoB/G,OAApB,CAA4BgH,OAAO,IAAG;QACpC,IAAIA,OAAO,KAAKjB,SAAhB,EAA2B;UACzB,KAAKjG,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,KAA/B,EAAsC+G,SAAtC,CAAgD;YAAEC,UAAU,EAAE;UAAd,CAAhD;QACD;MACF,CAJD;IAMD,CAzBD,MA0BK;MACH,KAAKpH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2B/F,UAA3B,CAAsC;QACpCqE,cAAc,EAAE,IADoB;QAEpC1B,KAAK,EAAE,CAAC,IAAIyB,WAAL,EAAkBxB,OAAlB,CAA0B,CAA1B;MAF6B,CAAtC;IAID;;IAED,KAAKgF,cAAL,CAAoBC,IAApB;IACA,KAAKC,eAAL;EACD;;EAEDrC,oBAAoB;IAClB,IAAI,KAAK1G,YAAL,CAAkBwC,mBAAlB,CAAsCzD,eAAe,CAAC4I,aAAtD,CAAJ,EAA0E;MACxE,KAAKA,aAAL,GAAqB,KAAK3H,YAAL,CAAkBwC,mBAAlB,CAAsCzD,eAAe,CAAC4I,aAAtD,CAArB;MACA;IACD;;IACD,KAAK7H,UAAL,CAAgB4G,oBAAhB,GAAuCsC,IAAvC,CAA4CxK,KAAK,EAAjD,EAAqDiC,SAArD,CAA+DwI,QAAQ,IAAG;MACxE,KAAKtB,aAAL,GAAqBsB,QAAQ,CAACC,OAA9B;IACD,CAFD;EAGD;;EAEDC,YAAY;IAEV,KAAK,IAAInC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK/E,eAAL,CAAqBR,QAArB,CAA8B,cAA9B,EAA8CpB,KAA9C,CAAoD4G,MAAhF,EAAwFD,KAAK,EAA7F,EAAiG;MAC/F,IAAI,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,sBAA/B,EAAuDvB,KAAvD,CAA6D4G,MAA7D,IAAuE,EAA3E,EAA+E;QAC7E,KAAKzF,WAAL,CAAiB0F,EAAjB,CAAoB,KAAKjF,eAAL,CAAqBR,QAArB,CAA8B,cAA9B,EAA8CpB,KAA9C,CAAoD4G,MAApD,GAA6D,CAAjF,EAAoFrF,GAApF,CAAwF,sBAAxF,EAAgH+G,SAAhH,CAA0H;UAAES,SAAS,EAAE;QAAb,CAA1H;MACD;;MACD,KAAKC,oBAAL,CAA0BrC,KAA1B,EAAiC,iBAAjC;MACA,KAAKsC,uBAAL,CAA6BtC,KAA7B,EAAoC,eAApC;MACA,KAAKuC,aAAL,CAAmBvC,KAAnB;MACA,KAAKwC,4BAAL,CAAkCxC,KAAlC;MACA,KAAK+B,eAAL;IACD;;IAED,OAAO,KAAK3B,YAAL,EAAP;EAED;;EAEDqC,UAAU,CAACC,CAAD,EAAI1C,KAAJ,EAAS;IAEjB,IAAI0C,CAAJ,EAAO;MACL,IAAI,CAAC,KAAKlI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0CvB,KAA/C,EACE,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0C+G,SAA1C,CAAoD;QAAErE,QAAQ,EAAE;MAAZ,CAApD;MACF,IAAI,CAAC,KAAK9C,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,QAA/B,EAAyCvB,KAA9C,EACE,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,QAA/B,EAAyC+G,SAAzC,CAAmD;QAAErE,QAAQ,EAAE;MAAZ,CAAnD;MACF,IAAI,CAAC,KAAK9C,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0CvB,KAA/C,EACE,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0C+G,SAA1C,CAAoD;QAAErE,QAAQ,EAAE;MAAZ,CAApD;IACH,CAPD,MAOO;MACL,KAAK9C,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0C+G,SAA1C,CAAoD,IAApD;MACA,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,QAA/B,EAAyC+G,SAAzC,CAAmD,IAAnD;MACA,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0C+G,SAA1C,CAAoD,IAApD;IACD;;IACD,KAAKa,4BAAL,CAAkCxC,KAAlC;EACD;;EACDwC,4BAA4B,CAACxC,KAAD,EAAM;IAChC,IAAI2C,MAAM,GAAG,CAAC,SAAD,EAAY,QAAZ,EAAsB,SAAtB,EAAiC,YAAjC,CAAb;IACA,IAAIC,oBAAoB,GAAG,KAA3B;;IACA,KAAK,MAAMC,KAAX,IAAoBF,MAApB,EAA4B;MAC1BC,oBAAoB,GAAGA,oBAAoB,IAAI,CAAC,CAAC,KAAKpI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCxJ,KAAvF;IACD;;IACD,IAAIuJ,oBAAJ,EAA0B;MACxB,KAAK,MAAMC,KAAX,IAAoBF,MAApB,EAA4B;QAC1B,IAAI,CAAE,CAAC,CAAC,KAAKnI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCxJ,KAA9C,EAAsD;UACpD,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsClB,SAAtC,CAAgD;YAAErE,QAAQ,EAAE;UAAZ,CAAhD;UACA,KAAK9C,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCC,aAAtC,CAAoDzL,UAAU,CAACiG,QAA/D;QACD;MACF;IACF,CAPD,MAOO;MACL,KAAK,MAAMuF,KAAX,IAAoBF,MAApB,EAA4B;QAC1B,KAAKnI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCE,gBAAtC,CAAuD1L,UAAU,CAACiG,QAAlE;;QACA,IAAI,KAAK9C,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCG,QAAtC,CAA+C,UAA/C,CAAJ,EAAgE;UAC9D,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCI,MAAtC,CAA6C,UAA7C,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCK,sBAAtC;QACD;MACF;IAEF,CAtB+B,CAuBhC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;EACD;;EACDC,KAAK,CAACnD,KAAD,EAAM;IACT,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0C+G,SAA1C,CAAoD,IAApD;IACA,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,QAA/B,EAAyC+G,SAAzC,CAAmD,IAAnD;IACA,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0C+G,SAA1C,CAAoD,IAApD;EACD;;EAEDyB,aAAa,CAACpD,KAAD,EAAM;IACjB,IAAIqD,OAAO,GAAG,KAAKpI,eAAL,CAAqBR,QAArB,CAA8B,cAA9B,CAAd;IACA,MAAM6I,SAAS,GAAGD,OAAO,CAAC5I,QAAR,CAAiBuF,KAAjB,CAAlB;IACA,OAAOsD,SAAP;EACD;;EAIDC,oBAAoB,CAACb,CAAD,EAAIc,CAAJ,EAAK;IACvB,IAAInF,WAAW,GAAG,KAAK7D,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,aAA3B,EAA0CvB,KAA5D;;IACA,IAAIgF,WAAW,IAAI,IAAf,IAAuBA,WAAW,IAAIoF,SAAtC,IAAmDpF,WAAW,CAAC4B,MAAZ,IAAsB,CAA7E,EAAgF;MAC9E5B,WAAW,GAAI,KAAKlE,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAA/C,IAA0D,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAAzG,IAAoH,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAApK,GACdjJ,SAAS,CAACkJ,SADI,GACQlJ,SAAS,CAACmJ,qBADhC;MAEA,KAAK3G,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,aAA3B,EAA0CC,QAA1C,CAAmDwD,WAAnD;IACD;;IACD,IAAIC,cAAc,GAAW,KAAK9D,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,gBAA3B,EAA6CvB,KAA1E;IACA,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuBvJ,UAAvB,CAAkC;MAChC2C,KAAK,EAAEH,MAAM,CAAC+E,UAAP,CAAkB,CAAClD,cAAc,GAAGD,WAAlB,EAA+BP,QAA/B,EAAlB,EAA6DjB,OAA7D,CAAqE,CAArE;IADyB,CAAlC;IAIA,KAAKgF,cAAL,CAAoBC,IAApB,CAAyBY,CAAzB;IACA,OAAO,KAAP;EACD;;EAMDgB,aAAa;IACX,MAAMzH,OAAO,GAAM,KAAK9B,aAAL,EAAoBI,UAApB,GAAiC,IAAIpB,IAAJ,CAAS,KAAKgB,aAAL,EAAoBwJ,aAApB,CAAkC1H,OAA3C,CAAjC,GAAuF,IAAI9C,IAAJ,CAAS,KAAKgB,aAAL,EAAoB6G,cAApB,CAAmC4C,YAA5C,CAA1G;IACA,MAAMzH,KAAK,GAAG,KAAKhC,aAAL,EAAoBI,UAApB,GAAiC,IAAIpB,IAAJ,CAAS,KAAKgB,aAAL,EAAoBwJ,aAApB,CAAkCxH,KAA3C,CAAjC,GAAqF,IAAIhD,IAAJ,CAAS,KAAKgB,aAAL,EAAoB6G,cAApB,CAAmC6C,UAA5C,CAAnG;IACA,IAAI5G,iBAAiB,GAAG,IAAxB;;IACA,IAAI,CAAC,CAAC,KAAKzC,WAAL,CAAiBC,QAAnB,IAA+B,KAAKD,WAAL,CAAiBC,QAAjB,CAA0BwF,MAA1B,GAAmC,CAAtE,EAAyE;MACvEhD,iBAAiB,GAAG,KAAKzC,WAAL,CAAiBC,QAAjB,CAA0B,CAA1B,EAA6BpB,KAA7B,CAAmC4D,iBAAvD;IACD;;IACD,MAAM6G,QAAQ,GAAG,KAAKnL,eAAL,CAAqBuC,KAArB,CAA2B;MAC1C4B,eAAe,EAAE,IAAI1F,WAAJ,CAAgB;QAACiC,KAAK,EAAE4C,OAAR;QAAiBc,QAAQ,EAAE;MAA3B,CAAhB,EAAkD,CAAC1F,UAAU,CAACiG,QAAZ,CAAlD,CADyB;MAE1CN,aAAa,EAAE,IAAI5F,WAAJ,CAAiB;QAACiC,KAAK,EAAE8C,KAAR;QAAeY,QAAQ,EAAE;MAAzB,CAAjB,EAAiD,CAAC1F,UAAU,CAACiG,QAAZ,CAAjD,CAF2B;MAG1CL,iBAAiB,EAAE,IAAI7F,WAAJ,CAAgB6F,iBAAhB,EAAmC5F,UAAU,CAACiG,QAA9C,CAHuB;MAI1CJ,GAAG,EAAE,IAAI9F,WAAJ,CAAgB,IAAhB,CAJqC;MAK1CmG,IAAI,EAAE,CAAC;QAAElE,KAAK,EAAE;MAAT,CAAD,CALoC;MAM1C+D,GAAG,EAAE,IAAIhG,WAAJ,CAAgB,IAAhB,EAAsB,CAACC,UAAU,CAACiG,QAAZ,CAAtB,CANqC;MAO1CE,EAAE,EAAE,CAAC;QAAEnE,KAAK,EAAE,EAAT;QAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA1C,CAAD,EAA0D7H,iBAA1D,CAPsC;MAQ1C2F,EAAE,EAAE,CAAC;QAAEpE,KAAK,EAAE,EAAT;QAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA1C,CAAD,EAA0D7H,iBAA1D,CARsC;MAS1C4F,EAAE,EAAE,CAAC;QAAErE,KAAK,EAAE,EAAT;QAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA1C,CAAD,EAA0D7H,iBAA1D,CATsC;MAU1C6F,EAAE,EAAE,CAAC;QAAEtE,KAAK,EAAE,EAAT;QAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA1C,CAAD,EAA0D7H,iBAA1D,CAVsC;MAW1C8F,iBAAiB,EAAE,IAAIxG,WAAJ,CAAgB,GAAhB,EAAqB,CAACC,UAAU,CAACiG,QAAZ,CAArB,CAXuB;MAY1CS,iBAAiB,EAAE,CAAC;QAAE1E,KAAK,EAAE,EAAT;QAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA1C,CAAD,CAZuB;MAa1C1B,iBAAiB,EAAE,CAAC;QAAE5E,KAAK,EAAE,EAAT;QAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA1C,CAAD,CAbuB;MAc1CxB,iBAAiB,EAAE,CAAC;QAAE9E,KAAK,EAAE,EAAT;QAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA1C,CAAD,CAduB;MAe1CtB,WAAW,EAAE,IAAIjH,WAAJ,CAAiB,KAAK+C,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAA/C,IAA0D,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAAzG,IAAoH,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAApK,GAA+KjJ,SAAS,CAACkJ,SAAzL,GAAqMlJ,SAAS,CAACmJ,qBAA/N,EAAsP9J,UAAU,CAACiG,QAAjQ,CAf6B;MAgB1CgB,cAAc,EAAG,IAAIlH,WAAJ,CAAgB;QAAEiC,KAAK,EAAE,GAAT;QAAc0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA3C,CAAhB,EAA0E,CAACtI,UAAU,CAACiG,QAAZ,EAAsBjG,UAAU,CAACkH,GAAX,CAAe,QAAf,CAAtB,EAAgDlH,UAAU,CAACmH,GAAX,CAAe,CAAf,CAAhD,CAA1E,CAhByB;MAiB1C5B,KAAK,EAAE,CAAC;QAAEvD,KAAK,EAAG,KAAKc,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAA/C,IAA0D,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAAzG,IAAoH,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAApK,GAA+KjJ,SAAS,CAACkJ,SAAzL,GAAsMlJ,SAAS,CAACmJ,qBAAzN;QAAgPpE,QAAQ,EAAE;MAA1P,CAAD,CAjBmC;MAkB1C0B,KAAK,EAAE,CAAC;QAAEpF,KAAK,EAAE,IAAT;QAAe0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;MAA5C,CAAD,CAlBmC;MAmB1CjB,oBAAoB,EAAE,CAAC;QAAErF,KAAK,EAAE,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoB,CAApB,GAAwBtF,GAAxB,CAA4B,sBAA5B,GAAqDvB,KAA9D;QAAqE0D,QAAQ,EAAE;MAA/E,CAAD,EAAwF,CAAC1F,UAAU,CAAC0M,SAAX,CAAqB,EAArB,CAAD,EAA2B1M,UAAU,CAAC2M,SAAX,CAAqB,EAArB,CAA3B,CAAxF,CAnBoB;MAoB1C;MACArF,UAAU,EAAE,EArB8B;MAsB1CC,cAAc,EAAE,KAAKpE,WAAL,CAAiByF,MAAjB,GAA0B,CAtBA;MAuB1CpB,YAAY,EAAE,IAAIzH,WAAJ,CAAgB,EAAhB,EAAoB,CAACS,2BAAD,EAA8BR,UAAU,CAACmH,GAAX,CAAe,CAAf,CAA9B,EAAiDnH,UAAU,CAACkH,GAAX,CAAe,WAAf,CAAjD,CAApB,CAvB4B;MAwB1CO,QAAQ,EAAE,EAxBgC;MAyB1CC,UAAU,EAAE,EAzB8B;MA0B1CC,SAAS,EAAE,EA1B+B;MA2B1CC,MAAM,EAAE,IAAI7H,WAAJ,CAAgB,EAAhB,EAAoB,CAACS,2BAAD,EAA8BR,UAAU,CAACmH,GAAX,CAAe,CAAf,CAA9B,EAAiDnH,UAAU,CAACkH,GAAX,CAAe,WAAf,CAAjD,CAApB,CA3BkC;MA4B1CW,SAAS,EAAE,EA5B+B;MA6B1CC,SAAS,EAAE,EA7B+B;MA8B1CC,SAAS,EAAE,EA9B+B;MA+B1CC,OAAO,EAAE,EA/BiC;MAgC1CC,OAAO,EAAE;IAhCiC,CAA3B,CAAjB;IAmCA,KAAK9E,WAAL,CAAiB+E,IAAjB,CAAsBuE,QAAtB,EA1CW,CA2CX;IACA;IACA;IACA;IACA;EAED;;EACDG,aAAa,CAACT,CAAD,EAAE;IAEb,KAAKhJ,WAAL,CAAiB0J,QAAjB,CAA0BV,CAA1B;IACA,KAAK3B,cAAL,CAAoBC,IAApB;IACA,KAAKK,YAAL;EACD;;EAEDtG,iBAAiB,CAAC6G,CAAD,EAAO;IACtB,KAAK/I,sBAAL,CAA4B+I,CAAC,CAACyB,MAAF,CAAS9K,KAArC;IACA,KAAKR,cAAL,CAAoBuL,+BAApB,CAAoD1B,CAAC,CAACyB,MAAF,CAAS9K,KAA7D;EACD;;EACDM,sBAAsB,CAAC0K,IAAD,EAAK;IACzB,IAAI,CAAC,CAAC,KAAKpJ,eAAL,CAAqBR,QAA3B,EAAqC;MACnC,KAAK,IAAIuF,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK/E,eAAL,CAAqBR,QAArB,CAA8B,cAA9B,EAA8CpB,KAA9C,CAAoD4G,MAAhF,EAAwFD,KAAK,EAA7F,EAAiG;QAC/F,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,sBAA/B,EAAuDX,UAAvD,CAAkEoK,IAAlE;MACD;IACF;EACF;;EACDtE,cAAc;IACZ,IAAI,CAAC,CAAC,KAAKvF,WAAL,CAAiBC,QAAnB,IAA+B,KAAKD,WAAL,CAAiBC,QAAjB,CAA0BwF,MAA1B,GAAmC,CAAtE,EAAyE;MACvE,KAAK,IAAIY,IAAT,IAAiB,KAAKrG,WAAL,CAAiBC,QAAlC,EAA4C;QAC1CoG,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4B0J,YAA5B,CAAyCtC,IAAzC,CAA8CzK,oBAAoB,EAAlE,EAAsEkC,SAAtE,CAAiFqD,eAAD,IAAoB;UAClG,IAAI,CAAC,CAACA,eAAN,EAAuB;YACrB,IAAI,CAAC,CAAC+D,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0BvB,KAAhC,EAAuC;cACrC,IAAI,IAAIF,IAAJ,CAAS2D,eAAT,EAA0ByH,OAA1B,KAAuC,IAAIpL,IAAJ,CAAS0H,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0BvB,KAAnC,EAA0CkL,OAA1C,EAA3C,EAAiG;gBAC/F1D,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4B+G,SAA5B,CAAsC;kBAAE6C,WAAW,EAAE;gBAAf,CAAtC;cACD,CAFD,MAEO,IAAG,IAAIrL,IAAJ,CAAS2D,eAAT,EAA0ByH,OAA1B,KAAuC,IAAIpL,IAAJ,CAAS0H,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0BvB,KAAnC,EAA0CkL,OAA1C,EAA1C,EAA+F;gBACpG1D,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BsI,sBAA5B;gBACArC,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0BsI,sBAA1B;cACD,CAHM,MAGA;gBACL,IAAIrC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BoI,QAA5B,CAAqC,aAArC,CAAJ,EAAyD;kBACvD,OAAOnC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BqI,MAA5B,CAAmC,aAAnC,CAAP;kBACApC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BsI,sBAA5B;gBACD;;gBACD,IAAIrC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BoI,QAA5B,CAAqC,UAArC,CAAJ,EAAsD;kBACpD,OAAOnC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BqI,MAA5B,CAAmC,aAAnC,CAAP;kBACApC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BsI,sBAA5B;gBACD;cACF;YACF,CAhBD,MAgBO;cACL,IAAIrC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BoI,QAA5B,CAAqC,aAArC,CAAJ,EAAyD;gBACvD,OAAOnC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BqI,MAA5B,CAAmC,aAAnC,CAAP;gBACApC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BsI,sBAA5B;cACD;;cACD,IAAIrC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BoI,QAA5B,CAAqC,UAArC,CAAJ,EAAsD;gBACpD,OAAOnC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BqI,MAA5B,CAAmC,aAAnC,CAAP;gBACApC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BsI,sBAA5B;cACD;YACF;UACF,CA3BD,MA2BO;YACLrC,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4B+G,SAA5B,CAAsC;cAAErE,QAAQ,EAAE;YAAZ,CAAtC;UACD;QACF,CA/BD;QAgCAuD,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0B0J,YAA1B,CAAuCtC,IAAvC,CAA4CzK,oBAAoB,EAAhE,EAAoEkC,SAApE,CAA+EuD,aAAD,IAAkB;UAC9F,IAAI,CAAC,CAACA,aAAN,EAAqB;YACnB,IAAI,CAAC,CAAC6D,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BvB,KAAlC,EAAyC;cACvC,IAAI,IAAIF,IAAJ,CAAS6D,aAAT,EAAwBuH,OAAxB,KAAqC,IAAIpL,IAAJ,CAAS0H,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BvB,KAArC,EAA4CkL,OAA5C,EAAzC,EAAiG;gBAC/F1D,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0B+G,SAA1B,CAAoC;kBAAE6C,WAAW,EAAE;gBAAf,CAApC;cACD,CAFD,MAEO;gBACL3D,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0B+G,SAA1B,CAAoC,IAApC;gBACAd,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BsI,sBAA5B;cACD;YACF,CAPD,MAOO;cACLrC,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0B+G,SAA1B,CAAoC,IAApC;YACD;UACF,CAXD,MAWO;YACLd,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0B+G,SAA1B,CAAoC;cAAErE,QAAQ,EAAE;YAAZ,CAApC;UACD;QACF,CAfD;MAgBD;IACF;EACF;;EACDmH,qBAAqB,CAAC/B,CAAD,EAAM;IAEzB,IAAI,CAAC,CAAC,KAAKzH,eAAL,CAAqBR,QAA3B,EAAqC;MACnC,KAAK,IAAIuF,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK/E,eAAL,CAAqBR,QAArB,CAA8B,cAA9B,EAA8CpB,KAA9C,CAAoD4G,MAAhF,EAAwFD,KAAK,EAA7F,EAAiG;QAC/F,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,mBAA/B,EAAoDX,UAApD,CAA+D,CAAC,CAACyI,CAAC,EAAErJ,KAAL,GAAWqJ,CAAC,CAACrJ,KAAb,GAAmB,IAAlF;MACD;IACF;EACF;;EACDqL,QAAQ;IACN,IAAIC,MAAM,GAAG,EAAb;IACA,IAAIC,OAAJ;;IACA,IAAG,KAAKzK,aAAL,CAAmBI,UAAtB,EAAiC;MAC/BqK,OAAO,GAAG,IAAIzL,IAAJ,CAAS,KAAKgB,aAAL,EAAoBwJ,aAApB,EAAmC1H,OAA5C,EAAqDsI,OAArD,EAAV;IACD,CAFD,MAGK,IAAG,CAAC,CAAC,KAAKpK,aAAL,EAAoB6G,cAAzB,EAAwC;MAC1C4D,OAAO,GAAG,IAAIzL,IAAJ,CAAS,KAAKgB,aAAL,EAAoB6G,cAApB,EAAoC4C,YAA7C,EAA2DW,OAA3D,EAAV;IACF;;IAED,KAAK,MAAM1D,IAAX,IAAmB,KAAKQ,QAAxB,EAAkC;MAEhC,IAAIwD,QAAQ,GAAI,IAAI1L,IAAJ,CAAS0H,IAAI,CAACgE,QAAd,CAAD,CAA0BN,OAA1B,EAAf;MACA,IAAIO,SAAS,GAAI,IAAI3L,IAAJ,CAAS0H,IAAI,CAACiE,SAAd,CAAD,CAA2BP,OAA3B,EAAhB;;MACA,IAAI,CAACM,QAAQ,GAAGD,OAAX,IAAsBC,QAAQ,IAAID,OAAnC,MAAgDE,SAAS,GAAGF,OAAZ,IAAuBE,SAAS,IAAIF,OAApF,KAAgG,CAAE,CAAC,CAACD,MAAM,CAACrD,IAAP,CAAYoB,CAAC,IAAIA,CAAC,CAACqC,OAAF,IAAalE,IAAI,CAACkE,OAAnC,CAAxG,EAAsJ;QACpJJ,MAAM,CAACpF,IAAP,CAAYsB,IAAZ;MACD;IACF;;IACD,KAAKmE,QAAL,GAAgBL,MAAhB;;IACA,IAAG,KAAKxK,aAAL,CAAmBqF,WAAtB,EAAkC;MAChC,KAAKyF,kBAAL;IACD;EACF;;EAEDxF,YAAY;IACV,IAAIrC,GAAG,GAAG8H,YAAY,CAACC,OAAb,CAAqBpN,eAAe,CAACqN,MAArC,CAAV;;IACA,IAAI,CAAC,CAAChI,GAAN,EAAW;MACT,KAAKiE,QAAL,GAAgBgE,IAAI,CAACC,KAAL,CAAW3N,UAAU,CAAC4N,UAAX,CAAsBnI,GAAtB,CAAX,CAAhB;MACA,KAAKsH,QAAL;IACD,CAHD,MAGO;MACL,KAAK5L,UAAL,CAAgB0M,WAAhB,GAA8B/L,SAA9B,CAAyCC,GAAD,IAAQ;QAC9C,IAAI,CAAC,CAACA,GAAN,EAAW;UACT,KAAK2H,QAAL,GAAgB3H,GAAhB;UACA,KAAKgL,QAAL;QACD;MACF,CALD;IAMD;EACF;;EACDe,UAAU,CAACpB,IAAD,EAAa;IACrB,IAAIA,IAAI,KAAK,EAAT,IAAeA,IAAI,KAAK,GAAxB,IAA+BA,IAAI,KAAK,IAA5C,EAAkD;MAChD,OAAO,IAAP;IACD;;IACD,OAAO,KAAP;EAED;;EACDzE,oBAAoB;IAClB,IAAI8F,KAAK,GAAW,CAApB;IACA,KAAKvL,aAAL,EAAoB6G,cAApB,EAAoC2E,qBAApC,EAA2DC,2BAA3D,CAAuFlL,OAAvF,CAAgG6B,GAAD,IAA0C;MACvI,IAAIsJ,IAAI,GAAGtJ,GAAG,CAACuJ,gBAAf;MACA,IAAIC,QAAQ,GAAGF,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,CAAlB,IAAuB,GAAvB,GAA6BH,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,CAAlB,CAA7B,GAAoD,GAApD,GAA0DH,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,CAAlB,CAAzE;MACA,IAAIC,MAAM,GAAGF,QAAb;MAEA,IAAIF,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,EAAlB,KAAyBH,IAAI,CAACG,SAAL,CAAe,EAAf,EAAmB,EAAnB,CAAzB,IAAmDH,IAAI,CAACG,SAAL,CAAe,EAAf,EAAmB,EAAnB,CAAvD,EACEC,MAAM,GAAGJ,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,EAAlB,IAAwB,GAAxB,GAA8BH,IAAI,CAACG,SAAL,CAAe,EAAf,EAAmB,EAAnB,CAA9B,GAAuD,GAAvD,GAA6DH,IAAI,CAACG,SAAL,CAAe,EAAf,EAAmB,EAAnB,CAAtE;MACF,MAAMlC,QAAQ,GAAG,KAAKnL,eAAL,CAAqBuC,KAArB,CAA2B;QAC1C4B,eAAe,EAAE,CAAC;UAAEzD,KAAK,EAAE0M,QAAT;UAAmBhJ,QAAQ,EAAE;QAA7B,CAAD,CADyB;QAE1CC,aAAa,EAAE,CAAC;UAAE3D,KAAK,EAAE4M,MAAT;UAAiBlJ,QAAQ,EAAE;QAA3B,CAAD,CAF2B;QAG1CE,iBAAiB,EAAE,CAAC;UAAE5D,KAAK,EAAEkD,GAAG,CAAC2J,uBAAb;UAAsCnJ,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;QAAnE,CAAD,EAAmFtI,UAAU,CAACiG,QAA9F,CAHuB;QAI1CJ,GAAG,EAAE,CAAC;UAAE7D,KAAK,EAAE,KAAKoM,UAAL,CAAgBlJ,GAAG,CAAC4J,uBAApB,CAAT;UAAuDpJ,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;QAApF,CAAD,CAJqC;QAK1CpC,IAAI,EAAE,CAAC;UAAElE,KAAK,EAAEkD,GAAG,CAAC6J;QAAb,CAAD,CALoC;QAM1ChJ,GAAG,EAAE,IAAIhG,WAAJ,CAAgBmF,GAAG,CAAC8J,oBAApB,EAA0C,CAAChP,UAAU,CAACiG,QAAZ,CAA1C,CANqC;QAO1CE,EAAE,EAAE,IAAIpG,WAAJ,CAAgBmF,GAAG,CAAC+J,yBAApB,EAA+CxO,iBAA/C,CAPsC;QAQ1C2F,EAAE,EAAE,IAAIrG,WAAJ,CAAgBmF,GAAG,CAACgK,yBAApB,EAA+CzO,iBAA/C,CARsC;QAS1C4F,EAAE,EAAE,IAAItG,WAAJ,CAAgBmF,GAAG,CAACiK,yBAApB,EAA+C1O,iBAA/C,CATsC;QAU1C6F,EAAE,EAAE,IAAIvG,WAAJ,CAAgBmF,GAAG,CAACkK,yBAApB,EAA+C3O,iBAA/C,CAVsC;QAW1C8F,iBAAiB,EAAE,IAAIxG,WAAJ,CAAgBqF,MAAM,CAACF,GAAG,CAACmK,4BAAL,CAAN,CAAyC5I,QAAzC,EAAhB,EAAqE,CAACzG,UAAU,CAACiG,QAAZ,CAArE,CAXuB;QAY1CS,iBAAiB,EAAE,IAAI3G,WAAJ,CAAgBmF,GAAG,CAACoK,4BAAJ,GAAmClK,MAAM,CAACF,GAAG,CAACoK,4BAAL,CAAN,CAAyC7I,QAAzC,EAAnC,GAAyF,IAAzG,CAZuB;QAa1CG,iBAAiB,EAAE,IAAI7G,WAAJ,CAAgBmF,GAAG,CAACqK,4BAAJ,GAAmCnK,MAAM,CAACF,GAAG,CAACqK,4BAAL,CAAN,CAAyC9I,QAAzC,EAAnC,GAAyF,IAAzG,CAbuB;QAc1CK,iBAAiB,EAAE,IAAI/G,WAAJ,CAAgBmF,GAAG,CAACsK,4BAAJ,GAAmCpK,MAAM,CAACF,GAAG,CAACsK,4BAAL,CAAN,CAAyC/I,QAAzC,EAAnC,GAAyF,IAAzG,CAduB;QAe1CO,WAAW,EAAG,CAAC;UAAEhF,KAAK,EAAEoD,MAAM,CAAC+E,UAAP,CAAkBjF,GAAG,CAACuK,yBAAtB,EAAiDjK,OAAjD,CAAyD,CAAzD,CAAT;UAAsEE,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;QAAnG,CAAD,EAAmHtI,UAAU,CAACiG,QAA9H,CAf4B;QAgB1CgB,cAAc,EAAE,CAAC;UAAEjF,KAAK,EAAEkD,GAAG,CAACwK,qBAAb;UAAoChK,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;QAAjE,CAAD,EAAiF,CAACtI,UAAU,CAACiG,QAAZ,CAAjF,CAhB0B;QAiB1CV,KAAK,EAAE,CAAC;UAAEvD,KAAK,EAAE,CAACoD,MAAM,CAAC+E,UAAP,CAAkBjF,GAAG,CAACwK,qBAAtB,IAA+CtK,MAAM,CAAC+E,UAAP,CAAkBjF,GAAG,CAACuK,yBAAtB,CAAhD,EAAkGjK,OAAlG,CAA0G,CAA1G,CAAT;UAAuHE,QAAQ,EAAE;QAAjI,CAAD,CAjBmC;QAkB1C0B,KAAK,EAAE,CAAC;UAAEpF,KAAK,EAAE,KAAKoM,UAAL,CAAgBlJ,GAAG,CAACyK,mBAApB,CAAT;UAAmDjK,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;QAAhF,CAAD,CAlBmC;QAmB1CjB,oBAAoB,EACpB,CAAC;UAAErF,KAAK,EAAE,KAAKc,aAAL,EAAoB6G,cAApB,EAAoC2E,qBAApC,EAA2DsB,gCAApE;UAAsGlK,QAAQ,EAAE;QAAhH,CAAD,EAAwH1F,UAAU,CAAC0M,SAAX,CAAqB,EAArB,CAAxH,EAAkJ1M,UAAU,CAAC2M,SAAX,CAAqB,EAArB,CAAlJ,CApB0C;QAqB1CrF,UAAU,EAAEpC,GAAG,CAAC2K,gCArB0B;QAsB1CtI,cAAc,EAAE8G,KAtB0B;QAwB1C7G,YAAY,EAAE,IAAIzH,WAAJ,CAAgBmF,GAAG,CAAC4K,0BAApB,EAAgD,CAACtP,2BAAD,EAA8BR,UAAU,CAACmH,GAAX,CAAe,CAAf,CAA9B,EAAiDnH,UAAU,CAACkH,GAAX,CAAe,WAAf,CAAjD,CAAhD,CAxB4B;QAyB1CO,QAAQ,EAAEvC,GAAG,CAAC6K,iBAzB4B;QA0B1CrI,UAAU,EAAExC,GAAG,CAAC8K,0BA1B0B;QA2B1CrI,SAAS,EAAEzC,GAAG,CAACyC,SA3B2B;QA4B1CC,MAAM,EAAE,IAAI7H,WAAJ,CAAgBmF,GAAG,CAAC+K,0BAApB,EAAgD,CAACzP,2BAAD,EAA8BR,UAAU,CAACmH,GAAX,CAAe,CAAf,CAA9B,EAAiDnH,UAAU,CAACkH,GAAX,CAAe,WAAf,CAAjD,CAAhD,CA5BkC;QA6B1CW,SAAS,EAAE3C,GAAG,CAACgL,QA7B2B;QA8B1CpI,SAAS,EAAE,CAAC;UAAE9F,KAAK,EAAE,EAAT;UAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;QAA1C,CAAD,CA9B+B;QA+B1CP,SAAS,EAAE,CAAC;UAAE/F,KAAK,EAAE,EAAT;UAAa0D,QAAQ,EAAE,KAAK5C,aAAL,CAAmBwF;QAA1C,CAAD,CA/B+B;QAgC1CN,OAAO,EAAE9C,GAAG,CAACiL,yBAhC6B;QAiC1ClI,OAAO,EAAE/C,GAAG,CAACkL;MAjC6B,CAA3B,CAAjB;MAmCA,KAAKjN,WAAL,CAAiB+E,IAAjB,CAAsBuE,QAAtB;MACA4B,KAAK,GAAGA,KAAK,GAAG,CAAhB;IACD,CA5CD;;IA6CA,IAAI,KAAKvL,aAAL,CAAmBqF,WAAnB,IAAkC,KAAKrF,aAAL,CAAmBI,UAAzD,EAAsE;MACpE,KAAK,IAAIiJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhJ,WAAL,CAAiBC,QAAjB,CAA0BwF,MAA9C,EAAsDuD,CAAC,EAAvD,EAA2D;QACzD,KAAKjB,aAAL,CAAmBiB,CAAnB;QACA,KAAKhB,4BAAL,CAAkCgB,CAAlC;MAED;;MACD,KAAKkE,kBAAL;IACD;EAEF;;EAEDC,mBAAmB,CAAC3H,KAAD,EAAQ6C,KAAR,EAAa;IAC9B,KAAK+E,cAAL,CAAoB9F,IAApB;IACA,KAAKO,oBAAL,CAA0BrC,KAA1B,EAAiC6C,KAAjC;IACA,KAAKgF,wBAAL,CAA8B7H,KAA9B;EAED;;EACDsC,uBAAuB,CAACtC,KAAD,EAAQ6C,KAAR,EAAa;IAClC,IAAIkD,QAAQ,GAAG,KAAKvL,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCxJ,KAArD;;IACA,IAAI,CAAC,CAAC0M,QAAF,IAAc,CAAC,CAAC,IAAI5M,IAAJ,CAAS4M,QAAT,CAApB,EAAwC;MACtC,IAAI,IAAI5M,IAAJ,CAAS4M,QAAT,EAAmBxB,OAAnB,KAA+B,KAAKuD,OAAL,CAAavD,OAAb,EAAnC,EAA2D;QACzD,KAAK/J,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsClB,SAAtC,CAAgD;UAAE,mBAAmB;QAArB,CAAhD;MACD,CAFD,MAEO;QACL,IAAI,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCG,QAAtC,CAA+C,iBAA/C,CAAJ,EAAuE;UACrE,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCI,MAAtC,CAA6C,iBAA7C,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCK,sBAAtC;QACD;MACF;IACF,CATD,MASO;MACL,IAAI,KAAK1I,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCG,QAAtC,CAA+C,iBAA/C,CAAJ,EAAuE;QACrE,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCI,MAAtC,CAA6C,iBAA7C,CAAP;QACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCK,sBAAtC;MACD;IACF;EACF;;EACD2E,wBAAwB,CAAC7H,KAAD,EAAM;IAAA;;IAC5B,IAAI,CAAC,CAAC,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDvB,KAAxD,EAA+D;MAC7D,IAAI0M,QAAQ,GAAG,IAAI5M,IAAJ,CAAS,KAAKqB,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDvB,KAA3D,CAAf;MACA,MAAM0O,IAAI,GAAGtQ,MAAM,CAAC,KAAKuQ,SAAN,CAAN,CAAuBC,IAAvB,CAA4BxQ,MAAM,CAACsO,QAAD,CAAlC,EAA8C,MAA9C,CAAb;;MACA,IAAIgC,IAAI,GAAG,GAAX,EAAgB;QACdrQ,IAAI,CAACwQ,IAAL,CAAU;UACRC,KAAK,EAAE,OADC;UAER/O,IAAI,EACF,4EAHM;UAIRgP,IAAI,EAAE,SAJE;UAKRC,gBAAgB,EAAE,KALV;UAMRC,iBAAiB,EAAE,KANX;UAORC,cAAc,EAAE,IAPR;UAQRC,cAAc,EAAE,IARR;UASRC,cAAc,EAAE;QATR,CAAV,EAUGC,IAVH;UAAA,6BAUQ,WAAO/D,MAAP,EAAsB;YAC5B,IAAIA,MAAM,CAACtL,KAAP,IAAgB,KAApB,EAA2B;cACzB,KAAI,CAACmB,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDC,QAAlD,CAA2D,IAA3D;YACD;UACF,CAdD;;UAAA;YAAA;UAAA;QAAA;MAeD;IACF;EACF;;EACDwH,oBAAoB,CAACrC,KAAD,EAAQ6C,KAAR,EAAa;IAC/B,IAAIkD,QAAQ,GAAG,KAAKvL,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDvB,KAAjE;IACA,IAAI4M,MAAM,GAAG,KAAKzL,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,eAA/B,EAAgDvB,KAA7D;;IACA,IAAI,CAAC,CAAC0M,QAAF,IAAc,CAAC,CAACE,MAAhB,IAA0B,CAAC,CAAC,IAAI9M,IAAJ,CAAS4M,QAAT,CAA5B,IAAkD,CAAC,CAAC,IAAI5M,IAAJ,CAAS8M,MAAT,CAAxD,EAA0E;MACxE,IAAI,IAAI9M,IAAJ,CAAS4M,QAAT,EAAmBxB,OAAnB,KAA+B,IAAIpL,IAAJ,CAAS8M,MAAT,EAAiB1B,OAAjB,EAAnC,EAA+D;QAC7D,KAAK/J,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsClB,SAAtC,CAAgD;UAAE,eAAe;QAAjB,CAAhD;MACD,CAFD,MAEO;QACL,IAAI,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDoI,QAAlD,CAA2D,aAA3D,CAAJ,EAA+E;UAC7E,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCI,MAAtC,CAA6C,aAA7C,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCK,sBAAtC;QACD;;QACD,IAAI,KAAK1I,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,eAA/B,EAAgDoI,QAAhD,CAAyD,aAAzD,CAAJ,EAA6E;UAC3E,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCI,MAAtC,CAA6C,aAA7C,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCK,sBAAtC;QACD;;QACD,IAAI,KAAK1I,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCG,QAAtC,CAA+C,UAA/C,CAAJ,EAAgE;UAC9D,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCI,MAAtC,CAA6C,UAA7C,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCK,sBAAtC;QACD;MACF;IACF,CAjBD,MAiBO;MACL,IAAI,KAAK1I,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCG,QAAtC,CAA+C,qBAA/C,CAAJ,EAA2E;QACzE,IAAI,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCG,QAAtC,CAA+C,aAA/C,CAAJ,EAAmE;UACjE,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCI,MAAtC,CAA6C,aAA7C,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsCK,sBAAtC;QACD;MACF,CALD,MAKO;QACL,KAAK1I,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+BiI,KAA/B,EAAsClB,SAAtC,CAAgD,IAAhD;MACD;;MACD,IAAI,CAAE,CAAC,CAACoE,QAAR,EAAmB;QACjB,KAAKvL,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkD+G,SAAlD,CAA4D;UAAE,YAAY;QAAd,CAA5D;;QACA,IAAI,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,eAA/B,EAAgDoI,QAAhD,CAAyD,aAAzD,CAAJ,EAA6E;UAC3E,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,eAA/B,EAAgDqI,MAAhD,CAAuD,aAAvD,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,eAA/B,EAAgDsI,sBAAhD;QACD;MACF;;MACD,IAAI,CAAE,CAAC,CAAC+C,MAAR,EAAiB;QACf,KAAKzL,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,eAA/B,EAAgD+G,SAAhD,CAA0D;UAAE,YAAY;QAAd,CAA1D;;QACA,IAAI,KAAKnH,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDoI,QAAlD,CAA2D,aAA3D,CAAJ,EAA+E;UAC7E,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDqI,MAAlD,CAAyD,aAAzD,CAAP;UACA,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,iBAA/B,EAAkDsI,sBAAlD;QACD;MACF;IACF;;IACD,KAAKZ,uBAAL,CAA6BtC,KAA7B,EAAoC6C,KAApC;IACA,KAAK8F,QAAL;EACD;;EACDC,gBAAgB,CAACjO,OAAD,EAAqB;IACnC,IAAIkO,OAAO,GAAI,CAAC,CAAClO,OAAO,CAACtB,KAAV,IAAmBsB,OAAO,CAACtB,KAAR,CAAcyP,IAAd,GAAqB7I,MAArB,IAA+B,EAAnD,IAA0DtF,OAAO,CAACtB,KAAR,IAAiB,IAA3E,IAAmFsB,OAAO,CAACtB,KAAR,IAAiB,EAAlH;;IACA,IAAI,CAAC,CAACsB,OAAO,CAACtB,KAAd,EAAqB;MACnBwP,OAAO,GAAGA,OAAO,IAAIE,MAAM,CAACpO,OAAO,CAACtB,KAAT,CAAN,CAAsB2P,KAAtB,CAA4B,QAA5B,EAAsC/I,MAAtC,IAAgDtF,OAAO,CAACtB,KAAR,CAAc4G,MAAnF;IACD;;IACD,OAAO4I,OAAO,GAAG,IAAH,GAAU;MAAE,aAAa;IAAf,CAAxB;EACD;;EACDtG,aAAa,CAACvC,KAAD,EAAM;IACjB,IAAI,CAAC,CAAC,KAAKoD,aAAL,CAAmBpD,KAAnB,EAA0BvF,QAA1B,CAAmC,SAAnC,EAA8CpB,KAAhD,IAAyD,KAAK+J,aAAL,CAAmBpD,KAAnB,EAA0BvF,QAA1B,CAAmC,SAAnC,EAA8CpB,KAA9C,CAAoD4P,WAApD,MAAqE,IAAlI,EAAwI;MACtI,KAAK7F,aAAL,CAAmBpD,KAAnB,EAA0BvF,QAA1B,CAAmC,SAAnC,EAA8CqI,aAA9C,CAA4D,KAAK8F,gBAAjE;;MACA,IAAI,CAAC,KAAKM,cAAL,CAAoB,KAAK9F,aAAL,CAAmBpD,KAAnB,EAA0BvF,QAA1B,CAAmC,SAAnC,EAA8CpB,KAAlE,CAAL,EAA+E;QAC7E,KAAK+J,aAAL,CAAmBpD,KAAnB,EAA0BvF,QAA1B,CAAmC,SAAnC,EAA8CkH,SAA9C,CAAwD;UAAE,aAAa;QAAf,CAAxD;MACD;IACF,CALD,MAKO;MACL,KAAKyB,aAAL,CAAmBpD,KAAnB,EAA0BvF,QAA1B,CAAmC,SAAnC,EAA8CsI,gBAA9C,CAA+D,KAAK6F,gBAApE;;MACA,IAAI,KAAKpO,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0CoI,QAA1C,CAAmD,WAAnD,CAAJ,EAAqE;QACnE,OAAO,KAAKxI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0CqI,MAA1C,CAAiD,WAAjD,CAAP;MACD;IACF;;IACD,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2BpF,GAA3B,CAA+B,SAA/B,EAA0CsI,sBAA1C;EACD;;EACDgG,cAAc,CAAC7P,KAAD,EAAM;IAClB,IAAIwP,OAAO,GAAI,CAAC,CAACxP,KAAF,IAAWA,KAAK,CAACyP,IAAN,GAAa7I,MAAb,IAAuB,EAAnC,IAA0C5G,KAAK,IAAI,IAAnD,IAA2DA,KAAK,IAAI,EAAlF;;IACA,IAAI,CAAC,CAACA,KAAN,EAAa;MACXwP,OAAO,GAAGA,OAAO,IAAIE,MAAM,CAAC1P,KAAD,CAAN,CAAc2P,KAAd,CAAoB,QAApB,EAA8B/I,MAA9B,IAAwC5G,KAAK,CAAC4G,MAAnE;IACD;;IACD,OAAO4I,OAAP;EACD;;EAODM,eAAe,CAACzG,CAAD,EAAE;IAEf,IAAIA,CAAC,CAACyB,MAAF,CAASiF,OAAb,EAAsB;MACpB,KAAKC,cAAL,GAAsB,IAAtB;;MACA,KAAK,IAAIxI,IAAT,IAAiB,KAAKrG,WAAL,CAAiBC,QAAlC,EAA4C;QAC1C,KAAKqC,eAAL,CAAqByC,IAArB,CAA0B,IAAIpG,IAAJ,CAAS0H,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BvB,KAArC,EAA4CyE,QAA5C,EAA1B;QACA,KAAKd,aAAL,CAAmBuC,IAAnB,CAAwB,IAAIpG,IAAJ,CAAS0H,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0BvB,KAAnC,EAA0CyE,QAA1C,EAAxB,EAF0C,CAI3C;QACA;MAEA,CATmB,CAUpB;;IACD,CAXD,MAWO;MACL,KAAKuL,cAAL,GAAsB,KAAtB;;MACA,KAAK,IAAIxI,IAAT,IAAiB,KAAKrG,WAAL,CAAiBC,QAAlC,EAA4C,CAC3C;QACA;MAEA;IACF;EACF;;EACDkO,QAAQ;IACN,KAAK7L,eAAL,GAAuB,EAAvB;IACA,KAAKE,aAAL,GAAqB,EAArB;;IACA,KAAK,IAAI6D,IAAT,IAAiB,KAAKrG,WAAL,CAAiBC,QAAlC,EAA4C;MAC1C,KAAKqC,eAAL,CAAqByC,IAArB,CAA0B,IAAIpG,IAAJ,CAAS0H,IAAI,CAACjG,GAAL,CAAS,iBAAT,EAA4BvB,KAArC,EAA4CyE,QAA5C,EAA1B;MACA,KAAKd,aAAL,CAAmBuC,IAAnB,CAAwB,IAAIpG,IAAJ,CAAS0H,IAAI,CAACjG,GAAL,CAAS,eAAT,EAA0BvB,KAAnC,EAA0CyE,QAA1C,EAAxB;IAGD;;IACD,IAAIU,GAAG,GAAG,KAAK1B,eAAL,CAAqB,CAArB,CAAV;IACA,IAAIyB,GAAG,GAAG,KAAKzB,eAAL,CAAqB,CAArB,CAAV;IACA,KAAKA,eAAL,CAAqBpC,OAArB,CAA6B,UAAU4O,CAAV,EAAW;MACtC/K,GAAG,GAAG,IAAIpF,IAAJ,CAASmQ,CAAT,IAAc,IAAInQ,IAAJ,CAASoF,GAAT,CAAd,GAA8B+K,CAA9B,GAAkC/K,GAAxC;MAEAC,GAAG,GAAG,IAAIrF,IAAJ,CAASmQ,CAAT,IAAc,IAAInQ,IAAJ,CAASqF,GAAT,CAAd,GAA8B8K,CAA9B,GAAkC9K,GAAxC;IACD,CAJD;IAKA,KAAK+K,kBAAL,GAA0B,IAAIpQ,IAAJ,CAASqF,GAAT,EAAcV,QAAd,EAA1B;IACA,KAAK0L,gBAAL,GAAwB,IAAIrQ,IAAJ,CAASoF,GAAT,EAAcT,QAAd,EAAxB;EACD;;EACD2L,gBAAgB,CAACC,IAAD,EAAe7I,IAAf,EAAmB;IACjC6I,IAAI,GAAGA,IAAI,CAACC,iBAAL,EAAP;IACA,OAAO9I,IAAI,CAAC,WAAD,CAAJ,CAAkB8I,iBAAlB,GAAsCC,OAAtC,CAA8CF,IAA9C,IAAsD,CAAC,CAAvD,IAA4D7I,IAAI,CAAC,aAAD,CAAJ,CAAoB8I,iBAApB,GAAwCC,OAAxC,CAAgDF,IAAhD,IAAwD,CAAC,CAA5H;EAED;;EAED3H,eAAe;IAEb,KAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhJ,WAAL,CAAiByF,MAArC,EAA6CuD,CAAC,EAA9C,EAAkD;MAEhD,KAAK,IAAIqG,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;QAC3B,IAAI,KAAKrP,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmD5G,MAAnD,EAA2D6G,SAA/D,EAA0E;UACxE,KAAKtP,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmDlI,SAAnD,CAA6D,IAA7D;QACD;MACF;IACF;;IACD,KAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhJ,WAAL,CAAiByF,MAArC,EAA6CuD,CAAC,EAA9C,EAAkD;MAChD,KAAK,IAAIuG,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,KAAKvP,WAAL,CAAiByF,MAAjB,GAA0B,CAA/C,EAAkD8J,CAAC,EAAnD,EAAuD;QACrD,IAAI,KAAKvP,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,KAA3B,EAAkCvB,KAAlC,KAA4C,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoB6J,CAApB,EAAuBnP,GAAvB,CAA2B,KAA3B,EAAkCvB,KAAlF,EAAyF;UACvF,KAAK,IAAI2Q,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;YAC3B,KAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;cAC3B,IAAIrG,CAAC,IAAIuG,CAAL,IAAU,KAAKvP,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBoP,CAAhD,EAAmD3Q,KAA7D,IAAsE,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoB6J,CAApB,EAAuBnP,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmDxQ,KAAzH,IAAkI,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBoP,CAAhD,EAAmD3Q,KAAnD,KAA6D,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoB6J,CAApB,EAAuBnP,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmDxQ,KAAlP,IAA2P,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,KAA3B,EAAkCvB,KAAlC,KAA4C,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoB6J,CAApB,EAAuBnP,GAAvB,CAA2B,KAA3B,EAAkCvB,KAA7U,EAAoV;gBAClV,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBoP,CAAhD,EAAmDrI,SAAnD,CAA6D;kBAAEmI,SAAS,EAAE;gBAAb,CAA7D;gBACA,KAAKtP,WAAL,CAAiB0F,EAAjB,CAAoB6J,CAApB,EAAuBnP,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmDlI,SAAnD,CAA6D;kBAAEmI,SAAS,EAAE;gBAAb,CAA7D;gBACI,KAAKtP,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAoBoP,CAA/C,EAAkDC,aAAlD;gBACL,KAAKzP,WAAL,CAAiB0F,EAAjB,CAAoB6J,CAApB,EAAuBnP,GAAvB,CAA2B,qBAAoBiP,CAA/C,EAAkDI,aAAlD;cACA;YACF;UACF;QACF;MACF;IACF;;IACD,KAAK,IAAIzG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhJ,WAAL,CAAiByF,MAArC,EAA6CuD,CAAC,EAA9C,EAAkD;MAChD,KAAK,IAAIwG,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;QAC3B,KAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,CAArB,EAAwBA,CAAC,EAAzB,EAA6B;UAC3B,IAAIG,CAAC,IAAIH,CAAL,IAAU,KAAKrP,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBoP,CAAhD,EAAmD3Q,KAA7D,IAAsE,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmDxQ,KAAzH,IAAkI,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBoP,CAAhD,EAAmD3Q,KAAnD,KAA6D,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmDxQ,KAAtP,EAA6P;YAC3P,IAAI,CAAC,KAAKmB,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmD5G,MAAxD,EAAgE;cAC9D,KAAKzI,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAqBiP,CAAhD,EAAmDlI,SAAnD,CAA6D;gBAAEmI,SAAS,EAAE;cAAb,CAA7D;cACA,KAAKtP,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,qBAAoB4I,CAA/C,EAAkDyG,aAAlD;YAED;UACF;QACF;MACF;IACF;EACF;;EACDhF,kBAAkB;IAChB,IAAIiF,YAAY,GAAG,CAAnB;;IACA,KAAK,IAAIlK,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAK7F,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoF3F,MAAhH,EAAwHD,KAAK,EAA7H,EAAiI;MAC/H,IAAI,KAAKqB,QAAL,CAAcT,MAAd,CAAqBuJ,CAAC,IAAIA,CAAC,CAAC/M,GAAF,IAAS,KAAKjD,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoF5F,KAApF,EAA2FqG,oBAA9H,EAAoJpG,MAApJ,GAA6J,CAAjK,EAAoK;QAClKiK,YAAY,GAAGA,YAAY,GAAG,CAA9B;MACD,CAFD,MAGK;QACH,KAAKpR,UAAL,CAAgBsR,mBAAhB,CAAoC,KAAKjQ,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoF5F,KAApF,EAA2FqG,oBAA/H,EAAqJ,KAAKlM,aAAL,CAAmB6G,cAAnB,CAAkC4C,YAAvL,EAAqM,EAArM,EAAyMnK,SAAzM,CAAoNC,GAAD,IAAsB;UACvOwQ,YAAY,GAAGA,YAAY,GAAG,CAA9B;UACAxQ,GAAG,CAACgB,OAAJ,CAAYgH,OAAO,IAAG;YACpB,KAAKsD,QAAL,CAAczF,IAAd,CAAmBmC,OAAnB;UACD,CAFD;UAGA,KAAK2I,iBAAL,CAAuBH,YAAvB;QACD,CAND;MAOD;IACF;;IACD,KAAKG,iBAAL,CAAuBH,YAAvB;EACD;;EACDG,iBAAiB,CAACH,YAAD,EAAqB;IACpC,IAAII,UAAU,GAAG,KAAKnQ,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoF3F,MAArG;;IACA,IAAIqK,UAAU,KAAKJ,YAAnB,EAAiC;MAC/B,KAAKtK,oBAAL;IACD;EACF;;EAED8H,kBAAkB;IAEhB,IAAI6C,OAAO,GAAyB,EAApC;;IACA,KAAK,IAAI/G,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,KAAKrJ,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoF3F,MAApF,GAA6F,CAAlH,EAAqHuD,CAAC,EAAtH,EAA0H;MACxH,IAAIgH,UAAU,GAAuB;QAAEpN,GAAG,EAAE,EAAP;QAAWqN,GAAG,EAAE;MAAhB,CAArC;MACA,IAAI5E,IAAI,GAAG,KAAK1L,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoFpC,CAApF,EAAuFsC,gBAAlG;MACA,IAAIC,QAAQ,GAAGF,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,CAAlB,IAAuB,GAAvB,GAA6BH,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,CAAlB,CAA7B,GAAoD,GAApD,GAA0DH,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,CAAlB,CAAzE;MACAwE,UAAU,CAACpN,GAAX,GAAiB,KAAKjD,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoFpC,CAApF,EAAuF6C,oBAAxG;MACAmE,UAAU,CAACC,GAAX,GAAiB1E,QAAjB;MACAwE,OAAO,CAAChL,IAAR,CAAaiL,UAAb;IACD;;IAGD,KAAK1R,UAAL,CAAgB4R,aAAhB,CAA8BH,OAA9B,EAAuC9Q,SAAvC,CAAkDC,GAAD,IAAsB;MACrE,KAAK,IAAI8J,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,KAAKrJ,aAAL,CAAmB6G,cAAnB,CAAkC2E,qBAAlC,CAAwDC,2BAAxD,CAAoF3F,MAApF,GAA6F,CAAlH,EAAqHuD,CAAC,EAAtH,EAA0H;QACxH,IAAI9J,GAAG,CAACkH,MAAJ,CAAWuJ,CAAC,IAAIA,CAAC,CAAC/M,GAAF,KAAU,KAAK5C,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,KAA3B,EAAkCvB,KAA5D,EAAmE4G,MAAnE,KAA8E,CAAlF,EAAqF;UACnF,KAAKzF,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,KAA3B,EAAkC+G,SAAlC,CAA4C;YAAEC,UAAU,EAAE;UAAd,CAA5C;UACAlI,GAAG,CAAC8J,CAAD,CAAH,CAAOpG,GAAP,GAAa,KAAK5C,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,KAA3B,EAAkCvB,KAA/C;UACA,KAAK2L,QAAL,CAAczF,IAAd,CAAmB7F,GAAG,CAAC8J,CAAD,CAAtB;UACA,KAAK/B,cAAL,CAAoBlC,IAApB,CAAyB,KAAK/E,WAAL,CAAiB0F,EAAjB,CAAoBsD,CAApB,EAAuB5I,GAAvB,CAA2B,KAA3B,EAAkCvB,KAA3D;QACD;MACF;;MACD,IAAI,KAAKmB,WAAL,CAAiB8F,OAArB,EAA8B;QAC5BhJ,uBAAuB,CAACiJ,iBAAxB,CAA0C,KAAKtF,eAA/C;QACA;MACD;IACF,CAbD;EAcD;;EAGD0P,cAAc;IACZ,IAAIC,OAAJ;;IACA,IAAI,KAAKzQ,aAAL,CAAmBqF,WAAvB,EAAoC;MAClC,IAAI,KAAKhF,WAAL,CAAiBC,QAAjB,CAA0BwF,MAA1B,GAAmC,CAAvC,EAA0C;QACxC,IAAI,CAAC,CAAC,KAAKzF,WAAL,CAAiBC,QAAjB,CAA0B,CAA1B,EAA6BpB,KAA7B,CAAmCyD,eAAnC,EAAoD+N,EAA1D,EAA8D;UAC5DD,OAAO,GAAG;YACRE,WAAW,EAAE,KAAK3Q,aAAL,CAAmB6G,cAAnB,CAAkC+J,WADvC;YAERN,GAAG,EAAE,KAAKjQ,WAAL,CAAiBC,QAAjB,CAA0B,CAA1B,EAA6BpB,KAA7B,CAAmCyD,eAAnC,CAAmD+N;UAFhD,CAAV;QAID,CALD,MAMI;UACFD,OAAO,GAAE;YACPE,WAAW,EAAE,KAAK3Q,aAAL,CAAmB6G,cAAnB,CAAkC+J,WADxC;YAEPN,GAAG,EAAE,KAAKjQ,WAAL,CAAiBC,QAAjB,CAA0B,CAA1B,EAA6BpB,KAA7B,CAAmCyD;UAFjC,CAAT;QAID;MACF;IACF,CAfD,MAgBK,IAAG,KAAK3C,aAAL,CAAmBI,UAAtB,EAAiC;MACpCqQ,OAAO,GAAG;QACRE,WAAW,EAAE,KAAK3Q,aAAL,CAAmBwJ,aAAnB,CAAiCqH,YADtC;QAERP,GAAG,EAAE,KAAKtQ,aAAL,CAAmBwJ,aAAnB,CAAiC1H;MAF9B,CAAV;IAID;;IACC,KAAKnD,UAAL,CAAgBmS,iBAAhB,CAAkCL,OAAlC,EAA2CnR,SAA3C,CAAsDyR,IAAD,IAAc;MACjE,IAAIC,OAAO,GAAQ,EAAnB;;MACA,IAAID,IAAI,CAACE,UAAL,IAAmB,GAAnB,IAA0B,CAACF,IAAI,CAAChJ,OAAL,IAAgB,EAAjB,EAAqBjC,MAArB,GAA8B,CAA5D,EAA+D;QAC7DkL,OAAO,GAAGD,IAAI,CAAChJ,OAAf;QACAiJ,OAAO,GAAGA,OAAO,CAAC7O,GAAR,CAAYc,GAAG,KAAK,EAC5B,GAAGA,GADyB;UAE5BiO,UAAU,EAAE;QAFgB,CAAL,CAAf,CAAV;MAID;;MACD,MAAMC,oBAAoB,GAAU,KAAK9Q,WAAL,CAAiBnB,KAArD;MACAiS,oBAAoB,EAAE5Q,OAAtB,CAA+BF,WAAD,IAAqB;QACjD2Q,OAAO,CAACvK,MAAR,CAAe2K,OAAO,IAAIA,OAAO,CAACnO,GAAR,KAAgB5C,WAAW,CAAC4C,GAAtD,EAA2D1C,OAA3D,CAAoE0C,GAAG,IAAG;UACxEA,GAAG,CAACiO,UAAJ,GAAiB,IAAjB;QACD,CAFD;MAGD,CAJD;MAKA,KAAKG,kBAAL,GAAyBL,OAAzB;MACA,IAAIM,SAAS,GAAG,KAAK1S,MAAL,CAAY2S,IAAZ,CAAiB9T,uBAAjB,EAA0C;QACxD+T,MAAM,EAAE,OADgD;QAExDC,KAAK,EAAE,QAFiD;QAGxDC,SAAS,EAAE,KAH6C;QAIxDC,YAAY,EAAE,KAJ0C;QAKxDC,SAAS,EAAE,MAL6C;QAMxDC,UAAU,EAAE,0BAN4C;QAOxD3H,IAAI,EAAE;UACJ8G,OAAO,EAAEA;QADL;MAPkD,CAA1C,CAAhB;MAWAM,SAAS,CAACQ,WAAV,GAAwBxS,SAAxB,CAAmC4K,IAAD,IAAc;QAC9C,IAAI,CAAC,CAACA,IAAF,IAAU,CAACA,IAAI,CAAC6H,gBAAL,IAAyB,EAA1B,EAA8BjM,MAA9B,GAAuC,CAArD,EAAwD;UACtD,KAAKkM,6BAAL,CAAmC9H,IAAI,CAAC6H,gBAAxC;QACD,CAFD,CAGA;QAHA,KAIK,IAAI,CAAC,CAAC7H,IAAF,IAAU,CAACA,IAAI,CAAC6H,gBAAL,IAAyB,EAA1B,EAA8BjM,MAA9B,IAAwC,CAAtD,EAAyD;UAC5D,IAAI,CAAC,CAAC,KAAKzF,WAAL,CAAiBC,QAAnB,IAA+B,KAAKD,WAAL,CAAiBC,QAAjB,CAA0BwF,MAA1B,GAAmC,CAAtE,EAAyE;YAEvE,MAAMmM,yCAAyC,GAAU,KAAK5R,WAAL,CAAiBnB,KAAjB,CAAuBuH,MAAvB,CAA8ByL,eAAe,IACpG,KAAKb,kBAAL,CAAwBc,IAAxB,CAA6BC,UAAU,IAAIF,eAAe,CAACjP,GAAhB,KAAwBmP,UAAU,CAACnP,GAA9E,CADuD,CAAzD;;YAGE,IAAG,CAAC,CAACgP,yCAAF,IAA+CA,yCAAyC,CAACnM,MAA1C,GAAiD,CAAnG,EAAqG;cAEnGmM,yCAAyC,CAAC1R,OAA1C,CAAkDmG,IAAI,IAAG;gBACvD,MAAMb,KAAK,GAAG,KAAKxF,WAAL,CAAiBC,QAAjB,CAA0B+R,SAA1B,CAAoC7R,OAAO,IAAIA,OAAO,CAACtB,KAAR,CAAc+D,GAAd,KAAsByD,IAAI,CAACzD,GAA1E,CAAd;;gBAEA,IAAG4C,KAAK,IAAG,CAAX,EAAa;kBACf,IAAIxD,OAAO,GAAI,KAAKiQ,uBAAL,EAAf;kBACA,IAAI/L,SAAS,GAAG,KAAKC,aAAL,CAAmBC,MAAnB,CAA0BC,IAAI,IAAIA,IAAI,CAACC,IAAL,IAAaD,IAAI,CAACzD,GAApD,EAAyD,CAAzD,GAA6D2D,MAA7E;;kBACA,IAAG,CAAC,CAACL,SAAL,EAAe;oBACblE,OAAO,GAAGkE,SAAV;kBACD;;kBACG,KAAKlG,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2B/F,UAA3B,CAAsC;oBACpCmD,GAAG,EAAE,IAD+B;oBAEpCiB,WAAW,EAAG5B,MAAM,CAAC+E,UAAP,CAAkBhF,OAAlB,EAA2BK,OAA3B,CAAmC,CAAnC;kBAFsB,CAAtC;gBAID,CAVD,MAWI;kBACF,KAAKrC,WAAL,CAAiB0J,QAAjB,CAA0BlE,KAA1B;gBACD;cACF,CAjBD;YAkBD,CAzBoE,CA0BvE;YACA;YACA;YACA;YACA;YACA;YACA;YACA;;UACD;QACF;MACF,CA1CD;IA2CD,CAtED;EAuEH;;EAEDmM,6BAA6B,CAACD,gBAAD,EAAwB;IACnD,IAAIQ,2BAAJ;;IACA,IAAI,CAAC,CAAC,KAAKlS,WAAL,CAAiBC,QAAnB,IAA+B,KAAKD,WAAL,CAAiBC,QAAjB,CAA0BwF,MAA1B,GAAmC,CAAtE,EAAyE;MACvEyM,2BAA2B,GAAG,KAAKlS,WAAL,CAAiBC,QAAjB,CAA0B,CAA1B,CAA9B,CADuE,CACV;MAC7D;;MACA,IAAIyR,gBAAgB,CAACjM,MAAjB,GAA0B,CAA1B,IAA+B,KAAKzF,WAAL,CAAiBC,QAAjB,CAA0BwF,MAA1B,GAAmC,CAAtE,EAAyE;QACvE,MAAM0M,wCAAwC,GAAU,KAAKnS,WAAL,CAAiBnB,KAAjB,CAAuBuH,MAAvB,CAA8ByL,eAAe,IACnG,CAACH,gBAAgB,CAACI,IAAjB,CAAsBC,UAAU,IAAIF,eAAe,CAACjP,GAAhB,KAAwBmP,UAAU,CAACnP,GAAvE,CADqD,CAAxD,CADuE,CAIvE;;QACAuP,wCAAwC,CAACjS,OAAzC,CAAiDmG,IAAI,IAAG;UACtD,IAAI,CAAC,CAACA,IAAI,CAACzD,GAAX,EAAgB;YACd,MAAMwP,iBAAiB,GAAG,KAAKpB,kBAAL,CAAwBlK,IAAxB,CAA6BlE,GAAG,IAAIA,GAAG,CAACA,GAAJ,IAAWyD,IAAI,CAACzD,GAApD,CAA1B,CADc,CAEd;;YACA,MAAM4C,KAAK,GAAG,KAAKxF,WAAL,CAAiBC,QAAjB,CAA0B+R,SAA1B,CAAoC7R,OAAO,IAAIA,OAAO,CAACtB,KAAR,CAAc+D,GAAd,KAAsByD,IAAI,CAACzD,GAA1E,CAAd,CAHc,CAId;;YACA,IAAI4C,KAAK,KAAK,CAAC,CAAX,IAAgB,CAAC,CAAC4M,iBAAtB,EAAyC;cACvC,IAAIpQ,OAAO,GAAI,KAAKiQ,uBAAL,EAAf;cACA,IAAI/L,SAAS,GAAG,KAAKC,aAAL,CAAmBC,MAAnB,CAA0BC,IAAI,IAAIA,IAAI,CAACC,IAAL,IAAaD,IAAI,CAACxD,OAApD,EAA6D,CAA7D,GAAiE0D,MAAjF;;cACA,IAAG,CAAC,CAACL,SAAL,EAAe;gBACblE,OAAO,GAAGkE,SAAV;cACD;;cACD,IAAIV,KAAK,IAAI,CAAb,EAAgB;gBACd,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2B/F,UAA3B,CAAsC;kBACpCmD,GAAG,EAAE,IAD+B;kBAEpCiB,WAAW,EAAE5B,MAAM,CAAC+E,UAAP,CAAkBhF,OAAlB,EAA2BK,OAA3B,CAAmC,CAAnC;gBAFuB,CAAtC;cAID,CALD,MAMK;gBACH,KAAKrC,WAAL,CAAiB0J,QAAjB,CAA0BlE,KAA1B;cACD;YACF;UACF;QACF,CAvBD;MAwBD;;MACD,IAAIsL,oBAAoB,GAAG,KAAK9Q,WAAL,CAAiBnB,KAA5C,CAjCuE,CAkCvE;;MACA6S,gBAAgB,GAAGA,gBAAgB,CAACtL,MAAjB,CAAwBiM,YAAY,IACrD,CAACvB,oBAAoB,CAACgB,IAArB,CAA0BQ,YAAY,IAAID,YAAY,CAACzP,GAAb,KAAqB0P,YAAY,CAAC1P,GAA5E,CADgB,CAAnB,CAnCuE,CAsCvE;;MACC,IAAI8O,gBAAgB,CAACjM,MAAjB,IAA2B,CAA3B,IAAgCqL,oBAAoB,CAACrL,MAArB,IAA+B,CAAnE,EAAsE;QAErE;QACA,IAAIqL,oBAAoB,CAACrL,MAArB,IAA+B,CAA/B,IAAoC,CAACqL,oBAAoB,CAAC,CAAD,CAApB,CAAwBlO,GAAjE,EAAsE;UACpE,KAAK5C,WAAL,CAAiBC,QAAjB,CAA0B,CAA1B,EAA6BR,UAA7B,CAAwC;YACtCmD,GAAG,EAAE8O,gBAAgB,CAAC,CAAD,CAAhB,CAAoB9O,GADa;YAEtCG,IAAI,EAAC2O,gBAAgB,CAAC,CAAD,CAAhB,CAAoBa;UAFa,CAAxC;UAIA,KAAKvM,eAAL,CAAqB0L,gBAAgB,CAAC,CAAD,CAAhB,CAAoB9O,GAAzC,EAA8C,CAA9C;UACA8O,gBAAgB,CAACc,KAAjB,GANoE,CAM1C;QAC3B;MACF;IACF,CArDkD,CAuDnD;;;IACAd,gBAAgB,CAACxR,OAAjB,CAA0BuS,cAAD,IAAwB;MAC/C,KAAKC,+BAAL,CAAqCD,cAArC,EAAqDP,2BAArD;IACD,CAFD;EAGD;;EAEDQ,+BAA+B,CAACD,cAAD,EAAqBP,2BAArB,EAAoD;IACjF,IAAIzP,iBAAJ;IACA,IAAIkQ,sBAAJ;;IACA,IAAI,CAAC,CAACT,2BAAN,EAAmC;MACjCzP,iBAAiB,GAAGyP,2BAA2B,CAACrT,KAA5B,CAAkC4D,iBAAtD;MACAkQ,sBAAsB,GAAGT,2BAA2B,CAACU,WAA5B,EAAzB;IACD;;IACD,IAAG,CAAC,CAACH,cAAc,CAAC7P,GAApB,EAAwB;MACtB;MACA,MAAM4C,KAAK,GAAG,KAAKxF,WAAL,CAAiBC,QAAjB,CAA0B+R,SAA1B,CAAoC7R,OAAO,IAAIA,OAAO,CAACtB,KAAR,CAAc+D,GAAd,KAAsB,IAArE,CAAd;;MACA,IAAI4C,KAAK,KAAK,CAAC,CAAf,EAAkB;QAChB,KAAKxF,WAAL,CAAiB0F,EAAjB,CAAoBF,KAApB,EAA2B/F,UAA3B,CAAsC;UACpCmD,GAAG,EAAE6P,cAAc,CAAC7P,GADgB;UAEpCG,IAAI,EAAC0P,cAAc,CAACF;QAFgB,CAAtC;QAIA,KAAKvM,eAAL,CAAqByM,cAAc,CAAC7P,GAApC,EAAyC4C,KAAzC;MACH,CANC,MAOE;QACF,IAAI3B,WAAW,GAAG,KAAKoO,uBAAL,EAAlB;QACA,IAAK7P,KAAK,GAAGyB,WAAb;QACA,IAAIqC,SAAS,GAAG,KAAKC,aAAL,CAAmBC,MAAnB,CAA0BC,IAAI,IAAIA,IAAI,CAACC,IAAL,IAAamM,cAAc,CAAC7P,GAA9D,EAAmE,CAAnE,GAAuE2D,MAAvF;;QACA,IAAG,CAAC,CAACL,SAAL,EAAe;UACVrC,WAAW,GAAGqC,SAAd;UACA9D,KAAK,GAAG,CAAC8D,SAAS,GAAG,CAAb,EAAgB7D,OAAhB,CAAwB,CAAxB,CAAR;QACJ;;QAED,MAAMZ,OAAO,GAAM,KAAK9B,aAAL,EAAoBI,UAApB,GAAiC,IAAIpB,IAAJ,CAAS,KAAKgB,aAAL,EAAoBwJ,aAApB,CAAkC1H,OAA3C,CAAjC,GAAuF,IAAI9C,IAAJ,CAAS,KAAKgB,aAAL,EAAoB6G,cAApB,CAAmC4C,YAA5C,CAA1G;QACA,MAAMzH,KAAK,GAAG,KAAKhC,aAAL,EAAoBI,UAApB,GAAiC,IAAIpB,IAAJ,CAAS,KAAKgB,aAAL,EAAoBwJ,aAApB,CAAkCxH,KAA3C,CAAjC,GAAqF,IAAIhD,IAAJ,CAAS,KAAKgB,aAAL,EAAoB6G,cAApB,CAAmC6C,UAA5C,CAAnG;QAEA,MAAMC,QAAQ,GAAG,KAAKnL,eAAL,CAAqBuC,KAArB,CAA2B;UAC1C4B,eAAe,EAAE,IAAI1F,WAAJ,CAAgB;YAACiC,KAAK,EAAC4C,OAAP;YAAgBc,QAAQ,EAAE;UAA1B,CAAhB,EAAiD,CAAC1F,UAAU,CAACiG,QAAZ,CAAjD,CADyB;UAE1CN,aAAa,EAAE,IAAI5F,WAAJ,CAAgB;YAACiC,KAAK,EAAC8C,KAAP;YAAaY,QAAQ,EAAE;UAAvB,CAAhB,EAA8C,CAAC1F,UAAU,CAACiG,QAAZ,CAA9C,CAF2B;UAG1CL,iBAAiB,EAAE,IAAI7F,WAAJ,CAAgB,CAAC,CAAC6F,iBAAF,GAAsBA,iBAAtB,GAA0C,IAA1D,EAAgE5F,UAAU,CAACiG,QAA3E,CAHuB;UAI1CJ,GAAG,EAAE,IAAI9F,WAAJ,CAAgB,IAAhB,CAJqC;UAK1CmG,IAAI,EAAE,IAAInG,WAAJ,CAAgB6V,cAAc,CAACF,gBAA/B,CALoC;UAM1C3P,GAAG,EAAE,IAAIhG,WAAJ,CAAgB6V,cAAc,CAAC7P,GAA/B,EAAoC,CAAC/F,UAAU,CAACiG,QAAZ,CAApC,CANqC;UAO1CE,EAAE,EAAE,CAAC;YAAEnE,KAAK,EAAE,EAAT;YAAa0D,QAAQ,EAAE;UAAvB,CAAD,EAAiCjF,iBAAjC,CAPsC;UAQ1C2F,EAAE,EAAE,CAAC;YAAEpE,KAAK,EAAE,EAAT;YAAa0D,QAAQ,EAAE;UAAvB,CAAD,EAAiCjF,iBAAjC,CARsC;UAS1C4F,EAAE,EAAE,CAAC;YAAErE,KAAK,EAAE,EAAT;YAAa0D,QAAQ,EAAE;UAAvB,CAAD,EAAiCjF,iBAAjC,CATsC;UAU1C6F,EAAE,EAAE,CAAC;YAAEtE,KAAK,EAAE,EAAT;YAAa0D,QAAQ,EAAE;UAAvB,CAAD,EAAiCjF,iBAAjC,CAVsC;UAW1C8F,iBAAiB,EAAE,IAAIxG,WAAJ,CAAgB,GAAhB,EAAqB,CAACC,UAAU,CAACiG,QAAZ,CAArB,CAXuB;UAY1CS,iBAAiB,EAAE,CAAC;YAAE1E,KAAK,EAAE,EAAT;YAAa0D,QAAQ,EAAE;UAAvB,CAAD,CAZuB;UAa1CkB,iBAAiB,EAAE,CAAC;YAAE5E,KAAK,EAAE,EAAT;YAAa0D,QAAQ,EAAE;UAAvB,CAAD,CAbuB;UAc1CoB,iBAAiB,EAAE,CAAC;YAAE9E,KAAK,EAAE,EAAT;YAAa0D,QAAQ,EAAC;UAAtB,CAAD,CAduB;UAe1CP,OAAO,EAAE,IAAIpF,WAAJ,CAAgBqF,MAAM,CAAC+E,UAAP,CAAkBnD,WAAlB,EAA+BxB,OAA/B,CAAuC,CAAvC,CAAhB,EAA2DxF,UAAU,CAACiG,QAAtE,CAfiC;UAgB1CgB,cAAc,EAAE,IAAIlH,WAAJ,CAAgB,GAAhB,EAAqBC,UAAU,CAACiG,QAAhC,CAhB0B;UAiB1Ce,WAAW,EAAE,IAAIjH,WAAJ,CAAgB;YAAEiC,KAAK,EAAEoD,MAAM,CAAC+E,UAAP,CAAkBnD,WAAlB,EAA+BxB,OAA/B,CAAuC,CAAvC,CAAT;YAAoDE,QAAQ,EAAE;UAA9D,CAAhB,EAAuF,CAAC1F,UAAU,CAACiG,QAAZ,CAAvF,CAjB6B;UAkB1CV,KAAK,EAAE,CAAC;YAAEvD,KAAK,EAAEuD,KAAT;YAAgBG,QAAQ,EAAE;UAA1B,CAAD,CAlBmC;UAmB1C0B,KAAK,EAAE,CAAC;YAAEpF,KAAK,EAAE,IAAT;YAAe0D,QAAQ,EAAE;UAAzB,CAAD,CAnBmC;UAoB1C2B,oBAAoB,EAAE,CAAC;YAAErF,KAAK,EAAG8T,sBAAsB,CAACzO,oBAAjC;YAAuD3B,QAAQ,EAAE;UAAjE,CAAD,EAA0E,CAAC1F,UAAU,CAAC0M,SAAX,CAAqB,EAArB,CAAD,EAA2B1M,UAAU,CAAC2M,SAAX,CAAqB,EAArB,CAA3B,CAA1E,CApBoB;UAqB1CrF,UAAU,EAAE,EArB8B;UAsB1CC,cAAc,EAAE,KAAKpE,WAAL,CAAiByF,MAAjB,GAA0B,CAtBA;UAuB1CpB,YAAY,EAAE,IAAIzH,WAAJ,CAAgB,EAAhB,EAAoB,CAACS,2BAAD,EAA8BR,UAAU,CAACmH,GAAX,CAAe,CAAf,CAA9B,EAAiDnH,UAAU,CAACkH,GAAX,CAAe,WAAf,CAAjD,CAApB,CAvB4B;UAwB1CO,QAAQ,EAAE,EAxBgC;UAyB1CC,UAAU,EAAE,EAzB8B;UA0B1CC,SAAS,EAAE,EA1B+B;UA2B1CC,MAAM,EAAE,IAAI7H,WAAJ,CAAgB,EAAhB,EAAoB,CAACS,2BAAD,EAA8BR,UAAU,CAACmH,GAAX,CAAe,CAAf,CAA9B,EAAiDnH,UAAU,CAACkH,GAAX,CAAe,WAAf,CAAjD,CAApB,CA3BkC;UA4B1CW,SAAS,EAAE,EA5B+B;UA6B1CC,SAAS,EAAE,EA7B+B;UA8B1CC,SAAS,EAAE,EA9B+B;UA+B1CC,OAAO,EAAE,EA/BiC;UAgC1CC,OAAO,EAAE;QAhCiC,CAA3B,CAAjB;QAmCA,KAAK9E,WAAL,CAAiB+E,IAAjB,CAAsBuE,QAAtB;QACA,KAAKjC,cAAL,CAAoBC,IAApB;QACA,KAAK/B,cAAL;;QACA,IAAI,KAAKvF,WAAL,CAAiB8F,OAArB,EAA8B;UAC5BhJ,uBAAuB,CAACiJ,iBAAxB,CAA0C,KAAKtF,eAA/C;UACA;QACD;MACF;IACA;EACF;;EAEDwR,uBAAuB;IACrB,IAAIjQ,OAAO,GAAExE,SAAS,CAACkJ,SAAvB;;IACG,IAAG,CAAC,CAAC,KAAK/G,aAAL,CAAmB6G,cAAxB,EAAuC;MACvCxE,OAAO,GAAG,KAAKrC,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAA/C,IAA0D,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAAzG,IAAoH,KAAK9G,aAAL,CAAmB6G,cAAnB,EAAmCC,OAAnC,KAA+C,OAAnK,GAA8KjJ,SAAS,CAACkJ,SAAxL,GAAsMlJ,SAAS,CAACmJ,qBAA1N;IACE,CAFF,MAGK,IAAG,KAAKhH,aAAL,CAAmBI,UAAtB,EAAiC;MACtCiC,OAAO,GAAG,KAAKrC,aAAL,CAAmBkT,SAAnB,CAA6BpM,OAA7B,KAAwC,OAAxC,IAAkD,KAAK9G,aAAL,CAAmBkT,SAAnB,EAA8BpM,OAA9B,KAA0C,OAA5F,IAAuG,KAAK9G,aAAL,CAAmBkT,SAAnB,EAA8BpM,OAA9B,KAA0C,OAAjJ,GAA4JjJ,SAAS,CAACkJ,SAAtK,GAAmLlJ,SAAS,CAACmJ,qBAAvM;IACC;;IACD,OAAO3E,OAAP;EACJ;;AA/iCmC;;;mBAAzB/D,2BAAyBR;AAAA;;;QAAzBQ;EAAyB6U;EAAAC;IAAAlR;IAAAH;IAAA/B;IAAAsB;EAAA;EAAA+R;IAAAC;IAAA7F;IAAA/F;EAAA;EAAA6L;EAAAC;EAAAC;EAAAC;IAAA;MChCtC5V,gCAAmE,CAAnE,EAAmE,KAAnE,EAAmE,CAAnE,EAAmE,CAAnE,EAAmE,KAAnE,EAAmE,CAAnE,EAAmE,CAAnE,EAAmE,GAAnE,EAAmE,CAAnE;MAGkCA;MAAgBA;MAE1CA;MASAA,+BAAiC,CAAjC,EAAiC,KAAjC,EAAiC,CAAjC,EAAiC,CAAjC,EAAiC,OAAjC,EAAiC,CAAjC;MAG0FA;QAAA,OAAU6V,aAAV;MAAmB,CAAnB;MAAlF7V;MACAA;MAA6DA;MAAQA;MAEzEA,gCAAqD,EAArD,EAAqD,OAArD,EAAqD,CAArD;MACuFA;QAAA,OAAU6V,aAAV;MAAmB,CAAnB;MAAnF7V;MACAA;MAA4DA;MAAQA;MAMhFA,iCAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB,EAAsB,EAAtB,EAAsB,SAAtB,EAAsB,EAAtB,EAAsB,EAAtB,EAAsB,KAAtB,EAAsB,EAAtB,EAAsB,EAAtB,EAAsB,OAAtB,EAAsB,EAAtB,EAAsB,EAAtB,EAAsB,OAAtB,EAAsB,EAAtB,EAAsB,IAAtB,EAAsB,EAAtB,EAAsB,IAAtB,EAAsB,EAAtB;MAQoEA;MAAqBA;MAC7DA;MAAoDA;MAAmBA;MACvEA;MAAoDA;MAAMA;MAC1DA;MAAwCA;MAAkCA;MAC1EA;MAAoDA;MAAoBA;MACxEA;MAAgFA;MACjEA;MACfA;MAAgFA;MACpEA;MACZA;MAAgFA;MACzEA;MACPA;MAAoDA;MAAQA;MAC5DA;MAAoDA;MAAUA;MAC9DA;MAAgFA;MACvDA;MACzBA;MAGJA;MACAA,4BAAI,EAAJ,EAAI,IAAJ,EAAI,EAAJ;MAC2CA;MAAIA;MAC3CA;MAAuCA;MAAEA;MACzCA;MAAuCA;MACnCA;MAIJA;MACAA;MAAuCA;MAAQA;MAIvDA;MACIA;MAurBJA;MAEJA;MAIJA;;;;MAlwBVA;MAiCkBA;MAAAA;MAkBaA;MAAAA;MASIA;MAAAA;MASsCA;MAAAA;MA0rBtDA;MAAAA", "names": ["ChangeDetectorRef", "EventEmitter", "FormControl", "Validators", "submitValidate<PERSON>ll<PERSON>ields", "distinctUntilChanged", "first", "moment", "<PERSON><PERSON>", "JSLZString", "CptViewHistoryComponent", "numberWith3DecimalValidator", "modifierValidator", "LocalStorageKey", "PriceCost", "i0", "ctx_r115", "ctx_r91", "ctx_r100", "ctx_r10", "_r11", "ctx_r17", "_r18", "ServiceLineClaimComponent", "constructor", "serviceLineform", "placeOfService", "subjectService", "cptservice", "dialog", "cacheService", "cdRef", "spinner", "Date", "text", "value", "addDate", "termDate", "getBillingProviderNPIChange", "subscribe", "res", "changeRenderProviderNo", "resetServicelineProviderNPIChange", "ngOnInit", "show", "detectChanges", "setTimeout", "patchValue", "nDcQualifier", "claimFormData", "ndcQualifierDataByType", "nDcQualifierQty", "ndcQtyQualifierDataByType", "isAddClaim", "serviceLine", "controls", "for<PERSON>ach", "control", "get", "setValue", "emitEvent", "hide", "createForm", "serviceLineInfo", "group", "showNDC", "serviceLines", "array", "f", "placeOfServiceData", "localStorageGetItem", "allPlaceOfServices", "fetchchAllPlaceOfServices", "renderingNPI", "provider", "renderingProvider", "providerNPI", "posCode", "placeOfServiceCode", "dOSFrom", "memberProfile", "dOSTo", "groups", "serviceLineInfos", "map", "ele", "charges", "Number", "units", "<PERSON><PERSON>t", "total", "toFixed", "dateServiceFrom", "disabled", "dateServiceTo", "locationOfService", "emg", "eMG", "cpt", "cptCode", "required", "desc", "m1", "m2", "m3", "m4", "diagnosispointer1", "d1", "toString", "diagnosispointer2", "d2", "diagnosispointer3", "d3", "diagnosispointer4", "d4", "unitCharges", "dayUnitChanges", "max", "min", "ePSDT", "jRenderingProviderId", "proceduceC", "proceduceCount", "ndcUnitPrice", "lineNote", "ndcQtyQual", "anesStart", "ndcQty", "anesStop1", "anesStop2", "anesStop3", "ndcQual", "ndcCode", "push", "isEditClaim", "fetchCPTData", "fetchchAllCPTCharges", "is<PERSON>iew<PERSON><PERSON><PERSON>", "patchServiceLineData", "disable", "enable", "dateValidators", "index", "length", "at", "hideNDC", "setValidator", "isSubmited", "invalid", "validate<PERSON>ll<PERSON>ields", "OnCPTCodeChange", "codeValue", "cptCharge", "allCPTCharges", "filter", "item", "code", "charge", "claimViewModel", "payerId", "zeroPrice", "zeroPointZeroOnePrice", "cptDescription", "cptCodes", "find", "description", "parseFloat", "invalidCptCode", "element", "setErrors", "invalidCpt", "calculateTotal", "emit", "validateCPTCode", "pipe", "response", "content", "validateForm", "maxlength", "checkDateValidations", "checkDateFromValidation", "ndcQualChange", "changeValuesToMarkValidation", "changeQual", "e", "fields", "validatorToBeApplied", "field", "setValidators", "removeValidators", "<PERSON><PERSON><PERSON><PERSON>", "errors", "updateValueAndValidity", "clear", "ndcValidation", "formArr", "formGroup", "calculateTotalAmount", "i", "undefined", "addnewService", "profileMember", "claimDosfrom", "claimDosto", "empGroup", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "removeService", "removeAt", "target", "setServicelineProviderNPIChange", "data", "valueChanges", "getTime", "invalidDate", "placeOfServiceChanges", "fetchCPT", "result", "dosFrom", "add_date", "term_date", "mdmCode", "cPTCodes", "validateInvalidCPT", "localStorage", "getItem", "allCPT", "JSON", "parse", "decompress", "fetchAllCpt", "setYesOrNo", "count", "claimsProfessional837", "serviceLineProfessional837s", "date", "dtp03ServiceDate", "fromDate", "substring", "toDate", "sv105PlaceOfServiceCode", "sv109EmergencyIndicator", "sv10107ServiceDescription", "sv10102ProcedureCode", "sv10103ProcedureModifier1", "sv10104ProcedureModifier2", "sv10105ProcedureModifier3", "sv10106ProcedureModifier4", "sv10701DiagnosisCodePointer1", "sv10702DiagnosisCodePointer2", "sv10703DiagnosisCodePointer3", "sv10704DiagnosisCodePointer4", "sv102LineItemChargeAmount", "sv104ServiceUnitCount", "sv111EpsdtIndicator", "nm109RenderingProviderIdentifier", "sv10101ProductServiceIdQualifier", "cpt03NationalDrugUnitPrice", "nte02LineNoteText", "ctp0501UnitMeasurementCode", "ctp04NationalDrugUnitCount", "anesStop", "lin02NationalDrugCodeQlfr", "lin03NationalDrugCode", "validateCPTCodeDOS", "dosFromChangedEvent", "dosFromChanged", "twoYearValidationForDate", "minDate", "days", "todayDate", "diff", "fire", "title", "icon", "showCancelButton", "confirmButtonText", "showDenyButton", "denyButtonText", "reverseButtons", "then", "dateShow", "ndcCodeValidator", "<PERSON><PERSON><PERSON><PERSON>", "trim", "String", "match", "toLowerCase", "isNdcCodeValid", "servicelineEdit", "checked", "addServiceShow", "v", "oldServiceDateFrom", "oldServiceDateTo", "onSearchQualifer", "term", "toLocaleLowerCase", "indexOf", "k", "dublicate", "h", "j", "<PERSON><PERSON><PERSON><PERSON>ched", "countMatched", "t", "fetchchCPTCodeSeach", "patchValueMatched", "totalCount", "cptData", "cptDosData", "dos", "CPTValidorNot", "cptViewHistory", "request", "_d", "subscribeID", "subscribeId", "subscriberID", "getCPTViewHistory", "resp", "cptList", "statusCode", "isSelected", "existingServiceLines", "cptItem", "viewHistoryCPTList", "dialogRef", "open", "height", "width", "autoFocus", "restoreFocus", "maxHeight", "panelClass", "afterClosed", "selectedCPTCodes", "mapServiceLinesSelecteCPCodes", "servicelineMatchedFromViewHistoryCPTCodes", "serviceLineItem", "some", "selecteItm", "findIndex", "getUnitChargesByPayerId", "getExistingFirstServiceLine", "servicelineNoMatchedFromSelectedCPTCodes", "isViewHistoryItem", "selectedItem", "existingItem", "shortDescription", "shift", "cptCodeDetails", "patchSelecteCPTCodesFromHistory", "topSelectedServiceLine", "getRawValue", "payerItem", "selectors", "inputs", "outputs", "diagnosis<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Projects\\Practice Management\\Web\\Mirra.Web.PracticeManagement\\Mirra.Web.PracticeManagement\\src\\app\\components\\create-claim\\claim-creation\\create-claim\\service-line-claim\\service-line-claim.component.ts", "C:\\Projects\\Practice Management\\Web\\Mirra.Web.PracticeManagement\\Mirra.Web.PracticeManagement\\src\\app\\components\\create-claim\\claim-creation\\create-claim\\service-line-claim\\service-line-claim.component.html"], "sourcesContent": ["\r\nimport { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\r\n\r\nimport { _MatTabGroupBase } from '@angular/material/tabs';\r\nimport { QualifierDataByType } from 'src/app/classmodels/ResponseModel/ClaimForm/QualifierDataByType';\r\nimport { MemberResult } from 'src/app/classmodels/ResponseModel/Member/MemberResult';\r\nimport { submitValidateAllFields } from 'src/app/common/form.validators';\r\nimport { ServiceLine, ServiceLineSelectionModel } from 'src/app/models/ClaimForm/ServiceLineSelectionModel';\r\nimport { ServiceLineProfessional837sModel } from 'src/app/models/ClaimForm/claim.create.model';\r\nimport { ClaimInfoModel } from 'src/app/models/Providers/ClaimInfoModel';\r\nimport { AllCPTCode, ValidateCPTRequest } from 'src/app/classmodels/ResponseModel/ClaimForm/AllCPTCode';\r\nimport { GetAllCPTCodeService } from 'src/app/services/ClaimForm/get-all-cptcode.service';\r\nimport { AllPlaceOfServicesService } from 'src/app/services/ClaimForm/all-place-of-services.service';\r\nimport { AllPlaceOfServices } from 'src/app/classmodels/ResponseModel/ClaimForm/AllPlaceOfServices';\r\nimport { SubjectService } from 'src/app/shared/services/subject.service';\r\nimport { distinctUntilChanged, first } from 'rxjs';\r\nimport * as moment from 'moment';\r\nimport Swal from 'sweetalert2';\r\nimport * as JSLZString from 'lz-string';\r\nimport { AllICDCode } from 'src/app/classmodels/ResponseModel/ClaimForm/AllICDCode';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { CptViewHistoryComponent } from '../../../../../modals/cpt-view-history/cpt-view-history.component';\r\nimport { numberWith3DecimalValidator,modifierValidator } from 'src/app/common/form.validators';\r\nimport { CacheService } from 'src/app/services/cache-service/cache.service';\r\nimport { LocalStorageKey, PriceCost } from 'src/app/shared/constant/constatnt';\r\nimport { NgxSpinnerService } from 'ngx-spinner';\r\n@Component({\r\n  selector: 'app-service-line-claim',\r\n  templateUrl: './service-line-claim.component.html',\r\n  styleUrls: ['./service-line-claim.component.scss']\r\n})\r\nexport class ServiceLineClaimComponent implements OnInit {\r\n  serviceLineInfo: FormGroup;\r\n  @Input() serviceLineInfos: ServiceLine;\r\n  todayDate: Date = new Date();\r\n  minDate = new Date('2015-10-01');\r\n  @Input() memberProfile: MemberResult;\r\n  @Input() claimFormData: ClaimInfoModel;\r\n  @Output() diagnosisPointerBlur: EventEmitter<any> = new EventEmitter();\r\n  @Output() dosFromChanged: EventEmitter<any> = new EventEmitter();\r\n  @Output() calculateTotal: EventEmitter<any> = new EventEmitter();\r\n  @Input() allPlaceOfServices: AllPlaceOfServices[] = [];\r\n\r\n  allICDCode: AllICDCode[] = [{ text: \"abcdef\", value: \"abcdef\", addDate: '', termDate: '' }];\r\n  isControl: boolean = true;\r\n  nDcQualifierQty: QualifierDataByType[] = [];\r\n  nDcQualifier: QualifierDataByType[] = [];\r\n  allCPTCode: AllCPTCode[] = [];\r\n  allCPTCharges: any = [];\r\n  viewHistoryCPTList:any[]=[];\r\n  cmg: any[] = [\"Yes\", \"No\"]\r\n  epsdt: any[] = [\"Yes\", \"No\"];\r\n  isSubmited: boolean = false;\r\n  constructor(private serviceLineform: FormBuilder,\r\n    private placeOfService: AllPlaceOfServicesService,\r\n    private subjectService: SubjectService,\r\n    private cptservice: GetAllCPTCodeService,\r\n    private dialog: MatDialog,\r\n    private cacheService:CacheService,\r\n    private cdRef: ChangeDetectorRef, \r\n    private readonly spinner:NgxSpinnerService) {\r\n    this.subjectService.getBillingProviderNPIChange().subscribe(res => {\r\n      if (res || res == '') {\r\n        this.changeRenderProviderNo(res);\r\n        this.subjectService.resetServicelineProviderNPIChange();\r\n      }\r\n    })\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.spinner.show('child');\r\n    this.cdRef.detectChanges();\r\n   \r\n    setTimeout(() => {\r\n      this.patchValue(); // Heavy synchronous form patch\r\n    this.nDcQualifier = this.claimFormData.ndcQualifierDataByType;\r\n    this.nDcQualifierQty = this.claimFormData.ndcQtyQualifierDataByType;\r\n\r\n    if (this.claimFormData.isAddClaim) {\r\n      this.serviceLine.controls.forEach(control => {\r\n        control.get('proceduceC')?.setValue('HC', { emitEvent: false });\r\n      });\r\n    }\r\n    this.spinner.hide('child'); \r\n    }, 1000); \r\n    \r\n  }\r\n\r\n  createForm() {\r\n    this.serviceLineInfo = this.serviceLineform.group({\r\n      showNDC: new FormControl('No'),\r\n\r\n      serviceLines: this.serviceLineform.array([]),\r\n    })\r\n    return this.serviceLineInfo;\r\n  }\r\n  get serviceLine() {\r\n    return this.serviceLineInfo.get('serviceLines') as FormArray;\r\n  }\r\n  get f() {\r\n    return this.serviceLineInfo.controls;\r\n  }\r\n  // allPlaceOfServices: AllPlaceOfServices[] = [];\r\n  placeOfServiceData() {\r\n    if (!!this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices)) {\r\n          this.allPlaceOfServices = this.cacheService.localStorageGetItem(LocalStorageKey.allPlaceOfServices);\r\n        }\r\n        else{\r\n          this.placeOfService.fetchchAllPlaceOfServices().subscribe((res: AllPlaceOfServices[]) => {\r\n            this.allPlaceOfServices = res;\r\n          })\r\n        }\r\n  }\r\n  patchValue() {\r\n    if (this.claimFormData.isAddClaim) {\r\n      const renderingNPI = this.claimFormData?.provider?.renderingProvider?.providerNPI;\r\n      const posCode = this.claimFormData?.placeOfServiceCode;\r\n      const dOSFrom = new Date(this.memberProfile?.dOSFrom);\r\n      const dOSTo = this.memberProfile?.dOSTo;\r\n  \r\n      const groups = this.serviceLineInfos?.serviceLines.map((ele: ServiceLineSelectionModel) => {\r\n        const charges = Number(ele.charges) || 0;\r\n        const units = Number(ele.daysunit) || 0;\r\n        const total = (units * charges).toFixed(2);\r\n  \r\n        return this.serviceLineform.group({\r\n          dateServiceFrom: [{ value: dOSFrom, disabled: true }] ,\r\n          dateServiceTo:[{ value: dOSTo, disabled: true }]   ,\r\n          locationOfService: [{ value: posCode, disabled: false }],\r\n          emg: [{ value: ele.eMG, disabled: false }],\r\n          cpt: [{ value: ele.cptCode, disabled: false }, Validators.required],\r\n          desc: [{ value: ele.desc }],\r\n          m1: [{ value: ele.m1, disabled: false }],\r\n          m2: [{ value: ele.m2, disabled: false }],\r\n          m3: [{ value: ele.m3, disabled: false }],\r\n          m4: [{ value: ele.m4, disabled: false }],\r\n          diagnosispointer1: [{ value: Number(ele.d1).toString(), disabled: false }],\r\n          diagnosispointer2: [{ value: ele.d2 ? Number(ele.d2).toString() : '', disabled: false }],\r\n          diagnosispointer3: [{ value: ele.d3 ? Number(ele.d3).toString() : '', disabled: false }],\r\n          diagnosispointer4: [{ value: ele.d4 ? Number(ele.d4).toString() : '', disabled: false }],\r\n          unitCharges: [{ value: ele.charges, disabled: false }, [Validators.required]],\r\n          dayUnitChanges: [{ value: ele.daysunit, disabled: false },[ Validators.required, Validators.max(99999999), Validators.min(1)]],\r\n          total: [{ value: total, disabled: true }],\r\n          ePSDT: [{ value: ele.ePSDT, disabled: false }],\r\n          jRenderingProviderId: [{ value: renderingNPI, disabled: true }],\r\n          proceduceC: '',\r\n          proceduceCount: '',\r\n          ndcUnitPrice: '',\r\n          lineNote: '',\r\n          ndcQtyQual: null,\r\n          anesStart: '',\r\n          ndcQty: '',\r\n          anesStop1: '',\r\n          anesStop2: '',\r\n          anesStop3: '',\r\n          ndcQual: null,\r\n          ndcCode: ''\r\n        })\r\n      });\r\n  \r\n      groups.forEach(group => this.serviceLine.push(group))\r\n  \r\n    }\r\n    \r\n    if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim) {\r\n      \r\n        this.fetchCPTData();\r\n        this.fetchchAllCPTCharges();\r\n        this.placeOfServiceData();\r\n    }\r\n\r\n    // View Claim Show NDC radio button enabled \r\n    if (this.claimFormData.isViewClaim) {\r\n      this.patchServiceLineData();\r\n      this.serviceLineInfo.disable();\r\n      this.serviceLineInfo.controls[\"showNDC\"].enable();\r\n    }\r\n    this.dateValidators();\r\n  }\r\n  \r\n\r\n  showNDC() {\r\n    if (this.claimFormData.isAddClaim) {\r\n      for (let index = 0; index < this.serviceLineInfos?.serviceLines.length; index++) {\r\n        this.serviceLine.at(index).get('proceduceC').patchValue('HC');\r\n        this.serviceLine.at(index).get('proceduceCount').patchValue(index + 1);\r\n      }\r\n    }\r\n  }\r\n  hideNDC() {\r\n\r\n  }\r\n\r\n  setValidator() {\r\n    this.isSubmited\r\n    if (this.serviceLineInfo.invalid) {\r\n      submitValidateAllFields.validateAllFields(this.serviceLineInfo);\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n  invalidCptCode: string[] = [];\r\n  \r\n  OnCPTCodeChange(codeValue: any, index: any) {\r\n  let dayUnitChanges = this.serviceLine.at(index).get('dayUnitChanges').value ? this.serviceLine.at(index).get('dayUnitChanges').value:'1';\r\n    let unitCharges = this.serviceLine.at(index).get('unitCharges').value;\r\n    if (codeValue) {\r\n      let cptCharge = this.allCPTCharges.filter(item => item.code == codeValue)[0]?.charge;\r\n       if(!!cptCharge){\r\n        unitCharges =cptCharge;\r\n       }\r\n       else{\r\n        unitCharges = (this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\") ? \r\n        PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\r\n       }\r\n     \r\n      let cptDescription = this.cptCodes.find(item => item.cpt == codeValue).description;\r\n      this.serviceLine.at(index).patchValue({\r\n        dayUnitChanges: (dayUnitChanges),\r\n        total: (dayUnitChanges * unitCharges).toFixed(2),\r\n        desc: {value : cptDescription},\r\n        proceduceC:'HC' ,\r\n        unitCharges: Number.parseFloat(unitCharges).toFixed(2)\r\n      })\r\n\r\n      this.invalidCptCode.forEach(element => {\r\n        if (element === codeValue) {\r\n          this.serviceLine.at(index).get('cpt').setErrors({ invalidCpt: true })\r\n        }\r\n      });\r\n\r\n    }\r\n    else {\r\n      this.serviceLine.at(index).patchValue({\r\n        dayUnitChanges: null,\r\n        total: (0 * unitCharges).toFixed(2)\r\n      })\r\n    }\r\n\r\n    this.calculateTotal.emit();\r\n    this.validateCPTCode();\r\n  }\r\n\r\n  fetchchAllCPTCharges() {\r\n    if (this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges)) {\r\n      this.allCPTCharges = this.cacheService.localStorageGetItem(LocalStorageKey.allCPTCharges);\r\n      return;\r\n    } \r\n    this.cptservice.fetchchAllCPTCharges().pipe(first()).subscribe(response => {                           //next() callback\r\n      this.allCPTCharges = response.content;\r\n    });\r\n  }\r\n\r\n  validateForm() {\r\n\r\n    for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\r\n      if (this.serviceLine.at(index).get('jRenderingProviderId').value.length != 10) {\r\n        this.serviceLine.at(this.serviceLineInfo.controls['serviceLines'].value.length - 1).get('jRenderingProviderId').setErrors({ maxlength: true });\r\n      }\r\n      this.checkDateValidations(index, 'dateServiceFrom');\r\n      this.checkDateFromValidation(index, 'dateServiceTo');\r\n      this.ndcQualChange(index);\r\n      this.changeValuesToMarkValidation(index);\r\n      this.validateCPTCode();\r\n    }\r\n\r\n    return this.setValidator();\r\n\r\n  }\r\n  \r\n  changeQual(e, index) {\r\n\r\n    if (e) {\r\n      if (!this.serviceLine.at(index).get('ndcCode').value)\r\n        this.serviceLine.at(index).get('ndcCode').setErrors({ required: true });\r\n      if (!this.serviceLine.at(index).get('ndcQty').value)\r\n        this.serviceLine.at(index).get('ndcQty').setErrors({ required: true });\r\n      if (!this.serviceLine.at(index).get('ndcQual').value)\r\n        this.serviceLine.at(index).get('ndcQual').setErrors({ required: true });\r\n    } else {\r\n      this.serviceLine.at(index).get('ndcCode').setErrors(null);\r\n      this.serviceLine.at(index).get('ndcQty').setErrors(null);\r\n      this.serviceLine.at(index).get('ndcQual').setErrors(null);\r\n    }\r\n    this.changeValuesToMarkValidation(index);\r\n  }\r\n  changeValuesToMarkValidation(index) {\r\n    let fields = ['ndcCode', 'ndcQty', 'ndcQual', 'ndcQtyQual'];\r\n    let validatorToBeApplied = false;\r\n    for (const field of fields) {\r\n      validatorToBeApplied = validatorToBeApplied || !!this.serviceLine.at(index).get(field).value;\r\n    }\r\n    if (validatorToBeApplied) {\r\n      for (const field of fields) {\r\n        if (!(!!this.serviceLine.at(index).get(field).value)) {\r\n          this.serviceLine.at(index).get(field).setErrors({ required: true });\r\n          this.serviceLine.at(index).get(field).setValidators(Validators.required);\r\n        }\r\n      }\r\n    } else {\r\n      for (const field of fields) {\r\n        this.serviceLine.at(index).get(field).removeValidators(Validators.required);\r\n        if (this.serviceLine.at(index).get(field).hasError('required')) {\r\n          delete this.serviceLine.at(index).get(field).errors['required'];\r\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n        }\r\n      }\r\n\r\n    }\r\n    // fields = fields.filter(field => field != fieldName);\r\n    // if (!!this.serviceLine.at(index).get(fieldName).value) {\r\n    //   for (const field of fields) {\r\n    //     if (!(!!this.serviceLine.at(index).get(field).value)) {\r\n    //       this.serviceLine.at(index).get(field).setErrors({required: true})\r\n    //     } else {\r\n    //       if (this.serviceLine.at(index).get(field).hasError('required')) {\r\n    //         delete this.serviceLine.at(index).get(field).errors['required'];\r\n    //         this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n    //       }\r\n    //     }\r\n    //   }\r\n    // }\r\n  }\r\n  clear(index) {\r\n    this.serviceLine.at(index).get('ndcCode').setErrors(null);\r\n    this.serviceLine.at(index).get('ndcQty').setErrors(null);\r\n    this.serviceLine.at(index).get('ndcQual').setErrors(null);\r\n  }\r\n\r\n  ndcValidation(index) {\r\n    let formArr = this.serviceLineInfo.controls['serviceLines'] as FormArray;\r\n    const formGroup = formArr.controls[index] as FormGroup;\r\n    return formGroup;\r\n  }\r\n\r\n\r\n\r\n  calculateTotalAmount(e, i) {\r\n    let unitCharges = this.serviceLine.at(i).get('unitCharges').value;  \r\n    if (unitCharges == null || unitCharges == undefined || unitCharges.length == 0) {\r\n      unitCharges = (this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\") ? \r\n      PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice;\r\n      this.serviceLine.at(i).get('unitCharges').setValue(unitCharges);\r\n    }\r\n    let dayUnitChanges: number = this.serviceLine.at(i).get('dayUnitChanges').value;\r\n    this.serviceLine.at(i).patchValue({\r\n      total: Number.parseFloat((dayUnitChanges * unitCharges).toString()).toFixed(2)\r\n    })\r\n\r\n    this.calculateTotal.emit(e);\r\n    return false\r\n  }\r\n  minimumcharacterTohitAPI: number = 3;\r\n  cPTCodes: AllCPTCode[] = [];\r\n\r\n\r\n\r\n  addnewService() {\r\n    const dOSFrom =    this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);\r\n    const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);\r\n    let locationOfService = '11';\r\n    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\r\n      locationOfService = this.serviceLine.controls[0].value.locationOfService;\r\n    }\r\n    const empGroup = this.serviceLineform.group({\r\n      dateServiceFrom: new FormControl({value: dOSFrom, disabled: true}, [Validators.required]),\r\n      dateServiceTo: new FormControl( {value: dOSTo, disabled: true}, [Validators.required  ]),\r\n      locationOfService: new FormControl(locationOfService, Validators.required),\r\n      emg: new FormControl(null),\r\n      desc: [{ value: null }],\r\n      cpt: new FormControl(null, [Validators.required]),\r\n      m1: [{ value: '', disabled: this.claimFormData.isViewClaim }, modifierValidator],\r\n      m2: [{ value: '', disabled: this.claimFormData.isViewClaim }, modifierValidator],\r\n      m3: [{ value: \"\", disabled: this.claimFormData.isViewClaim }, modifierValidator],\r\n      m4: [{ value: '', disabled: this.claimFormData.isViewClaim }, modifierValidator],\r\n      diagnosispointer1: new FormControl('1', [Validators.required]),\r\n      diagnosispointer2: [{ value: '', disabled: this.claimFormData.isViewClaim }],\r\n      diagnosispointer3: [{ value: '', disabled: this.claimFormData.isViewClaim }],\r\n      diagnosispointer4: [{ value: '', disabled: this.claimFormData.isViewClaim }],\r\n      unitCharges: new FormControl((this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\") ? PriceCost.zeroPrice : PriceCost.zeroPointZeroOnePrice, Validators.required),\r\n      dayUnitChanges : new FormControl({ value: '1', disabled: this.claimFormData.isViewClaim }, [Validators.required, Validators.max(99999999), Validators.min(1)]),\r\n      total: [{ value: (this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\") ? PriceCost.zeroPrice  : PriceCost.zeroPointZeroOnePrice, disabled: true }],\r\n      ePSDT: [{ value: null, disabled: this.claimFormData.isViewClaim }],\r\n      jRenderingProviderId: [{ value: this.serviceLine.at(0)?.get('jRenderingProviderId')?.value, disabled: true }, [Validators.maxLength(10), Validators.minLength(10)]],\r\n      //new FormControl(this.serviceLine.at(0)?.get('jRenderingProviderId')?.value, [Validators.maxLength(10), Validators.minLength(10)]).disable(),\r\n      proceduceC: '',\r\n      proceduceCount: this.serviceLine.length + 1,\r\n      ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\r\n      lineNote: '',\r\n      ndcQtyQual: '',\r\n      anesStart: '',\r\n      ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\r\n      anesStop1: '',\r\n      anesStop2: '',\r\n      anesStop3: '',\r\n      ndcQual: '',\r\n      ndcCode: ''\r\n    });\r\n\r\n    this.serviceLine.push(empGroup);\r\n    // this.dateValidators();\r\n    // if (this.serviceLine.invalid) {\r\n    //   submitValidateAllFields.validateAllFields(this.serviceLineInfo);\r\n    //   return;\r\n    // }\r\n\r\n  }\r\n  removeService(i) {    \r\n    \r\n    this.serviceLine.removeAt(i);\r\n    this.calculateTotal.emit();\r\n    this.validateForm();\r\n  }\r\n\r\n  renderingProvider(e: any) {\r\n    this.changeRenderProviderNo(e.target.value);\r\n    this.subjectService.setServicelineProviderNPIChange(e.target.value);\r\n  }\r\n  changeRenderProviderNo(data) {\r\n    if (!!this.serviceLineInfo.controls) {\r\n      for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\r\n        this.serviceLine.at(index).get('jRenderingProviderId').patchValue(data);\r\n      }\r\n    }\r\n  }\r\n  dateValidators() {\r\n    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\r\n      for (let item of this.serviceLine.controls) {\r\n        item.get('dateServiceFrom').valueChanges.pipe(distinctUntilChanged()).subscribe((dateServiceFrom) => {\r\n          if (!!dateServiceFrom) {\r\n            if (!!item.get('dateServiceTo').value) {\r\n              if (new Date(dateServiceFrom).getTime() > (new Date(item.get('dateServiceTo').value).getTime())) {\r\n                item.get('dateServiceFrom').setErrors({ invalidDate: true });\r\n              } else if(new Date(dateServiceFrom).getTime() < (new Date(item.get('dateServiceTo').value).getTime())){\r\n                item.get('dateServiceFrom').updateValueAndValidity();\r\n                item.get('dateServiceTo').updateValueAndValidity();\r\n              } else {\r\n                if (item.get('dateServiceFrom').hasError('invalidDate')) {\r\n                  delete item.get('dateServiceFrom').errors['invalidDate'];\r\n                  item.get('dateServiceFrom').updateValueAndValidity();\r\n                }\r\n                if (item.get('dateServiceFrom').hasError('required')) {\r\n                  delete item.get('dateServiceFrom').errors['invalidDate'];\r\n                  item.get('dateServiceFrom').updateValueAndValidity();\r\n                }\r\n              }\r\n            } else {\r\n              if (item.get('dateServiceFrom').hasError('invalidDate')) {\r\n                delete item.get('dateServiceFrom').errors['invalidDate'];\r\n                item.get('dateServiceFrom').updateValueAndValidity();\r\n              }\r\n              if (item.get('dateServiceFrom').hasError('required')) {\r\n                delete item.get('dateServiceFrom').errors['invalidDate'];\r\n                item.get('dateServiceFrom').updateValueAndValidity();\r\n              }\r\n            }\r\n          } else {\r\n            item.get('dateServiceFrom').setErrors({ required: true });\r\n          }\r\n        })\r\n        item.get('dateServiceTo').valueChanges.pipe(distinctUntilChanged()).subscribe((dateServiceTo) => {\r\n          if (!!dateServiceTo) {\r\n            if (!!item.get('dateServiceFrom').value) {\r\n              if (new Date(dateServiceTo).getTime() < (new Date(item.get('dateServiceFrom').value).getTime())) {\r\n                item.get('dateServiceTo').setErrors({ invalidDate: true });\r\n              } else {\r\n                item.get('dateServiceTo').setErrors(null);\r\n                item.get('dateServiceFrom').updateValueAndValidity();\r\n              }\r\n            } else {\r\n              item.get('dateServiceTo').setErrors(null);             \r\n            }\r\n          } else {\r\n            item.get('dateServiceTo').setErrors({ required: true });\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n  placeOfServiceChanges(e:any) {\r\n\r\n    if (!!this.serviceLineInfo.controls) {\r\n      for (let index = 0; index < this.serviceLineInfo.controls['serviceLines'].value.length; index++) {\r\n        this.serviceLine.at(index).get('locationOfService').patchValue(!!e?.value?e.value:null);\r\n      }\r\n    }\r\n  }\r\n  fetchCPT() {\r\n    let result = [];\r\n    let dosFrom;\r\n    if(this.claimFormData.isAddClaim){\r\n      dosFrom = new Date(this.claimFormData?.profileMember?.dOSFrom).getTime();\r\n    }\r\n    else if(!!this.claimFormData?.claimViewModel){\r\n       dosFrom = new Date(this.claimFormData?.claimViewModel?.claimDosfrom).getTime();\r\n    }\r\n   \r\n    for (const item of this.cptCodes) {\r\n\r\n      let add_date = (new Date(item.add_date)).getTime();\r\n      let term_date = (new Date(item.term_date)).getTime();\r\n      if ((add_date < dosFrom || add_date == dosFrom) && (term_date > dosFrom || term_date == dosFrom) && !(!!result.find(e => e.mdmCode == item.mdmCode))) {\r\n        result.push(item);\r\n      }\r\n    }\r\n    this.cPTCodes = result;\r\n    if(this.claimFormData.isEditClaim){\r\n      this.validateInvalidCPT();\r\n    }\r\n  }\r\n  cptCodes: AllCPTCode[];\r\n  fetchCPTData() {\r\n    let cpt = localStorage.getItem(LocalStorageKey.allCPT);\r\n    if (!!cpt) {\r\n      this.cptCodes = JSON.parse(JSLZString.decompress(cpt));\r\n      this.fetchCPT();\r\n    } else {\r\n      this.cptservice.fetchAllCpt().subscribe((res) => {\r\n        if (!!res) {\r\n          this.cptCodes = res;\r\n          this.fetchCPT();\r\n        }\r\n      })\r\n    }\r\n  }\r\n  setYesOrNo(data: string) {\r\n    if (data === '' || data === 'N' || data === 'No') {\r\n      return 'No';\r\n    }\r\n    return 'Yes';\r\n\r\n  }\r\n  patchServiceLineData() {\r\n    let count: number = 1;\r\n    this.claimFormData?.claimViewModel?.claimsProfessional837?.serviceLineProfessional837s.forEach((ele: ServiceLineProfessional837sModel) => {\r\n      let date = ele.dtp03ServiceDate;\r\n      let fromDate = date.substring(0, 4) + \"-\" + date.substring(4, 6) + \"-\" + date.substring(6, 8);\r\n      let toDate = fromDate;\r\n\r\n      if (date.substring(9, 13) && date.substring(13, 15) && date.substring(15, 17))\r\n        toDate = date.substring(9, 13) + \"-\" + date.substring(13, 15) + \"-\" + date.substring(15, 17);\r\n      const empGroup = this.serviceLineform.group({\r\n        dateServiceFrom: [{ value: fromDate, disabled: true }],\r\n        dateServiceTo: [{ value: toDate, disabled: true }],\r\n        locationOfService: [{ value: ele.sv105PlaceOfServiceCode, disabled: this.claimFormData.isViewClaim }, Validators.required],\r\n        emg: [{ value: this.setYesOrNo(ele.sv109EmergencyIndicator), disabled: this.claimFormData.isViewClaim }],\r\n        desc: [{ value: ele.sv10107ServiceDescription }],\r\n        cpt: new FormControl(ele.sv10102ProcedureCode, [Validators.required]),\r\n        m1: new FormControl(ele.sv10103ProcedureModifier1, modifierValidator),\r\n        m2: new FormControl(ele.sv10104ProcedureModifier2, modifierValidator),\r\n        m3: new FormControl(ele.sv10105ProcedureModifier3, modifierValidator),\r\n        m4: new FormControl(ele.sv10106ProcedureModifier4, modifierValidator),\r\n        diagnosispointer1: new FormControl(Number(ele.sv10701DiagnosisCodePointer1).toString(), [Validators.required]),\r\n        diagnosispointer2: new FormControl(ele.sv10702DiagnosisCodePointer2 ? Number(ele.sv10702DiagnosisCodePointer2).toString() : null),\r\n        diagnosispointer3: new FormControl(ele.sv10703DiagnosisCodePointer3 ? Number(ele.sv10703DiagnosisCodePointer3).toString() : null),\r\n        diagnosispointer4: new FormControl(ele.sv10704DiagnosisCodePointer4 ? Number(ele.sv10704DiagnosisCodePointer4).toString() : null),\r\n        unitCharges : [{ value: Number.parseFloat(ele.sv102LineItemChargeAmount).toFixed(2), disabled: this.claimFormData.isViewClaim }, Validators.required],\r\n        dayUnitChanges: [{ value: ele.sv104ServiceUnitCount, disabled: this.claimFormData.isViewClaim }, [Validators.required]],\r\n        total: [{ value: (Number.parseFloat(ele.sv104ServiceUnitCount) * Number.parseFloat(ele.sv102LineItemChargeAmount)).toFixed(2), disabled: true }],\r\n        ePSDT: [{ value: this.setYesOrNo(ele.sv111EpsdtIndicator), disabled: this.claimFormData.isViewClaim }],\r\n        jRenderingProviderId: \r\n        [{ value: this.claimFormData?.claimViewModel?.claimsProfessional837?.nm109RenderingProviderIdentifier, disabled: true}, Validators.maxLength(10), Validators.minLength(10) ],\r\n        proceduceC: ele.sv10101ProductServiceIdQualifier,\r\n        proceduceCount: count,\r\n\r\n        ndcUnitPrice: new FormControl(ele.cpt03NationalDrugUnitPrice, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\r\n        lineNote: ele.nte02LineNoteText,\r\n        ndcQtyQual: ele.ctp0501UnitMeasurementCode,\r\n        anesStart: ele.anesStart,\r\n        ndcQty: new FormControl(ele.ctp04NationalDrugUnitCount, [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\r\n        anesStop1: ele.anesStop,\r\n        anesStop2: [{ value: '', disabled: this.claimFormData.isViewClaim }],\r\n        anesStop3: [{ value: '', disabled: this.claimFormData.isViewClaim }],\r\n        ndcQual: ele.lin02NationalDrugCodeQlfr,\r\n        ndcCode: ele.lin03NationalDrugCode,\r\n      });\r\n      this.serviceLine.push(empGroup);\r\n      count = count + 1;\r\n    })\r\n    if (this.claimFormData.isEditClaim || this.claimFormData.isAddClaim ) {\r\n      for (let i = 0; i < this.serviceLine.controls.length; i++) {\r\n        this.ndcQualChange(i);\r\n        this.changeValuesToMarkValidation(i);\r\n\r\n      }\r\n      this.validateCPTCodeDOS();\r\n    }\r\n\r\n  }\r\n  \r\n  dosFromChangedEvent(index, field) {\r\n    this.dosFromChanged.emit();\r\n    this.checkDateValidations(index, field);\r\n    this.twoYearValidationForDate(index);\r\n\r\n  }\r\n  checkDateFromValidation(index, field) {\r\n    let fromDate = this.serviceLine.at(index).get(field).value;\r\n    if (!!fromDate && !!new Date(fromDate)) {\r\n      if (new Date(fromDate).getTime() < this.minDate.getTime()) {\r\n        this.serviceLine.at(index).get(field).setErrors({ 'invalidFromDate': true });\r\n      } else {\r\n        if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {\r\n          delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];\r\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n        }\r\n      }\r\n    } else {\r\n      if (this.serviceLine.at(index).get(field).hasError('invalidFromDate')) {\r\n        delete this.serviceLine.at(index).get(field).errors['invalidFromDate'];\r\n        this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n      }\r\n    }\r\n  }\r\n  twoYearValidationForDate(index) {\r\n    if (!!this.serviceLine.at(index).get('dateServiceFrom').value) {\r\n      let fromDate = new Date(this.serviceLine.at(index).get('dateServiceFrom').value);\r\n      const days = moment(this.todayDate).diff(moment(fromDate), 'days');\r\n      if (days > 730) {\r\n        Swal.fire({\r\n          title: 'Alert',\r\n          text:\r\n            \"The Date of Service is beyond 730 days (2 years). Do you want to continue?\",\r\n          icon: 'warning',\r\n          showCancelButton: false,\r\n          confirmButtonText: 'Yes',\r\n          showDenyButton: true,\r\n          denyButtonText: 'No',\r\n          reverseButtons: true,\r\n        }).then(async (result: any) => {\r\n          if (result.value == false) {\r\n            this.serviceLine.at(index).get('dateServiceFrom').setValue(null);\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n  checkDateValidations(index, field) {\r\n    let fromDate = this.serviceLine.at(index).get('dateServiceFrom').value;\r\n    let toDate = this.serviceLine.at(index).get('dateServiceTo').value;\r\n    if (!!fromDate && !!toDate && !!new Date(fromDate) && !!new Date(toDate)) {\r\n      if (new Date(fromDate).getTime() > new Date(toDate).getTime()) {\r\n        this.serviceLine.at(index).get(field).setErrors({ 'invalidDate': true });\r\n      } else {\r\n        if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {\r\n          delete this.serviceLine.at(index).get(field).errors['invalidDate'];\r\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n        }\r\n        if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {\r\n          delete this.serviceLine.at(index).get(field).errors['invalidDate'];\r\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n        }\r\n        if (this.serviceLine.at(index).get(field).hasError('required')) {\r\n          delete this.serviceLine.at(index).get(field).errors['required'];\r\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n        }\r\n      }\r\n    } else {\r\n      if (this.serviceLine.at(index).get(field).hasError('currentIllnessError')) {\r\n        if (this.serviceLine.at(index).get(field).hasError('invalidDate')) {\r\n          delete this.serviceLine.at(index).get(field).errors['invalidDate'];\r\n          this.serviceLine.at(index).get(field).updateValueAndValidity();\r\n        }\r\n      } else {\r\n        this.serviceLine.at(index).get(field).setErrors(null);\r\n      }\r\n      if (!(!!fromDate)) {\r\n        this.serviceLine.at(index).get('dateServiceFrom').setErrors({ 'required': true });\r\n        if (this.serviceLine.at(index).get('dateServiceTo').hasError('invalidDate')) {\r\n          delete this.serviceLine.at(index).get('dateServiceTo').errors['invalidDate'];\r\n          this.serviceLine.at(index).get('dateServiceTo').updateValueAndValidity();\r\n        }\r\n      }\r\n      if (!(!!toDate)) {\r\n        this.serviceLine.at(index).get('dateServiceTo').setErrors({ 'required': true });\r\n        if (this.serviceLine.at(index).get('dateServiceFrom').hasError('invalidDate')) {\r\n          delete this.serviceLine.at(index).get('dateServiceFrom').errors['invalidDate'];\r\n          this.serviceLine.at(index).get('dateServiceFrom').updateValueAndValidity();\r\n        }\r\n      }\r\n    }\r\n    this.checkDateFromValidation(index, field);\r\n    this.dateShow();\r\n  }\r\n  ndcCodeValidator(control: FormControl) {\r\n    let isValid = (!!control.value && control.value.trim().length == 11) || control.value == null || control.value == '';\r\n    if (!!control.value) {\r\n      isValid = isValid && String(control.value).match(/[0-9]/g).length == control.value.length;\r\n    }\r\n    return isValid ? null : { 'isInvalid': true };\r\n  }\r\n  ndcQualChange(index) {\r\n    if (!!this.ndcValidation(index).controls['ndcQual'].value && this.ndcValidation(index).controls['ndcQual'].value.toLowerCase() == 'n4') {\r\n      this.ndcValidation(index).controls['ndcCode'].setValidators(this.ndcCodeValidator);\r\n      if (!this.isNdcCodeValid(this.ndcValidation(index).controls['ndcCode'].value)) {\r\n        this.ndcValidation(index).controls['ndcCode'].setErrors({ 'isInvalid': true });\r\n      }\r\n    } else {\r\n      this.ndcValidation(index).controls['ndcCode'].removeValidators(this.ndcCodeValidator);\r\n      if (this.serviceLine.at(index).get('ndcCode').hasError('isInvalid')) {\r\n        delete this.serviceLine.at(index).get('ndcCode').errors['isInvalid'];\r\n      }\r\n    }\r\n    this.serviceLine.at(index).get('ndcCode').updateValueAndValidity();\r\n  }\r\n  isNdcCodeValid(value) {\r\n    let isValid = (!!value && value.trim().length == 11) || value == null || value == '';\r\n    if (!!value) {\r\n      isValid = isValid && String(value).match(/[0-9]/g).length == value.length;\r\n    }\r\n    return isValid;\r\n  }\r\n  addServiceShow: boolean = false;\r\n  dateServiceFrom: any = [];\r\n  dateServiceTo: any = [];\r\n  oldServiceDateFrom: string = '';\r\n  oldServiceDateTo: string = '';\r\n\r\n  servicelineEdit(e) {\r\n\r\n    if (e.target.checked) {\r\n      this.addServiceShow = true;\r\n      for (let item of this.serviceLine.controls) {\r\n        this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());\r\n        this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString());\r\n\r\n       // item.get('dateServiceFrom').enable({ onlySelf: true });\r\n       // item.get('dateServiceTo').enable({ onlySelf: true });\r\n\r\n      }\r\n      //submitValidateAllFields.validateDisableControl(this.serviceLineInfo, [\"dateServiceFrom\"]);\r\n    } else {\r\n      this.addServiceShow = false;\r\n      for (let item of this.serviceLine.controls) {\r\n       // item.get('dateServiceFrom').disable({ onlySelf: true });\r\n       // item.get('dateServiceTo').disable({ onlySelf: true });\r\n\r\n      }\r\n    }\r\n  }\r\n  dateShow() {\r\n    this.dateServiceFrom = []\r\n    this.dateServiceTo = [];\r\n    for (let item of this.serviceLine.controls) {\r\n      this.dateServiceFrom.push(new Date(item.get('dateServiceFrom').value).toString());\r\n      this.dateServiceTo.push(new Date(item.get('dateServiceTo').value).toString());\r\n\r\n\r\n    }\r\n    let min = this.dateServiceFrom[0]\r\n    let max = this.dateServiceFrom[0]\r\n    this.dateServiceFrom.forEach(function (v) {\r\n      max = new Date(v) > new Date(max) ? v : max;\r\n\r\n      min = new Date(v) < new Date(min) ? v : min;\r\n    });\r\n    this.oldServiceDateFrom = new Date(min).toString();\r\n    this.oldServiceDateTo = new Date(max).toString();\r\n  }\r\n  onSearchQualifer(term: string, item) {\r\n    term = term.toLocaleLowerCase();\r\n    return item['qualifier'].toLocaleLowerCase().indexOf(term) > -1 || item['description'].toLocaleLowerCase().indexOf(term) > -1;\r\n\r\n  }\r\n\r\n  validateCPTCode() {\r\n\r\n    for (let i = 0; i < this.serviceLine.length; i++) {\r\n\r\n      for (let k = 1; k <= 4; k++) {\r\n        if (this.serviceLine.at(i).get('diagnosispointer' + k).errors?.dublicate) {\r\n          this.serviceLine.at(i).get('diagnosispointer' + k).setErrors(null)\r\n        }\r\n      }\r\n    }\r\n    for (let i = 0; i < this.serviceLine.length; i++) {\r\n      for (let h = 1; h <= this.serviceLine.length - 1; h++) {\r\n        if (this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {\r\n          for (let j = 1; j <= 4; j++) {\r\n            for (let k = 1; k <= 4; k++) {\r\n              if (i != h && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(h).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('cpt').value === this.serviceLine.at(h).get('cpt').value) {\r\n                this.serviceLine.at(i).get('diagnosispointer' + j).setErrors({ dublicate: true })\r\n                this.serviceLine.at(h).get('diagnosispointer' + k).setErrors({ dublicate: true })\r\n                    this.serviceLine.at(i).get('diagnosispointer' +j).markAsTouched();\r\n               this.serviceLine.at(h).get('diagnosispointer' +k).markAsTouched();\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    for (let i = 0; i < this.serviceLine.length; i++) {\r\n      for (let j = 1; j <= 4; j++) {\r\n        for (let k = 1; k <= 4; k++) {\r\n          if (j != k && this.serviceLine.at(i).get('diagnosispointer' + j).value && this.serviceLine.at(i).get('diagnosispointer' + k).value && this.serviceLine.at(i).get('diagnosispointer' + j).value === this.serviceLine.at(i).get('diagnosispointer' + k).value) {\r\n            if (!this.serviceLine.at(i).get('diagnosispointer' + k).errors) {\r\n              this.serviceLine.at(i).get('diagnosispointer' + k).setErrors({ dublicate: true })\r\n              this.serviceLine.at(i).get('diagnosispointer' +i).markAsTouched();\r\n \r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  validateInvalidCPT() {\r\n    let countMatched = 0;\r\n    for (let index = 0; index < this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length; index++) {\r\n      if (this.cptCodes.filter(t => t.cpt == this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode).length > 0) {\r\n        countMatched = countMatched + 1;\r\n      }\r\n      else {\r\n        this.cptservice.fetchchCPTCodeSeach(this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[index].sv10102ProcedureCode, this.claimFormData.claimViewModel.claimDosfrom, '').subscribe((res: AllCPTCode[]) => {\r\n          countMatched = countMatched + 1;\r\n          res.forEach(element => {\r\n            this.cPTCodes.push(element);\r\n          });\r\n          this.patchValueMatched(countMatched);\r\n        })\r\n      }\r\n    }\r\n    this.patchValueMatched(countMatched);\r\n  }\r\n  patchValueMatched(countMatched: number) {\r\n    let totalCount = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length;\r\n    if (totalCount === countMatched) {\r\n      this.patchServiceLineData();\r\n    }\r\n  }\r\n\r\n  validateCPTCodeDOS() {\r\n\r\n    let cptData: ValidateCPTRequest[] = [];\r\n    for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {\r\n      let cptDosData: ValidateCPTRequest = { cpt: '', dos: '' };\r\n      let date = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].dtp03ServiceDate;\r\n      let fromDate = date.substring(6, 8) + \"/\" + date.substring(4, 6) + \"/\" + date.substring(0, 4);\r\n      cptDosData.cpt = this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s[i].sv10102ProcedureCode;\r\n      cptDosData.dos = fromDate;\r\n      cptData.push(cptDosData);\r\n    }\r\n\r\n\r\n    this.cptservice.CPTValidorNot(cptData).subscribe((res: AllCPTCode[]) => {\r\n      for (let i = 0; i <= this.claimFormData.claimViewModel.claimsProfessional837.serviceLineProfessional837s.length - 1; i++) {\r\n        if (res.filter(t => t.cpt === this.serviceLine.at(i).get('cpt').value).length === 0) {\r\n          this.serviceLine.at(i).get('cpt').setErrors({ invalidCpt: true })\r\n          res[i].cpt = this.serviceLine.at(i).get('cpt').value;\r\n          this.cPTCodes.push(res[i]);\r\n          this.invalidCptCode.push(this.serviceLine.at(i).get('cpt').value);\r\n        }\r\n      }\r\n      if (this.serviceLine.invalid) {\r\n        submitValidateAllFields.validateAllFields(this.serviceLineInfo);\r\n        return;\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  cptViewHistory() {\r\n    let request: any;\r\n    if (this.claimFormData.isEditClaim) {\r\n      if (this.serviceLine.controls.length > 0) {\r\n        if (!!this.serviceLine.controls[0].value.dateServiceFrom?._d) {\r\n          request = {\r\n            subscribeID: this.claimFormData.claimViewModel.subscribeId,\r\n            dos: this.serviceLine.controls[0].value.dateServiceFrom._d\r\n          };\r\n        }\r\n        else{\r\n          request ={\r\n            subscribeID: this.claimFormData.claimViewModel.subscribeId,\r\n            dos: this.serviceLine.controls[0].value.dateServiceFrom\r\n          }\r\n        }\r\n      }\r\n    }\r\n    else if(this.claimFormData.isAddClaim){\r\n      request = {\r\n        subscribeID: this.claimFormData.profileMember.subscriberID,\r\n        dos: this.claimFormData.profileMember.dOSFrom\r\n      };\r\n    }\r\n      this.cptservice.getCPTViewHistory(request).subscribe((resp: any) => {\r\n        let cptList: any = [];\r\n        if (resp.statusCode == 200 && (resp.content || []).length > 0) {\r\n          cptList = resp.content;\r\n          cptList = cptList.map(cpt => ({\r\n            ...cpt,\r\n            isSelected: false\r\n          }));\r\n        }\r\n        const existingServiceLines: any[] = this.serviceLine.value;\r\n        existingServiceLines?.forEach((serviceLine: any) => {\r\n          cptList.filter(cptItem => cptItem.cpt === serviceLine.cpt).forEach((cpt => {\r\n            cpt.isSelected = true;\r\n          }));\r\n        });\r\n        this.viewHistoryCPTList =cptList;\r\n        let dialogRef = this.dialog.open(CptViewHistoryComponent, {\r\n          height: '650px',\r\n          width: '1100px',\r\n          autoFocus: false,\r\n          restoreFocus: false,\r\n          maxHeight: '90vh',\r\n          panelClass: 'custom-dialog-containers',\r\n          data: {\r\n            cptList: cptList,\r\n          }\r\n        });\r\n        dialogRef.afterClosed().subscribe((data: any) => {\r\n          if (!!data && (data.selectedCPTCodes || []).length > 0) {\r\n            this.mapServiceLinesSelecteCPCodes(data.selectedCPTCodes)\r\n          }\r\n          // if selected cpts is zero then index 0 in existing service line cpt should be empty and remove remaining all.\r\n          else if (!!data && (data.selectedCPTCodes || []).length == 0) {\r\n            if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\r\n\r\n              const servicelineMatchedFromViewHistoryCPTCodes: any[] = this.serviceLine.value.filter(serviceLineItem =>\r\n                this.viewHistoryCPTList.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt));\r\n\r\n                if(!!servicelineMatchedFromViewHistoryCPTCodes && servicelineMatchedFromViewHistoryCPTCodes.length>0){\r\n\r\n                  servicelineMatchedFromViewHistoryCPTCodes.forEach(item =>{\r\n                    const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt);\r\n\r\n                    if(index ==0){\r\n                  let charges =  this.getUnitChargesByPayerId()\r\n                  let cptCharge = this.allCPTCharges.filter(item => item.code == item.cpt)[0]?.charge;\r\n                  if(!!cptCharge){\r\n                    charges = cptCharge;\r\n                  }\r\n                      this.serviceLine.at(index).patchValue({\r\n                        cpt: null,\r\n                        unitCharges:  Number.parseFloat(charges).toFixed(2)         \r\n                      }) \r\n                    }\r\n                    else{\r\n                      this.serviceLine.removeAt(index);\r\n                    }\r\n                  })\r\n                }\r\n              // this.serviceLine.controls[0].patchValue({\r\n              //   cpt: null,\r\n              //   dayUnitChanges: '0.00'\r\n              // });\r\n              // this.isShowDelete = false;\r\n              // while (this.serviceLine.controls.length > 1) {\r\n              //   this.serviceLine.removeAt(this.serviceLine.controls.length - 1);  // Remove last control\r\n              // }\r\n            }\r\n          }\r\n        });\r\n      })     \r\n  }\r\n\r\n  mapServiceLinesSelecteCPCodes(selectedCPTCodes: any[]) {\r\n    let getExistingFirstServiceLine: any;\r\n    if (!!this.serviceLine.controls && this.serviceLine.controls.length > 0) {\r\n      getExistingFirstServiceLine = this.serviceLine.controls[0];  // copy this values to auto fill to new adding service\r\n      /// filter  Unused  servicelines based on selected CPT and remove it from servicelines controls.\r\n      if (selectedCPTCodes.length > 0 && this.serviceLine.controls.length > 0) {\r\n        const servicelineNoMatchedFromSelectedCPTCodes: any[] = this.serviceLine.value.filter(serviceLineItem =>\r\n          !selectedCPTCodes.some(selecteItm => serviceLineItem.cpt === selecteItm.cpt));\r\n\r\n        // Remove unMatched values from serviceLines Controls.      \r\n        servicelineNoMatchedFromSelectedCPTCodes.forEach(item => {\r\n          if (!!item.cpt) {\r\n            const isViewHistoryItem = this.viewHistoryCPTList.find(cpt => cpt.cpt == item.cpt);\r\n            // Find the index of the control where the 'id' matches\r\n            const index = this.serviceLine.controls.findIndex(control => control.value.cpt === item.cpt);\r\n            // Remove the control if found\r\n            if (index !== -1 && !!isViewHistoryItem) {\r\n              let charges =  this.getUnitChargesByPayerId()\r\n              let cptCharge = this.allCPTCharges.filter(item => item.code == item.cptCode)[0]?.charge;\r\n              if(!!cptCharge){\r\n                charges = cptCharge;\r\n              }\r\n              if (index == 0) {\r\n                this.serviceLine.at(index).patchValue({\r\n                  cpt: null,\r\n                  unitCharges: Number.parseFloat(charges).toFixed(2)  \r\n                })\r\n              }\r\n              else {\r\n                this.serviceLine.removeAt(index);\r\n              }\r\n            }\r\n          }\r\n        });\r\n      }\r\n      let existingServiceLines = this.serviceLine.value;\r\n      // get selected cptCodes except from existingservice lines.\r\n      selectedCPTCodes = selectedCPTCodes.filter(selectedItem =>\r\n        !existingServiceLines.some(existingItem => selectedItem.cpt === existingItem.cpt));\r\n\r\n      // if existing service line is one with dates , selected cpt is one if both are different       \r\n       if (selectedCPTCodes.length == 1 && existingServiceLines.length == 1) {\r\n\r\n        // if existing cpt is null update existing row of cpt.\r\n        if (existingServiceLines.length == 1 && !existingServiceLines[0].cpt) {\r\n          this.serviceLine.controls[0].patchValue({\r\n            cpt: selectedCPTCodes[0].cpt,\r\n            desc:selectedCPTCodes[0].shortDescription\r\n          });\r\n          this.OnCPTCodeChange(selectedCPTCodes[0].cpt, 0)\r\n          selectedCPTCodes.shift(); // 0 index remove\r\n        }\r\n      }\r\n    }\r\n\r\n    // for new selected cpts addding to service lines.\r\n    selectedCPTCodes.forEach((cptCodeDetails: any) => {\r\n      this.patchSelecteCPTCodesFromHistory(cptCodeDetails, getExistingFirstServiceLine)\r\n    });\r\n  }\r\n\r\n  patchSelecteCPTCodesFromHistory(cptCodeDetails: any,getExistingFirstServiceLine:any) {\r\n    let locationOfService: any;\r\n    let topSelectedServiceLine: any;\r\n    if (!!getExistingFirstServiceLine) {\r\n      locationOfService = getExistingFirstServiceLine.value.locationOfService;\r\n      topSelectedServiceLine = getExistingFirstServiceLine.getRawValue();\r\n    }\r\n    if(!!cptCodeDetails.cpt){\r\n      // existing service line with empty cptCode patch values\r\n      const index = this.serviceLine.controls.findIndex(control => control.value.cpt === null);\r\n      if (index !== -1) {\r\n        this.serviceLine.at(index).patchValue({\r\n          cpt: cptCodeDetails.cpt,\r\n          desc:cptCodeDetails.shortDescription           \r\n        }) \r\n        this.OnCPTCodeChange(cptCodeDetails.cpt, index)\r\n    }\r\n    else{\r\n      let unitCharges = this.getUnitChargesByPayerId();\r\n      let  total = unitCharges;\r\n      let cptCharge = this.allCPTCharges.filter(item => item.code == cptCodeDetails.cpt)[0]?.charge;\r\n      if(!!cptCharge){\r\n           unitCharges = cptCharge;  \r\n           total = (cptCharge * 1).toFixed(2);  \r\n      }\r\n\r\n      const dOSFrom =    this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSFrom) : new Date(this.claimFormData?.claimViewModel.claimDosfrom);\r\n      const dOSTo = this.claimFormData?.isAddClaim ? new Date(this.claimFormData?.profileMember.dOSTo) : new Date(this.claimFormData?.claimViewModel.claimDosto);\r\n\r\n      const empGroup = this.serviceLineform.group({\r\n        dateServiceFrom: new FormControl({value:dOSFrom ,disabled: true}, [Validators.required]),\r\n        dateServiceTo: new FormControl({value:dOSTo,disabled: true}, [Validators.required]),\r\n        locationOfService: new FormControl(!!locationOfService ? locationOfService : null, Validators.required),\r\n        emg: new FormControl(null),\r\n        desc: new FormControl(cptCodeDetails.shortDescription),\r\n        cpt: new FormControl(cptCodeDetails.cpt, [Validators.required]),\r\n        m1: [{ value: '', disabled: false }, modifierValidator],\r\n        m2: [{ value: '', disabled: false }, modifierValidator],\r\n        m3: [{ value: '', disabled: false }, modifierValidator],\r\n        m4: [{ value: '', disabled: false }, modifierValidator],\r\n        diagnosispointer1: new FormControl('1', [Validators.required]),\r\n        diagnosispointer2: [{ value: '', disabled: false }],\r\n        diagnosispointer3: [{ value: '', disabled: false }],\r\n        diagnosispointer4: [{ value: '', disabled:false}],\r\n        charges: new FormControl(Number.parseFloat(unitCharges).toFixed(2), Validators.required),\r\n        dayUnitChanges: new FormControl('1', Validators.required),\r\n        unitCharges: new FormControl({ value: Number.parseFloat(unitCharges).toFixed(2), disabled: false }, [Validators.required]),\r\n        total: [{ value: total, disabled: true }],\r\n        ePSDT: [{ value: null, disabled: false }],\r\n        jRenderingProviderId: [{ value:  topSelectedServiceLine.jRenderingProviderId, disabled: true }, [Validators.maxLength(10), Validators.minLength(10)]],\r\n        proceduceC: '',\r\n        proceduceCount: this.serviceLine.length + 1,\r\n        ndcUnitPrice: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\r\n        lineNote: '',\r\n        ndcQtyQual: '',\r\n        anesStart: '',\r\n        ndcQty: new FormControl('', [numberWith3DecimalValidator, Validators.min(0), Validators.max(9999999.999)]),\r\n        anesStop1: '',\r\n        anesStop2: '',\r\n        anesStop3: '',\r\n        ndcQual: '',\r\n        ndcCode: '',\r\n      });\r\n  \r\n      this.serviceLine.push(empGroup);\r\n      this.calculateTotal.emit();\r\n      this.dateValidators();\r\n      if (this.serviceLine.invalid) {\r\n        submitValidateAllFields.validateAllFields(this.serviceLineInfo);\r\n        return;\r\n      }\r\n    }\r\n    }\r\n  }\r\n\r\n  getUnitChargesByPayerId(){\r\n    let charges =PriceCost.zeroPrice\r\n       if(!!this.claimFormData.claimViewModel){\r\n       charges = this.claimFormData.claimViewModel?.payerId === \"41212\" || this.claimFormData.claimViewModel?.payerId === \"20133\" || this.claimFormData.claimViewModel?.payerId === \"59354\" ?  PriceCost.zeroPrice  :  PriceCost.zeroPointZeroOnePrice\r\n        }\r\n       else if(this.claimFormData.isAddClaim){\r\n       charges = this.claimFormData.payerItem.payerId ===\"41212\"|| this.claimFormData.payerItem?.payerId === \"20133\" || this.claimFormData.payerItem?.payerId === \"59354\" ?  PriceCost.zeroPrice :  PriceCost.zeroPointZeroOnePrice\r\n       }\r\n       return charges\r\n  }\r\n}\r\n\r\n\r\n\r\n", "<form [formGroup]=\"serviceLineInfo\" class=\"service-line-dashboard\">\r\n    <div class=\"row\">\r\n        <div class=\"col-md-6\">\r\n            <p class=\"form-title\">24. Service Line</p>\r\n        </div>\r\n        <div class=\"col-md-2 radio-flex\">\r\n\r\n          <!--  <div class=\"form-check form-check-inline radio-flex\" *ngIf=\"claimFormData.isAddClaim\">\r\n                <input class=\"form-check-input\" type=\"checkbox\" (change)=\"servicelineEdit($event)\">\r\n                <label class=\"create-claim-radio-labels\" for=\"OutsideLabYes\">COPY DOS TO ALL SERVICE LINES</label>\r\n            </div>-->\r\n\r\n\r\n        </div>\r\n        <div class=\"col-md-4 radio-flex\">\r\n\r\n            <div class=\"form-check form-check-inline radio-flex\">\r\n                <input class=\"form-check-input\" type=\"radio\" formControlName=\"showNDC\" value=\"No\" (change)=\"hideNDC()\">\r\n                <label class=\"create-claim-radio-labels\" for=\"OutsideLabYes\">Hide NDC</label>\r\n            </div>\r\n            <div class=\"form-check form-check-inline radio-flex\">\r\n                <input class=\"form-check-input\" type=\"radio\" formControlName=\"showNDC\" value=\"Yes\" (change)=\"showNDC()\">\r\n                <label class=\"create-claim-radio-labels\" for=\"OutsideLabNo\">Show NDC</label>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"row mt-2\">\r\n        <div class=\"col-md\">\r\n            <section class=\"24\">\r\n                <div class=\"table-responsive\">\r\n                    <table class=\"table table-bordered\"\r\n                        [ngStyle]=\"{'width' : !claimFormData.isViewClaim ? 'max-content' : ''}\">\r\n                        <thead>\r\n                            <tr>\r\n                                <th style=\"font-size:12px;\" colspan=\"2\">A. Date(S) Of Service</th>\r\n                                <th style=\"font-size:12px;\" scope=\"col\" rowspan=\"2\">B. Place Of Service</th>\r\n                                <th style=\"font-size:12px;\" scope=\"col\" rowspan=\"2\">C. EMG</th>\r\n                                <th style=\"font-size:12px;\" colspan=\"2\">D. Procedures,Services Or Supplies</th>\r\n                                <th style=\"font-size:12px;\" scope=\"col\" rowspan=\"2\">E. Diagnosis Pointer</th>\r\n                                <th style=\"font-size:12px; max-width: 6rem !important;\" scope=\"col\" rowspan=\"2\">F. Unit\r\n                                    Charges ($)</th>\r\n                                <th style=\"font-size:12px; max-width: 6rem !important;\" scope=\"col\" rowspan=\"2\">G. Days\r\n                                    And Unit</th>\r\n                                <th style=\"font-size:12px; max-width: 6rem !important;\" scope=\"col\" rowspan=\"2\">Charges\r\n                                    ($)</th>\r\n                                <th style=\"font-size:12px;\" scope=\"col\" rowspan=\"2\">H. EPSDT</th>\r\n                                <th style=\"font-size:12px;\" scope=\"col\" rowspan=\"2\">I. ID Qual</th>\r\n                                <th style=\"font-size:12px; max-width: 9rem !important;\" scope=\"col\" rowspan=\"2\">J.\r\n                                    Rendering Provider ID</th>\r\n                                <th *ngIf=\"serviceLine.length>1 && claimFormData.isEditClaim\" style=\"font-size:12px;\"\r\n                                    scope=\"col\" rowspan=\"2\"></th>\r\n\r\n                            </tr>\r\n                            <tr>\r\n                                <th style=\"font-size:12px\" scope=\"col\">From</th>\r\n                                <th style=\"font-size:12px\" scope=\"col\">To</th>\r\n                                <th style=\"font-size:12px\" scope=\"col\">CPT/HCPCS\r\n                                    <a href=\"javascript:void(0);\" id=\"viewCptCodesHistory\"\r\n                                        *ngIf=\"(claimFormData.isEditClaim) || (claimFormData.isAddClaim)\"\r\n                                        class=\"view-history\" (click)=\"cptViewHistory()\" title=\"View CPT History\"><span\r\n                                            class=\"fa fa-history\"></span></a>\r\n                                </th>\r\n                                <th style=\"font-size:12px\" scope=\"col\">Modifier</th>\r\n                            </tr>\r\n\r\n                        </thead>\r\n                        <tbody style=\"border: inherit !important;\" formArrayName=\"serviceLines\">\r\n                            <span style=\"display: contents;\" *ngFor=\"let items of serviceLine.controls; let i = index\"\r\n                                [formGroupName]=\"i\">\r\n                                <tr *ngIf=\"f.showNDC.value==='Yes'\">\r\n                                    <td style=\"font-size:12px; vertical-align: middle;\"> <input\r\n                                            *ngIf=\"!claimFormData.isViewClaim\" class=\"form-control form-control-sm\"\r\n                                            placeholder=\"Procedure Code Type\" formControlName=\"proceduceC\">\r\n                                        <span class=\"form-control\"\r\n                                            *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['proceduceC'].value\r\n                                            && ndcValidation(i).controls['proceduceC'].value.length > 0 ?\r\n                                            ndcValidation(i).controls['proceduceC'].value : '-'}}</span>\r\n                                    </td>\r\n                                    <td style=\"font-size:12px; vertical-align: middle;\"> <input\r\n                                            *ngIf=\"!claimFormData.isViewClaim\" class=\"form-control form-control-sm\"\r\n                                            formControlName=\"proceduceCount\">\r\n                                        <span class=\"form-control\"\r\n                                            *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['proceduceCount'].value\r\n                                            && ndcValidation(i).controls['proceduceCount'].value.length > 0 ?\r\n                                            ndcValidation(i).controls['proceduceCount'].value : '-'}}</span>\r\n                                    </td>\r\n                                    <td style=\"font-size:12px;\" rowspan=\"2\" colspan=\"11\">\r\n                                        <div class=\"row\" style=\"margin: 0; margin-top: 0.5rem !important;\">\r\n                                            <div class=\"col-1\"\r\n                                                [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                                Line Note:\r\n                                            </div>\r\n                                            <div class=\"col-1 no-padding\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"lineNote\"\r\n                                                    placeholder=\"LINE NOTE\">\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['lineNote'].value\r\n                                                    && ndcValidation(i).controls['lineNote'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['lineNote'].value : '-'}}</span>\r\n                                            </div>\r\n\r\n                                            <div class=\"col-1\"\r\n                                                [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                                Anes Start:\r\n                                            </div>\r\n                                            <div class=\"col-1 no-padding\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"anesStart\"\r\n                                                    placeholder=\"Anes Start\">\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['anesStart'].value\r\n                                                    && ndcValidation(i).controls['anesStart'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['anesStart'].value : '-'}}</span>\r\n                                            </div>\r\n\r\n                                            <div class=\"col-1\"\r\n                                                [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                                Anes Stop:\r\n\r\n                                            </div>\r\n                                            <div class=\"col-1 no-padding\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"anesStop1\"\r\n                                                    placeholder=\"ANES STOP\">\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['anesStop1'].value\r\n                                                    && ndcValidation(i).controls['anesStop1'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['anesStop1'].value : '-'}}</span>\r\n                                            </div>\r\n                                            <!---->\r\n                                            <div class=\"col-1\"\r\n                                                [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                                NDC Qual:\r\n                                            </div>\r\n                                            <div class=\"col-3 no-padding\">\r\n\r\n                                                <ng-select *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" #assignto\r\n                                                    [items]=\"nDcQualifier\" appendTo=\"body\" placeholder=\"NDC Qualifer\"\r\n                                                    bindLabel=\"description\" bindValue=\"qualifier\"\r\n                                                    (change)=\"changeValuesToMarkValidation(i);ndcQualChange(i)\"\r\n                                                    [searchFn]=\"onSearchQualifer\"\r\n                                                    [ngClass]=\"{ 'is-invalid':  ndcValidation(i).controls['ndcQual'].invalid && ndcValidation(i).controls['ndcQual'].errors}\"\r\n                                                    formControlName=\"ndcQual\">\r\n                                                    <ng-template ng-option-tmp ng-label-tmp let-item=\"item\">\r\n                                                        {{item.qualifier}}-{{item.description}}\r\n                                                    </ng-template>\r\n                                                </ng-select>\r\n\r\n\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"ndcValidation(i).controls['ndcQual'].errors\">\r\n                                                    <div\r\n                                                        *ngIf=\"ndcValidation(i).controls['ndcQual']?.errors['required']\">\r\n                                                        NDC Qual is Required\r\n                                                    </div>\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['ndcQual'].value\r\n                                                    && ndcValidation(i).controls['ndcQual'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['ndcQual'].value : '-'}}</span>\r\n                                            </div>\r\n                                            <div class=\"col-1\"\r\n                                                [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                                NDC Code:\r\n                                            </div>\r\n                                            <div class=\"col-1\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\"\r\n                                                    [ngClass]=\"{ 'is-invalid':  ndcValidation(i).controls['ndcCode'].invalid && ndcValidation(i).controls['ndcCode'].errors}\"\r\n                                                    formControlName=\"ndcCode\" placeholder=\"NDC Code\"\r\n                                                    (change)=\"changeValuesToMarkValidation(i)\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"ndcValidation(i).controls['ndcCode'].errors\">\r\n                                                    <div\r\n                                                        *ngIf=\"ndcValidation(i).controls['ndcCode']?.errors['required']\">\r\n                                                        NDC Code is Required\r\n                                                    </div>\r\n                                                    <div\r\n                                                        *ngIf=\"!ndcValidation(i).controls['ndcCode']?.errors['required'] && ndcValidation(i).controls['ndcCode']?.errors['isInvalid']\">\r\n                                                        Invalid NDC code, it should be a 11 digit code\r\n                                                    </div>\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['ndcCode'].value\r\n                                                    && ndcValidation(i).controls['ndcCode'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['ndcCode'].value : '-'}}</span>\r\n                                            </div>\r\n\r\n                                        </div>\r\n                                        <div class=\"row\" style=\"margin: 0; margin-top: 0.5rem !important;\">\r\n                                            <div class=\"col-1\"\r\n                                                [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                                NDC Qty:\r\n                                            </div>\r\n                                            <div class=\"col-1 no-padding\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"ndcQty\"\r\n                                                    numbersThreeDecimalOnly (change)=\"changeValuesToMarkValidation(i)\"\r\n                                                    [ngClass]=\"{ 'is-invalid':  ndcValidation(i).controls['ndcQty'].invalid && ndcValidation(i).controls['ndcQty'].errors}\"\r\n                                                    placeholder=\"NDC Qty\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"ndcValidation(i).controls['ndcQty'].errors\">\r\n                                                    <div\r\n                                                        *ngIf=\"ndcValidation(i).controls['ndcQty']?.errors['required']\">\r\n                                                        NDC Quantity is Required\r\n                                                    </div>\r\n                                                    <div\r\n                                                        *ngIf=\"items.get('ndcQty')?.invalid && items.get('ndcQty').errors.isInvalid && !items.get('ndcQty').errors.required\">\r\n                                                        Please enter valid NDC Quantity. Only numbers and three decimals\r\n                                                        accepted.\r\n                                                    </div>\r\n                                                    <div\r\n                                                        *ngIf=\"items.get('ndcQty')?.invalid && !items.get('ndcQty').errors.isInvalid && !items.get('ndcQty').errors.required && (items.get('ndcQty').errors.max || items.get('ndcQty').errors.min)\">\r\n                                                        The field NDC Quantity must be between 0 and 9999999.999.\r\n                                                    </div>\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['ndcQty'].value\r\n                                                    && ndcValidation(i).controls['ndcQty'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['ndcQty'].value : '-'}}</span>\r\n                                            </div>\r\n                                            <div class=\"col-1\"\r\n                                                [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                                NDC Qty Qual:\r\n                                            </div>\r\n                                            <div class=\"col-1 no-padding\">\r\n                                                <ng-select *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" #assignto\r\n                                                    [items]=\"nDcQualifierQty\" appendTo=\"body\" placeholder=\"NDC Qty Qual\"\r\n                                                    (change)=\"changeQual($event ,i)\" bindLabel=\"description\"\r\n                                                    (clear)=\"clear(i)\" bindValue=\"qualifier\"\r\n                                                    formControlName=\"ndcQtyQual\" [searchFn]=\"onSearchQualifer\"\r\n                                                    [ngClass]=\"{ 'is-invalid':  ndcValidation(i).controls['ndcQtyQual'].invalid && ndcValidation(i).controls['ndcQtyQual'].errors}\">\r\n                                                    <ng-template ng-option-tmp ng-label-tmp let-item=\"item\">\r\n                                                        {{item.qualifier}}-{{item.description}}\r\n                                                    </ng-template>\r\n                                                </ng-select>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"ndcValidation(i).controls['ndcQtyQual'].errors\">\r\n                                                    <div\r\n                                                        *ngIf=\"ndcValidation(i).controls['ndcQtyQual']?.errors['required']\">\r\n                                                        NDC Qty Qual is Required\r\n                                                    </div>\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['ndcQtyQual'].value\r\n                                                    && ndcValidation(i).controls['ndcQtyQual'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['ndcQtyQual'].value : '-'}}</span>\r\n                                            </div>\r\n\r\n                                            <div class=\"col-1\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"anesStop2\">\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['anesStop2'].value\r\n                                                    && ndcValidation(i).controls['anesStop2'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['anesStop2'].value : '-'}}</span>\r\n                                            </div>\r\n                                            <div class=\"col-1 no-padding\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"anesStop3\">\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['anesStop3'].value\r\n                                                    && ndcValidation(i).controls['anesStop3'].value.length > 0 ?\r\n                                                    ndcValidation(i).controls['anesStop3'].value : '-'}}</span>\r\n                                            </div>\r\n\r\n                                            <div class=\"col-1\">\r\n\r\n                                            </div>\r\n                                            <div class=\"col-1\">\r\n\r\n                                            </div>\r\n\r\n                                            <div class=\"col-1\">\r\n\r\n                                            </div>\r\n                                            <div class=\"col-1\">\r\n\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                                <tr *ngIf=\"f.showNDC.value==='Yes'\">\r\n                                    <td style=\"font-size:12px;\"\r\n                                        [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">NDC Unit\r\n                                        Price:</td>\r\n                                    <td style=\"font-size:12px;\"> <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                            class=\"form-control form-control-sm\" numbersThreeDecimalOnly\r\n                                            formControlName=\"ndcUnitPrice\" placeholder=\"NDC Unit Price \"\r\n                                            [ngClass]=\"{ 'is-invalid': items.get('ndcUnitPrice')?.invalid && (items.get('ndcUnitPrice').dirty || items.get('ndcUnitPrice').touched) && (items.get('ndcUnitPrice')?.errors)}\">\r\n                                        <div class=\"invalid-feedback\"\r\n                                            *ngIf=\"items.get('ndcUnitPrice')?.invalid && items.get('ndcUnitPrice').errors.isInvalid\">\r\n                                            Please enter valid NDC Unit Price. Only numbers and three decimals accepted.\r\n                                        </div>\r\n                                        <div class=\"invalid-feedback\"\r\n                                            *ngIf=\"items.get('ndcUnitPrice')?.invalid && !items.get('ndcUnitPrice').errors.isInvalid && (items.get('ndcUnitPrice').errors.max || items.get('ndcUnitPrice').errors.min)\">\r\n                                            The field NDC Unit Price must be between 0 and 9999999.999.\r\n                                        </div>\r\n                                        <span class=\"form-control\"\r\n                                            *ngIf=\"claimFormData.isViewClaim\">{{!!ndcValidation(i).controls['ndcUnitPrice'].value\r\n                                            && ndcValidation(i).controls['ndcUnitPrice'].value.length > 0 ?\r\n                                            ndcValidation(i).controls['ndcUnitPrice'].value : '-'}}</span>\r\n                                    </td>\r\n                                </tr>\r\n                                <tr>\r\n                                    <td>\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\" matInput\r\n                                                    class=\"form-control form-control-sm\" [min]=\"minDate\"\r\n                                                    [max]=\"todayDate\" [matDatepicker]=\"datepickerFrom\"\r\n                                                    formControlName=\"dateServiceFrom\" (click)=\"datepickerFrom.open()\"\r\n                                                    placeholder=\"MM/DD/YYYY\"\r\n                                                    (blur)=\"dosFromChangedEvent(i, 'dateServiceFrom')\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('dateServiceFrom')?.invalid && (items.get('dateServiceFrom').dirty || items.get('dateServiceFrom').touched) && (items.get('dateServiceFrom')?.errors)   }\">\r\n                                                <!-- <mat-datepicker-toggle matSuffix [for]=\"datepicker\"></mat-datepicker-toggle> -->\r\n                                                <mat-datepicker #datepickerFrom></mat-datepicker>\r\n                                                <!-- <input class=\"form-control form-control-sm\"\r\n                                                    formControlName=\"dateServiceFrom\"\r\n                                                    [type]=\"(!!claimFormData.isViewClaim && claimFormData.isViewClaim && !(!!items.get('dateServiceFrom').value))  ?  'text' : 'date'\"\r\n                                                    (blur)=\"dosFromChangedEvent(i, 'dateServiceFrom')\"\r\n                                                    max=\"{{todayDate | date:'yyyy-MM-dd'}}\"\r\n                                                    min=\"{{minDate | date: 'yyyy-MM-dd'}}\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('dateServiceFrom')?.invalid && (items.get('dateServiceFrom').dirty || items.get('dateServiceFrom').touched) && (items.get('dateServiceFrom')?.errors)   }\"> -->\r\n\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dateServiceFrom')?.invalid && items.get('dateServiceFrom').errors.required\">\r\n                                                    Please select date of service from.\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dateServiceFrom')?.invalid && items.get('dateServiceFrom').errors.invalidDate && !items.get('dateServiceFrom').errors.required\">\r\n                                                    From date Should be less than To Date.\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dateServiceFrom')?.invalid && items.get('dateServiceFrom').errors.currentIllnessError && !items.get('dateServiceFrom').errors.invalidDate && !items.get('dateServiceFrom').errors.required\">\r\n                                                    DOS From should be greater than or equal to Date of Current Illness.\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dateServiceFrom')?.invalid && !items.get('dateServiceFrom').errors.currentIllnessError && !items.get('dateServiceFrom').errors.invalidDate && !items.get('dateServiceFrom').errors.required && items.get('dateServiceFrom').errors.invalidFromDate\">\r\n                                                    Select a date greater than or equal to 10-01-2015.\r\n                                                </div>\r\n                                                <span class=\"form-control\" *ngIf=\"claimFormData.isViewClaim\">\r\n                                                    {{!!items.get('dateServiceFrom').value &&\r\n                                                    items.get('dateServiceFrom').value.length > 0 ?\r\n                                                    (items.get('dateServiceFrom').value | date: 'MM/dd/yyyy') :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td>\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\" matInput\r\n                                                    class=\"form-control form-control-sm\" [min]=\"minDate\"\r\n                                                    [max]=\"todayDate\" [matDatepicker]=\"datepickerTo\"\r\n                                                    formControlName=\"dateServiceTo\" (click)=\"datepickerTo.open()\"\r\n                                                    placeholder=\"MM/DD/YYYY\"\r\n                                                    (blur)=\"checkDateValidations(i, 'dateServiceTo')\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('dateServiceTo')?.invalid && (items.get('dateServiceTo').dirty || items.get('dateServiceTo').touched) && (items.get('dateServiceTo')?.errors)   }\">\r\n                                                <!-- <mat-datepicker-toggle matSuffix [for]=\"datepicker\"></mat-datepicker-toggle> -->\r\n                                                <mat-datepicker #datepickerTo></mat-datepicker>\r\n                                                <!-- <input class=\"form-control form-control-sm\"\r\n                                                    [type]=\"!!claimFormData.isViewClaim && claimFormData.isViewClaim && !(!!items.get('dateServiceTo').value) ? 'text' : 'date'\"\r\n                                                    formControlName=\"dateServiceTo\"\r\n                                                    (blur)=\"checkDateValidations(i, 'dateServiceTo')\"\r\n                                                    max=\"{{todayDate | date:'yyyy-MM-dd'}}\"\r\n                                                    min=\"{{minDate | date: 'yyyy-MM-dd'}}\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('dateServiceTo')?.invalid && (items.get('dateServiceTo').dirty || items.get('dateServiceTo').touched) && (items.get('dateServiceTo')?.errors)   }\"> -->\r\n\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dateServiceTo')?.invalid && items.get('dateServiceTo').errors.required\">\r\n                                                    Please select date of service to.\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dateServiceTo')?.invalid && items.get('dateServiceTo').errors.invalidDate && !items.get('dateServiceTo').errors.required\">\r\n                                                    To date Should be greater than From Date.\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dateServiceTo')?.invalid && !items.get('dateServiceTo').errors.invalidDate && !items.get('dateServiceTo').errors.required && items.get('dateServiceTo').errors.invalidFromDate\">\r\n                                                    Select a date greater than or equal to 10-01-2015.\r\n                                                </div>\r\n                                                <span class=\"form-control\" *ngIf=\"claimFormData.isViewClaim\">\r\n                                                    {{!!items.get('dateServiceTo').value &&\r\n                                                    items.get('dateServiceTo').value.length > 0 ?\r\n                                                    (items.get('dateServiceTo').value | date: 'MM/dd/yyyy') :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"vertical-align: top;\">\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <ng-select *ngIf=\"claimFormData.isEditClaim || claimFormData.isAddClaim\"\r\n                                                    (change)=\"placeOfServiceChanges($event)\"\r\n                                                    class=\"form-control form-control-sm wdt-100\"\r\n                                                    placeholder=\"Location Of Service\" appendTo=\"body\" bindLabel=\"text\"\r\n                                                    bindValue=\"value\" formControlName=\"locationOfService\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('locationOfService')?.invalid && (items.get('locationOfService').dirty || items.get('locationOfService').touched) && (items.get('locationOfService')?.errors?.required)   }\">\r\n                                                    <ng-option *ngFor=\"let item of allPlaceOfServices\" [value]=\"item\">\r\n                                                        {{item.text | uppercase}}\r\n                                                    </ng-option>\r\n                                                </ng-select>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('locationOfService')?.invalid && items.get('locationOfService').errors.required\">\r\n                                                    Place of Service is Required\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('locationOfService').value\r\n                                                    && items.get('locationOfService').value.length > 0 ?\r\n                                                    items.get('locationOfService').value : '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td>\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\"> <ng-select *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" placeholder=\"EMG\"\r\n                                                    appendTo=\"body\" formControlName=\"emg\">\r\n                                                    <ng-option *ngFor=\"let item of cmg\" [value]=\"item\">\r\n                                                        {{item | uppercase}}\r\n                                                    </ng-option>\r\n                                                </ng-select>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('emg').value &&\r\n                                                    items.get('emg').value.length > 0 ? items.get('emg').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"vertical-align: top;\">\r\n\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <ng-select\r\n                                                    *ngIf=\"claimFormData.isEditClaim  || claimFormData.isAddClaim\"\r\n                                                    class=\"form-control form-control-sm wdt-100\" [virtualScroll]=\"true\"\r\n                                                    placeholder=\"CPT  Type 3 letter\" appendTo=\"body\"\r\n                                                    (ngModelChange)=\"OnCPTCodeChange($event,i)\"\r\n                                                    bindLabel=\"shortDescription\" bindValue=\"cpt\" formControlName=\"cpt\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('cpt')?.invalid && (items.get('cpt').dirty || items.get('cpt').touched) && (items.get('cpt')?.errors?.required || items.get('cpt')?.errors?.invalidCptlength|| items.get('cpt')?.errors?.invalidCpt )   }\">\r\n                                                    <ng-option *ngFor=\"let item of cPTCodes\" [value]=\"item\">\r\n                                                        {{item.cpt}} - {{item.shortDescription}}\r\n                                                    </ng-option>\r\n                                                </ng-select>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('cpt')?.invalid && items.get('cpt').errors.required\">\r\n                                                    Code is Required\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('cpt')?.invalid && items.get('cpt').errors.invalidCptlength\">\r\n                                                    Invalid CPT code entered. Please provide a valid CPT code for\r\n                                                    accurate processing.\r\n                                                </div>\r\n\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('cpt')?.invalid && items.get('cpt').errors.invalidCpt\">\r\n                                                    Please Give Valid CPT Code\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('cpt').value &&\r\n                                                    items.get('cpt').value.length > 0 ? items.get('cpt').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"max-width: 20rem !important;\">\r\n                                        <div class=\"table-flex m-2\">\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"m1\"\r\n                                                    numbersOnly type=\"text\" [disabled]=\"isControl\" name=\"\"\r\n                                                    id=\"Modifier1{{i}}\" placeholder=\"M1\" maxlength=\"2\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('m1')?.invalid && (items.get('m1').dirty || items.get('m1').touched) && (items.get('m1')?.errors)}\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('m1')?.invalid && items.get('m1').errors\">\r\n                                                    Modifier1 must be of 2 characters in length\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('m1').value &&\r\n                                                    items.get('m1').value.length > 0 ? items.get('m1').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"m2\"\r\n                                                    numbersOnly type=\"text\" [disabled]=\"isControl\" name=\"\" maxlength=\"2\"\r\n                                                    id=\"Modifier2{{i}}\" placeholder=\"M2\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('m2')?.invalid && (items.get('m2').dirty || items.get('m2').touched) && (items.get('m2')?.errors)}\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('m2')?.invalid && items.get('m2').errors\">\r\n                                                    Modifier2 must be of 2 characters in length\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('m2').value &&\r\n                                                    items.get('m2').value.length > 0 ? items.get('m2').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"m3\"\r\n                                                    numbersOnly type=\"text\" [disabled]=\"isControl\" name=\"\" maxlength=\"2\"\r\n                                                    id=\"Modifier3{{i}}\" placeholder=\"M3\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('m3')?.invalid && (items.get('m3').dirty || items.get('m3').touched) && (items.get('m3')?.errors)}\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('m3')?.invalid && items.get('m3').errors\">\r\n                                                    Modifier3 must be of 2 characters in length\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('m3').value &&\r\n                                                    items.get('m3').value.length > 0 ? items.get('m3').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" formControlName=\"m4\"\r\n                                                    numbersOnly type=\"text\" [disabled]=\"isControl\" name=\"\" maxlength=\"2\"\r\n                                                    id=\"Modifier4{{i}}\" placeholder=\"M4\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('m4')?.invalid && (items.get('m4').dirty || items.get('m4').touched) && (items.get('m4')?.errors)}\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('m4')?.invalid && items.get('m4').errors\">\r\n                                                    Modifier4 must be of 2 characters in length\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('m4').value &&\r\n                                                    items.get('m4').value.length > 0 ? items.get('m4').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"max-width: 20rem !important;\">\r\n                                        <div class=\"table-flex mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\"\r\n                                                    formControlName=\"diagnosispointer1\" numbersOnly type=\"text\"\r\n                                                    [disabled]=\"isControl\" placeholder=\"D1\" [ngClass]=\"{ 'is-invalid': items.get('diagnosispointer1')?.invalid && (items.get('diagnosispointer1')?.dirty \r\n                                || items.get('diagnosispointer1')?.touched) }\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer1')?.hasError('required')\">\r\n                                                    Please enter Diagnosis Pointer\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer1')?.hasError('numberMax')\">\r\n                                                    Please enter valid Diagnosis Pointer\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer1')?.hasError('dublicate')\">\r\n                                                    Dupication Of Diagnosis Pointers is not allowed\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer1')?.hasError('continue')\">\r\n                                                    Please Maintain Continuity while entering Diagnosis Pointer\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('diagnosispointer1').value\r\n                                                    && items.get('diagnosispointer1').value.length > 0 ?\r\n                                                    items.get('diagnosispointer1').value : '-'}}</span>\r\n                                            </div>\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\"\r\n                                                    formControlName=\"diagnosispointer2\" numbersOnly type=\"text\"\r\n                                                    placeholder=\"D2\" [ngClass]=\"{ 'is-invalid': items.get('diagnosispointer2')?.invalid && (items.get('diagnosispointer2')?.dirty \r\n                                                    || items.get('diagnosispointer2')?.touched) }\">\r\n                                                <!-- <div class=\"invalid-feedback\" *ngIf=\"items.get('diagnosispointer2')?.invalid && (items.get('diagnosispointer2')?.dirty \r\n                                                    || items.get('diagnosispointer2')?.touched)\">-->\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer2')?.hasError('numberMax')\">\r\n                                                    Please enter valid Diagnosis Pointer\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer2')?.hasError('dublicate')\">\r\n                                                    Dupication Of Diagnosis Pointers is not allowed\r\n                                                </div>\r\n\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer2')?.hasError('continue')\">\r\n                                                    Please Maintain Continuity while entering Diagnosis Pointer\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('diagnosispointer2').value\r\n                                                    && items.get('diagnosispointer2').value.length > 0 ?\r\n                                                    items.get('diagnosispointer2').value : '-'}}</span>\r\n                                                <!--   </div>-->\r\n                                            </div>\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\"\r\n                                                    formControlName=\"diagnosispointer3\" numbersOnly type=\"text\"\r\n                                                    placeholder=\"D3\" [ngClass]=\"{ 'is-invalid': items.get('diagnosispointer3')?.invalid && (items.get('diagnosispointer3')?.dirty \r\n                                                    || items.get('diagnosispointer3')?.touched) }\">\r\n                                                <!-- <div class=\"invalid-feedback\" *ngIf=\"items.get('diagnosispointer2')?.invalid && (items.get('diagnosispointer2')?.dirty \r\n                                                <div class=\"invalid-feedback\" *ngIf=\"items.get('diagnosispointer3')?.invalid || (items.get('diagnosispointer3')?.dirty \r\n                                                    || items.get('diagnosispointer3')?.touched)\">\r\n                                                *ngIf=\"items.get('diagnosispointer3')?.hasError('numberMax')\"-->\r\n\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer3')?.hasError('numberMax')\">\r\n                                                    Please enter valid Diagnosis Pointer\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer3')?.hasError('dublicate')\">\r\n                                                    Dupication Of Diagnosis Pointers is not allowed\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer3')?.hasError('continue')\">\r\n                                                    Please Maintain Continuity while entering Diagnosis Pointer\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('diagnosispointer3').value\r\n                                                    && items.get('diagnosispointer3').value.length > 0 ?\r\n                                                    items.get('diagnosispointer3').value : '-'}}</span>\r\n                                                <!-- </div>-->\r\n                                            </div>\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\"\r\n                                                    formControlName=\"diagnosispointer4\" numbersOnly type=\"text\"\r\n                                                    placeholder=\"D4\" [ngClass]=\"{ 'is-invalid': items.get('diagnosispointer4')?.invalid && (items.get('diagnosispointer4')?.dirty \r\n                                                    || items.get('diagnosispointer4')?.touched) }\">\r\n                                                <!--  <div class=\"invalid-feedback\" *ngIf=\"items.get('diagnosispointer4')?.invalid && (items.get('diagnosispointer4')?.dirty \r\n                                                    || items.get('diagnosispointer4')?.touched)\">-->\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer4')?.hasError('numberMax')\">\r\n                                                    Please enter valid Diagnosis Pointer\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer4')?.hasError('dublicate')\">\r\n                                                    Dupication Of Diagnosis Pointers is not allowed\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('diagnosispointer4')?.hasError('continue')\">\r\n                                                    Please Maintain Continuity while entering Diagnosis Pointer\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('diagnosispointer4').value\r\n                                                    && items.get('diagnosispointer4').value.length > 0 ?\r\n                                                    items.get('diagnosispointer4').value : '-'}}</span>\r\n                                                <!--    </div>-->\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"max-width: 6rem !important;\">\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" type=\"text\"\r\n                                                    numbersTwoDecimalOnly formControlName=\"unitCharges\"\r\n                                                    (blur)=\"calculateTotalAmount($event,i)\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('unitCharges')?.invalid && (items.get('unitCharges')?.dirty \r\n                                                    || items.get('unitCharges')?.touched) && items.get('unitCharges')?.errors}\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('unitCharges')?.invalid && items.get('unitCharges').errors.required\">\r\n                                                    The unit charges field is required.\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('unitCharges').value\r\n                                                    && items.get('unitCharges').value.length > 0 ?\r\n                                                    items.get('unitCharges').value : '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"max-width: 6rem !important;\">\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" type=\"text\" numbersOnly\r\n                                                    formControlName=\"dayUnitChanges\"\r\n                                                    (blur)=\"calculateTotalAmount($event,i)\"\r\n                                                    [ngClass]=\"{ 'is-invalid': items.get('dayUnitChanges')?.invalid && (items.get('dayUnitChanges')?.dirty \r\n                                                    || items.get('dayUnitChanges')?.touched) && items.get('dayUnitChanges')?.errors}\">\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dayUnitChanges')?.invalid && items.get('dayUnitChanges').errors.required\">\r\n                                                    The days and units field is required.\r\n                                                </div>\r\n                                                <div class=\"invalid-feedback\"\r\n                                                    *ngIf=\"items.get('dayUnitChanges')?.invalid && (items.get('dayUnitChanges').errors.max || items.get('dayUnitChanges').errors.min)\">\r\n                                                    The field days and units must be between 1 and 99999999.\r\n                                                </div>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('dayUnitChanges').value\r\n                                                    && items.get('dayUnitChanges').value.length > 0 ?\r\n                                                    items.get('dayUnitChanges').value : '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"max-width: 6rem !important;\">\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" type=\"text\" placeholder=\"\"\r\n                                                    formControlName=\"total\">\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('total').value &&\r\n                                                    items.get('total').value.length > 0 ? items.get('total').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td>\r\n                                        <div class=\"row mt-2\">\r\n                                            <div class=\"col\">\r\n                                                <ng-select *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                    class=\"form-control form-control-sm\" placeholder=\"EPSDT\"\r\n                                                    appendTo=\"body\" formControlName=\"ePSDT\">\r\n                                                    <ng-option *ngFor=\"let item of epsdt\" [value]=\"item\">\r\n                                                        {{item | uppercase}}\r\n                                                    </ng-option>\r\n                                                </ng-select>\r\n                                                <span class=\"form-control\"\r\n                                                    *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('ePSDT').value &&\r\n                                                    items.get('ePSDT').value.length > 0 ? items.get('ePSDT').value :\r\n                                                    '-'}}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td>\r\n                                        <div class=\"d-flex mt-2\" style=\"font-size:12px\"\r\n                                            [ngClass]=\"{'service-line-view-label' : claimFormData.isViewClaim}\">\r\n                                            NPI\r\n                                        </div>\r\n                                    </td>\r\n                                    <td style=\"max-width: 9rem !important;\">\r\n                                        <div class=\"mt-2\">\r\n\r\n                                            <input *ngIf=\"!claimFormData.isViewClaim\"\r\n                                                class=\"form-control form-control-sm\" type=\"text\" placeholder=\"\"\r\n                                                maxlength=\"10\" required formControlName=\"jRenderingProviderId\"\r\n                                                [ngClass]=\"{ 'is-invalid': items.get('jRenderingProviderId')?.invalid && items.get('jRenderingProviderId')?.errors }\"\r\n                                                (input)=\"renderingProvider($event)\">\r\n\r\n\r\n                                            <div class=\"invalid-feedback\"\r\n                                                *ngIf=\"items.get('jRenderingProviderId')?.invalid\">\r\n                                                <div *ngIf=\"items.get('jRenderingProviderId')?.hasError('required')\">\r\n                                                    Please enter Rendering Provider NPI\r\n                                                </div>\r\n\r\n                                                <div\r\n                                                    *ngIf=\"items.get('jRenderingProviderId')?.hasError('maxlength') || items.get('jRenderingProviderId')?.hasError('minlength')\">\r\n                                                    Rendering Provider NPI must be of 10 characters in length.\r\n                                                </div>\r\n                                            </div>\r\n                                            <span class=\"form-control\"\r\n                                                *ngIf=\"claimFormData.isViewClaim\">{{!!items.get('jRenderingProviderId').value\r\n                                                && items.get('jRenderingProviderId').value.length > 0 ?\r\n                                                items.get('jRenderingProviderId').value : '-'}}</span>\r\n                                        </div>\r\n                                    </td>\r\n                                    <td *ngIf=\"claimFormData.isEditClaim || claimFormData.isAddClaim\">\r\n                                        <div class=\"d-flex mt-2\">\r\n                                            <button *ngIf=\"serviceLine.length > 1\" type=\"button\"\r\n                                                (click)=\"removeService(i)\" matTooltip=\"Remove\"\r\n                                                matTooltipPosition=\"above\" class=\"remove-button\">X</button>\r\n                                        </div>\r\n                                    </td>\r\n                                </tr>\r\n                            </span>\r\n                        </tbody>\r\n                    </table>\r\n                    <button type=\"button\" class=\"btn-primary primary-btn btn-height\" (click)=\"addnewService()\"\r\n                        *ngIf=\"claimFormData.isEditClaim || claimFormData.isAddClaim\"><i\r\n                            class=\"material-icons icon-margin icon-height icon-align\">playlist_add</i>Add\r\n                        Service</button>\r\n                </div>\r\n            </section>\r\n        </div>\r\n    </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module"}